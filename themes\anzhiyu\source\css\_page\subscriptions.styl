// 订阅服务页面样式
.goodsubscriptions-title
  margin: 1rem 0
  line-height: 1;

.subscriptions-item
  .subscriptions-item-content
    display: flex
    flex-direction: row
    flex-wrap: wrap
    margin: 0 -8px

    .subscriptions-item-content-item
      width: calc(25% - 12px)
      border-radius: 12px
      border: var(--style-border-always)
      overflow: hidden
      margin: 8px 6px
      background: var(--anzhiyu-card-bg)
      box-shadow: var(--anzhiyu-shadow-border)
      min-height: 400px
      position: relative

      +maxWidth1200()
        width: calc(50% - 12px)
      +maxWidth768()
        width: 100%

      .subscriptions-item-content-item-info
        padding: 8px 16px 16px 16px
        margin-top: 12px

        .subscriptions-item-content-item-name
          font-size: 1.1rem
          font-weight: bold
          margin-bottom: 8px
          line-height: 1.2
          cursor: pointer

          &:hover
            color: var(--anzhiyu-main-op)

        .subscriptions-item-content-item-specification
          font-size: 0.9rem
          color: var(--anzhiyu-secondtext)
          margin-bottom: 8px

        .subscriptions-item-content-item-note
          font-size: 0.9rem
          line-height: 1.4
          color: var(--anzhiyu-fontcolor)
          margin-bottom: 8px
          display: -webkit-box
          -webkit-line-clamp: 4
          -webkit-box-orient: vertical
          overflow: hidden
          text-overflow: ellipsis

        .subscriptions-item-content-item-description
          font-size: 0.9rem
          line-height: 1.4
          color: var(--anzhiyu-fontcolor)
          margin-bottom: 40px
          display: -webkit-box
          -webkit-line-clamp: 4
          -webkit-box-orient: vertical
          overflow: hidden
          text-overflow: ellipsis

      .subscriptions-item-content-item-cover
        height: 200px
        overflow: hidden
        position: relative

        .flip-card-inner
          position: relative
          width: 100%
          height: 100%
          text-align: center
          transition: transform 0.6s
          transform-style: preserve-3d

        .flip-card-front, .flip-card-back
          position: absolute
          width: 100%
          height: 100%
          -webkit-backface-visibility: hidden
          backface-visibility: hidden

        .flip-card-back
          transform: rotateY(180deg)

        &:hover .flip-card-inner
          transform: rotateY(180deg)

      img.subscriptions-item-content-item-image
        object-fit: cover
        height: 100%
        width: 100%
        // border-radius: 0
        // 若需要去除图片圆角可以将这里的注释去掉

      img.subscriptions-item-back-image
        object-fit: cover
        height: 100%
        width: 100%

      .subscriptions-item-content-item-toolbar
        display: flex
        justify-content: space-between
        position: absolute
        bottom: 12px
        left: 0
        width: 100%
        padding: 0 16px

        .subscriptions-item-content-item-link
          background: var(--anzhiyu-main)
          color: var(--anzhiyu-white)
          padding: 6px 12px
          border-radius: 20px
          text-decoration: none
          font-size: 0.85rem
          transition: all 0.3s ease

          &:hover
            background: var(--anzhiyu-main-op)

        .bber-reply
          background: var(--anzhiyu-main)
          color: var(--anzhiyu-white)
          padding: 6px 8px
          border-radius: 20px
          cursor: pointer
          transition: all 0.3s ease

          &:hover
            background: var(--anzhiyu-main-op)

          i
            font-size: 0.9rem

// 页面整体样式
body[data-type="subscriptions"] #web_bg
  background: var(--anzhiyu-background);

body[data-type="subscriptions"] #page
  border: 0;
  box-shadow: none !important;
  padding: 0 !important;
  background: 0 0 !important;

body[data-type="subscriptions"] #page .page-title
  display: none;

//- https://guide.daocloud.io/daovoice/javascript-api-5869833.html
script.
  (() => {
    (function(i,s,o,g,r,a,m){i["DaoVoiceObject"]=r;i[r]=i[r]||function(){(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;a.charset="utf-8";m.parentNode.insertBefore(a,m)})(window,document,"script",('https:' == document.location.protocol ? 'https:' : 'http:') + "//widget.daovoice.io/widget/!{theme.daovoice.app_id}.js","daovoice")

    const isChatBtn = !{theme.chat_btn}
    const isChatHideShow = !{theme.chat_hide_show}

    daovoice('init', {
      app_id: '!{theme.daovoice.app_id}',},{
      launcher: { 
        disableLauncherIcon: isChatBtn
      },
    });
    daovoice('update');

    if (isChatBtn) {
      window.chatBtnFn = () => {
        const isShow = document.getElementById('daodream-messenger').classList.contains('daodream-messenger-active')
        isShow ? daovoice('hide') : daovoice('show')
      }
    } else if (isChatHideShow) {
      window.chatBtn = {
        hide: () => {
          daovoice('update', {},{
            launcher: {
              disableLauncherIcon: true
            }
          })
        },
        show: () => {
          daovoice('update', {}, {
            launcher: {
              disableLauncherIcon: false
            }
          })
        }
      }
    }
  })()
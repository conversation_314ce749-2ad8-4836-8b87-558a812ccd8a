<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"><title>即刻短文 | 我的博客</title><meta name="author" content="c2hapmll"><meta name="copyright" content="c2hapmll"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f7f9fe"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="即刻短文"><meta name="application-name" content="即刻短文"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f7f9fe"><meta property="og:type" content="website"><meta property="og:title" content="即刻短文"><meta property="og:url" content="https://shijie.icu/essay/index.html"><meta property="og:site_name" content="我的博客"><meta property="og:description" content="即刻短文"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://shijie.icu/img/default_cover.jpg"><meta property="article:author" content="c2hapmll"><meta property="article:tag"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://shijie.icu/img/default_cover.jpg"><meta name="description" content="即刻短文"><link rel="shortcut icon" href="/images/themes/32.png"><link rel="canonical" href="https://shijie.icu/essay/"><link rel="preconnect" href="//cdn.cbd.int"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><meta name="google-site-verification" content="xxx"/><meta name="baidu-site-verification" content="code-xxx"/><meta name="msvalidate.01" content="xxx"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.cbd.int/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media='all'"><script>const GLOBAL_CONFIG = {
  linkPageTop: undefined,
  peoplecanvas: undefined,
  postHeadAiDescription: undefined,
  diytitle: undefined,
  LA51: undefined,
  greetingBox: undefined,
  twikooEnvId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
  commentBarrageConfig:undefined,
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: false,
  mainTone: undefined,
  authorStatus: {"skills":["🤖️ 数码科技爱好者","🔍 分享与热心帮助","🏠 智能家居小能手"]},
  algolia: undefined,
  localSearch: {"path":"/search.xml","preload":true,"languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: undefined,
  highlight: {"plugin":"prismjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    simplehomepage: true,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: c2hapmll","link":"链接: ","source":"来源: 我的博客","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: false,
  shortcutKey: undefined,
  autoDarkmode: true
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  configTitle: '我的博客',
  title: '即刻短文',
  postAI: '',
  pageFillDescription: '即刻短文',
  isPost: false,
  isHome: false,
  isHighlightShrink: false,
  isToc: false,
  postUpdate: '2024-11-13 10:10:38',
  postMainColor: '',
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#18171d')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f7f9fe')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/costom_widget.css"><link rel="stylesheet" href="/css/badge.css"><meta name="generator" content="Hexo 7.3.0"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="/images/themes/32.png"/><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"/><script async="async" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="page" id="body-wrap"><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><a id="site-name" href="/" accesskey="h"><div class="title">我的博客</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"/><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole();"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" alt="微信" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/wechat.jpg"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" alt="支付宝" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/alipay.jpg"/></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title"> 最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 1.05rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 1.05rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 1.05rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 1.05rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 1.05rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 1.05rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 1.05rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 1.05rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 1.05rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/02/"><span class="card-archive-list-date">二月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/12/"><span class="card-archive-list-date">十二月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/11/"><span class="card-archive-list-date">十一月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/10/"><span class="card-archive-list-date">十月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/09/"><span class="card-archive-list-date">九月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li></ul></div><hr/></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout hide-aside" id="content-inner"><div id="page"><h1 class="page-title">即刻短文</h1><div id="essay_page"><div class="author-content author-content-item essayPage single" style="background: url(/images/essay/top_background.jpg) left 28% / cover no-repeat;"><div class="card-content"><div class="author-content-item-tips">即刻</div><span class="author-content-item-title">咸鱼的日常生活。</span><div class="content-bottom"><div class="tips">随时随地，分享生活</div></div><div class="banner-button-group"><a class="banner-button" href="/about/"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i><span class="banner-button-text">关于我</span></a></div></div></div><div id="bber"><section class="timeline page-1"><ul class="list" id="waterfall"><li class="bber-item"><div class="bber-content"><div class="datacont"><p>本来想写个文章讲选购宽带的，但是落笔发现没什么可讲的，就是淘宝、拼多多找低价的宽带就行了，没什么坑，营业厅办的宽带坑才叫多，又贵，速度还慢。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/05/06 13:47">2025-05-06T05:47:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('本来想写个文章讲选购宽带的，但是落笔发现没什么可讲的，就是淘宝、拼多多找低价的宽带就行了，没什么坑，营业厅办的宽带坑才叫多，又贵，速度还慢。
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>1. 节前最后一天，已经完全没有气力工作了。</p><p>2. 近期看大麦的演出信息的时候，发现自己的眼界还是低，在我还不关注的领域，挣钱的方法还是很多。</p><p>3. 等我不干了，就整合自己了解的领域，不求赚钱，只求收支平衡的话还是比较简单的。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/04/30 11:11">2025-04-30T03:11:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('1. 节前最后一天，已经完全没有气力工作了。
2. 近期看大麦的演出信息的时候，发现自己的眼界还是低，在我还不关注的领域，挣钱的方法还是很多。
3. 等我不干了，就整合自己了解的领域，不求赚钱，只求收支平衡的话还是比较简单的。
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>我不明白，明明经济独立自主，为什么要偏偏给自己戴上紧箍咒，工资上交无论男女完全就是非常离谱的操作。金融行业投资都要做风险管理，怎么到了结婚就完全不管了呢？</p><p>所以人一定要有壮士断腕的勇气，当你不能确定100%能够达成目标，沉没成本就一定要下决心放弃。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/04/24 17:45">2025-04-24T09:45:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('我不明白，明明经济独立自主，为什么要偏偏给自己戴上紧箍咒，工资上交无论男女完全就是非常离谱的操作。金融行业投资都要做风险管理，怎么到了结婚就完全不管了呢？
所以人一定要有壮士断腕的勇气，当你不能确定100%能够达成目标，沉没成本就一定要下决心放弃。
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>小时候经常跑游戏厅去玩，那个时候没有钱，都是在游戏厅看着别人打，看人家打三国战纪玩诸葛亮放大招，又是什么&quot;雷霆万钧&quot;，又是什么爆气后的&quot;呼风唤雨&quot;，津津有味能看到天黑，然后就被薅着耳朵拎回家。到大了一些游戏厅又流行什么捕鱼达人什么的，老板还能上分下分，当时已经有些是非观念了，觉得这就是赌博，后来逐渐就对游戏厅这个地点不感兴趣了。</p><p>很多人问我为什么喜欢打游戏，我觉得根本原因就是因为游戏是我从小到大能够接触到的，比较高级的娱乐方式。如果说我小时候从未接触过游戏，也许就没有现在的作为程序员的我，我有可能在别的行业工作，也有可能在家里的店里干活，还有可能在工地上搬砖。</p><p>我对现状应该是满意的，因为我本身是一个很纠结的人，如果你只给我两个选项，我会仔细的评估每个选择，做出符合当下的决策。但如果你不给我选项，我就会立刻陷入迷茫，我不知道该如何做决定，这也是我在关于页面中写到我是个怠惰因循、得过且过的人的原因。</p><p>(整理 RetroArch 街机游戏库的时候突然emo了，不知道为什么，可能是吃饱喝足和平静的生活闲的，我是否应该花点钱去做下心理评估？花这钱可能还不如再买两款游戏)</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/04/18 17:41">2025-04-18T09:41:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('小时候经常跑游戏厅去玩，那个时候没有钱，都是在游戏厅看着别人打，看人家打三国战纪玩诸葛亮放大招，又是什么&quot;雷霆万钧&quot;，又是什么爆气后的&quot;呼风唤雨&quot;，津津有味能看到天黑，然后就被薅着耳朵拎回家。到大了一些游戏厅又流行什么捕鱼达人什么的，老板还能上分下分，当时已经有些是非观念了，觉得这就是赌博，后来逐渐就对游戏厅这个地点不感兴趣了。
很多人问我为什么喜欢打游戏，我觉得根本原因就是因为游戏是我从小到大能够接触到的，比较高级的娱乐方式。如果说我小时候从未接触过游戏，也许就没有现在的作为程序员的我，我有可能在别的行业工作，也有可能在家里的店里干活，还有可能在工地上搬砖。
我对现状应该是满意的，因为我本身是一个很纠结的人，如果你只给我两个选项，我会仔细的评估每个选择，做出符合当下的决策。但如果你不给我选项，我就会立刻陷入迷茫，我不知道该如何做决定，这也是我在关于页面中写到我是个怠惰因循、得过且过的人的原因。
(整理 RetroArch 街机游戏库的时候突然emo了，不知道为什么，可能是吃饱喝足和平静的生活闲的，我是否应该花点钱去做下心理评估？花这钱可能还不如再买两款游戏)
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>玩了太多的游戏，对于游戏的品质要求是越来越高了，</p><p>导致现在很想要一个没玩过游戏的脑子，这样那些</p><p>经典游戏我就能再玩一遍了。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/04/17 9:00">2025-04-17T01:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('玩了太多的游戏，对于游戏的品质要求是越来越高了，
导致现在很想要一个没玩过游戏的脑子，这样那些
经典游戏我就能再玩一遍了。
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>每当通关一款游戏、读完一本小说、看完一整部剧集，我总是会觉得悸动，</p><p>好像这段故事、这次冒险就曾经发生在我的身边，内心久久不能平静。</p><p>(ta嘴里喊着什么友情啊、羁绊啊、未来啊什么的就向我冲了过来.gif)</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/04/07 17:00:00">2025-04-07T09:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('每当通关一款游戏、读完一本小说、看完一整部剧集，我总是会觉得悸动，
好像这段故事、这次冒险就曾经发生在我的身边，内心久久不能平静。
(ta嘴里喊着什么友情啊、羁绊啊、未来啊什么的就向我冲了过来.gif)
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>感谢阿成的礼物</p><div class="bber-container-img"><a class="bber-content-img" href="/images/essay/2025/03/202503190101.png" target="_blank" data-fancybox="gallery" data-caption=""><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/essay/2025/03/202503190101.png"></a><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div></div></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/03/19 17:00:00">2025-03-19T09:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('感谢阿成的礼物')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>感觉现在很多好看的图片都是处理过的，尤其是旅游类App上的图片，眼睛看到的和拍到的完全不一样。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/02/27 10:49:00">2025-02-27T02:49:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('感觉现在很多好看的图片都是处理过的，尤其是旅游类App上的图片，眼睛看到的和拍到的完全不一样。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>快过年了，不要再讨论什么NAS、HTPC、OpenWrt 了。你带你的大机箱回到家并不能给你带来任何实质性作用，</p><p>朋友们兜里掏出一大把钱吃喝玩乐，你默默的在家里摆弄你的破群晖。亲戚朋友吃饭问你收获了什么，</p><p>你说我组了一个RAID 0的ALL IN ONE，亲戚们懵逼了，你还在心里默默嘲笑他们，笑他们不懂你的刮削器，</p><p>不懂你的Auto Backup，也笑他们看爱奇艺还要忍受会员专属广告。你父母的同事都在说自己的子女一年的收获，</p><p>儿子买了个房，女儿买了个车，姑娘升职加薪了，你的父母默默无言，说我的儿子装了个黑盒子，开起来嗡嗡响，家里电表走得越来越快了。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/02/20 17:25:00">2025-02-20T09:25:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('快过年了，不要再讨论什么NAS、HTPC、OpenWrt 了。你带你的大机箱回到家并不能给你带来任何实质性作用，
朋友们兜里掏出一大把钱吃喝玩乐，你默默的在家里摆弄你的破群晖。亲戚朋友吃饭问你收获了什么，
你说我组了一个RAID 0的ALL IN ONE，亲戚们懵逼了，你还在心里默默嘲笑他们，笑他们不懂你的刮削器，
不懂你的Auto Backup，也笑他们看爱奇艺还要忍受会员专属广告。你父母的同事都在说自己的子女一年的收获，
儿子买了个房，女儿买了个车，姑娘升职加薪了，你的父母默默无言，说我的儿子装了个黑盒子，开起来嗡嗡响，家里电表走得越来越快了。
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>考虑到支出记录每个月都要开一篇文章有点浪费，准备做成页面的形式，直接在yml里面进行数据更新。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/02/07 14:14:00">2025-02-07T06:14:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('考虑到支出记录每个月都要开一篇文章有点浪费，准备做成页面的形式，直接在yml里面进行数据更新。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>有趣的BUG，焊武帝盾兵</p><div class="bber-container-img"><a class="bber-content-img" href="/images/essay/2025/02/232025020701.jpg" target="_blank" data-fancybox="gallery" data-caption=""><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/essay/2025/02/232025020701.jpg"></a><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div></div></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/02/07 09:00:00">2025-02-07T01:00:00.000Z</time></div><div class="bber-info-from"><i class="anzhiyufont anzhiyu-icon-location-dot"></i><span>三角洲行动-机密大坝</span></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('有趣的BUG，焊武帝盾兵')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>侧边栏&quot;当前在听&quot;功能早就想实现了，正好假期结束慢慢更新，给自己找点活干。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/02/06 13:31:00">2025-02-06T05:31:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('侧边栏&quot;当前在听&quot;功能早就想实现了，正好假期结束慢慢更新，给自己找点活干。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>春节放假回家，暂停更新</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/01/24 18:00:00">2025-01-24T10:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('春节放假回家，暂停更新')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>得甲流了，停几天更新</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2025/01/01 09:00:00">2025-01-01T01:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('得甲流了，停几天更新')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>开了防撤回被制裁了</p><div class="bber-container-img"><a class="bber-content-img" href="/images/essay/2024/12/2024122501.jpg" target="_blank" data-fancybox="gallery" data-caption=""><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/essay/2024/12/2024122501.jpg"></a><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div></div></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/12/25 17:10:00">2024-12-25T09:10:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('开了防撤回被制裁了')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>已经完全记不清上次收到礼物是何时了，今天居然收到了两份礼物，一份红美人柑橘、一份车厘子，实在是太感谢了。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/12/24 16:14:00">2024-12-24T08:14:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('已经完全记不清上次收到礼物是何时了，今天居然收到了两份礼物，一份红美人柑橘、一份车厘子，实在是太感谢了。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>当你觉得生活很扯淡的时候，你可以去看TGA年度游戏颁奖典礼，刚才最佳持续运营颁给了从46万日活运营到4万日活的《地狱潜兵2》;</p><p>当你觉得生活不够有压迫感的时候，你可以去看TGA年度游戏颁奖典礼，刚才任天堂在下面坐着，上面在放幻兽帕鲁的新作播片;</p><p>当你觉得你的家庭关系一团糟的时候，你可以去看TGA年度游戏颁奖典礼，刚才一个单人游戏，获得了最佳家庭游戏奖;</p><p>当你觉得你的人生没有方向的时候，你可以去看TGA年度游戏颁奖典礼，刚才年度最佳游戏颁给了销量不到200万、游戏流程不到10小时，售价高达468、粉丝都找不到的《宇宙机器人》</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/12/13 13:40:00">2024-12-13T05:40:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('当你觉得生活很扯淡的时候，你可以去看TGA年度游戏颁奖典礼，刚才最佳持续运营颁给了从46万日活运营到4万日活的《地狱潜兵2》;

当你觉得生活不够有压迫感的时候，你可以去看TGA年度游戏颁奖典礼，刚才任天堂在下面坐着，上面在放幻兽帕鲁的新作播片;

当你觉得你的家庭关系一团糟的时候，你可以去看TGA年度游戏颁奖典礼，刚才一个单人游戏，获得了最佳家庭游戏奖;

当你觉得你的人生没有方向的时候，你可以去看TGA年度游戏颁奖典礼，刚才年度最佳游戏颁给了销量不到200万、游戏流程不到10小时，售价高达468、粉丝都找不到的《宇宙机器人》
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>不要太执着于别人给你设立的目标，自己开心就行</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/12/09 18:00:00">2024-12-09T10:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('不要太执着于别人给你设立的目标，自己开心就行')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>修改了即刻页面的文本显示，增加了多行文本功能，之前挤在一行有点难受</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/12/03 18:00:00">2024-12-03T10:00:00.000Z</time></div><a class="bber-content-link" title="跳转到短文指引的链接" href="https://shijie.icu/post/20241203090000.html#%E5%A4%9A%E8%A1%8C%E6%96%87%E6%9C%AC" rel="external nofollow"><i class="anzhiyufont anzhiyu-icon-link"></i>链接</a></div><div class="bber-reply" onclick="rm.rightMenuCommentText('修改了即刻页面的文本显示，增加了多行文本功能，之前挤在一行有点难受')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>达尼亚-谭晶</p><div class="bber-container-img"><div style="position: relative; padding: 30% 45%;margin-top: 10px;margin-bottom: 10px;"><iframe style="position: absolute; width: 100%; height: 100%; left: 0; top: 0;margin: 0;border-radius: 12px;border: var(--style-border);" src="https://player.bilibili.com/player.html?isOutside=true&amp;aid=42576546&amp;bvid=BV1Lb411r7JL&amp;cid=74701224&amp;p=1&amp;autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe></div><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div></div></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/12/03">2024-12-02T16:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('达尼亚-谭晶')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>自我感觉内心还是比较强大的，但是今天早上刷小黑盒看到一篇漫画，叫做《贫民窟的晓》，硬生生给我看破防了，</p><p>又想到那句描述无锡学校持刀伤人案的话，说：想象一下，你在学校的蜜雪冰城开开心心买了杯6块钱的柠檬水，回宿舍的路上还没喝几口就被人莫名捅了几刀，弥留之际还在想着没喝完的柠檬水。</p><p>我心里就突然难受至极，眼泪止不住的流。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/27 13:27:00">2024-11-27T05:27:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('自我感觉内心还是比较强大的，但是今天早上刷小黑盒看到一篇漫画，叫做《贫民窟的晓》，硬生生给我看破防了，
又想到那句描述无锡学校持刀伤人案的话，说：想象一下，你在学校的蜜雪冰城开开心心买了杯6块钱的柠檬水，回宿舍的路上还没喝几口就被人莫名捅了几刀，弥留之际还在想着没喝完的柠檬水。
我心里就突然难受至极，眼泪止不住的流。
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>你求名利，他卜吉凶，可怜我全无心肝，怎出得什么主意？</p><p>殿遏烟云，堂列钟鼎，堪笑人供此泥木，空费了许多钱财。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/27 9:25:00">2024-11-27T01:25:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('你求名利，他卜吉凶，可怜我全无心肝，怎出得什么主意？
殿遏烟云，堂列钟鼎，堪笑人供此泥木，空费了许多钱财。
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>后来我才知道，她并不是我的花，我也只是途径了她的盛放，可生活不是电影，我也缺少一点运气，我偷偷的碰了她一下，却不料她如蒲公英般散开，</p><p>此后到处都飘絮着她的模样。 </p><p>    --小黑盒用户:黄小智123</p><div class="bber-container-img"><a class="bber-content-img" href="/images/essay/2024/11/2024112501.jpg" target="_blank" data-fancybox="gallery" data-caption=""><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/essay/2024/11/2024112501.jpg"></a><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div><div class="bber-content-noimg"></div></div></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/25 15:25:00">2024-11-25T07:25:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('后来我才知道，她并不是我的花，我也只是途径了她的盛放，可生活不是电影，我也缺少一点运气，我偷偷的碰了她一下，却不料她如蒲公英般散开，
此后到处都飘絮着她的模样。 
    --小黑盒用户:黄小智123
')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>困啊困，困啊困，周一事情又多，有点折磨</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/25 14:00:00">2024-11-25T06:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('困啊困，困啊困，周一事情又多，有点折磨')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>每天上下班5分钟,休息时间绝对是够的,但是一到办公室就犯困,咖啡也缓解不了</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/21 17:59:59">2024-11-21T09:59:59.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('每天上下班5分钟,休息时间绝对是够的,但是一到办公室就犯困,咖啡也缓解不了')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>记忆已经久远，但经历不会遗忘。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/20 15:25:00">2024-11-20T07:25:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('记忆已经久远，但经历不会遗忘。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>今天收到部门领导祝贺,说我已经在公司工作满五年了,这才惊觉又是一年11月20日,看着公司发送的祝贺邮件，真是感慨万千。</p><p>当初参加学院竞赛小组,被学院老师推荐到公司实习,没曾想这一工作就是五年,当时实习入职时间正是19年11月20日。</p><p>还记得接到公司的实习offer时,拎着个行李箱就往北京跑,租个房子押金钱不够还是问文杰借的,工资5000交完房租就不剩下多少了,拿剩下的工资给老妈买了台脚部按摩仪，</p><p>上班通勤时间一度达到两个半小时，当时竟不觉得苦，只觉得未来一片光明。后面又经历了转正、疫情、人事变动、团队变动，逐渐的，</p><p>技术能力增加了不少，工资也翻了几番，加上去年调回苏州，生活节奏一下子慢了下来，也有了一些积蓄，这个过程真的要感谢家人、朋友、老师、同事、领导的帮助。</p><p>写得越多，越多愁善感，总是不自觉的带入感情，总之，感谢所有帮助过我的人，也希望未来也同样能够乘风破浪，继续前行。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/20 14:34:00">2024-11-20T06:34:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('今天收到部门领导祝贺,说我已经在公司工作满五年了,这才惊觉又是一年11月20日,看着公司发送的祝贺邮件，真是感慨万千。
当初参加学院竞赛小组,被学院老师推荐到公司实习,没曾想这一工作就是五年,当时实习入职时间正是19年11月20日。
还记得接到公司的实习offer时,拎着个行李箱就往北京跑,租个房子押金钱不够还是问文杰借的,工资5000交完房租就不剩下多少了,拿剩下的工资给老妈买了台脚部按摩仪，
上班通勤时间一度达到两个半小时，当时竟不觉得苦，只觉得未来一片光明。后面又经历了转正、疫情、人事变动、团队变动，逐渐的，
技术能力增加了不少，工资也翻了几番，加上去年调回苏州，生活节奏一下子慢了下来，也有了一些积蓄，这个过程真的要感谢家人、朋友、老师、同事、领导的帮助。
写得越多，越多愁善感，总是不自觉的带入感情，总之，感谢所有帮助过我的人，也希望未来也同样能够乘风破浪，继续前行。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>说到做梦，有很多时候我能够意识到自己是在梦中的，可以实现跳一下几十米高，几百米远，但无法做到更离谱的事情，比如像超人那样直接飞起来，还是多少遵守点物理规则🫤</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/14 16:35:00">2024-11-14T08:35:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('说到做梦，有很多时候我能够意识到自己是在梦中的，可以实现跳一下几十米高，几百米远，但无法做到更离谱的事情，比如像超人那样直接飞起来，还是多少遵守点物理规则🫤')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>又做了个奇怪的梦，梦见自己租了个一楼的车库住，突然发现厨房上面有一层积水，想用抹布擦掉，转头发现不是积水，而是下暴雨导致水漫进来了，都淹到了床的高度，我的东西都被水泡了，然后就吓醒了。然后发现是因为昨晚没关窗，今天暴雨，空气太潮湿了导致的梦。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/14 09:00:00">2024-11-14T01:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('又做了个奇怪的梦，梦见自己租了个一楼的车库住，突然发现厨房上面有一层积水，想用抹布擦掉，转头发现不是积水，而是下暴雨导致水漫进来了，都淹到了床的高度，我的东西都被水泡了，然后就吓醒了。然后发现是因为昨晚没关窗，今天暴雨，空气太潮湿了导致的梦。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li><li class="bber-item"><div class="bber-content"><div class="datacont"><p>把音乐页面的图片卡片变成了圆形,然后鼠标移到卡片上还会像CD一样旋转,感觉不错。然后觉得如果记录内容过多，每次下滑很久不太好，就加了页面分页，效果也不错。</p></div></div><hr><div class="bber-bottom"><div class="bber-info"><div class="bber-info-time"><i class="anzhiyufont anzhiyu-icon-clock"></i><time class="datatime" datetime="2024/11/13 18:00:00">2024-11-13T10:00:00.000Z</time></div></div><div class="bber-reply" onclick="rm.rightMenuCommentText('把音乐页面的图片卡片变成了圆形,然后鼠标移到卡片上还会像CD一样旋转,感觉不错。然后觉得如果记录内容过多，每次下滑很久不太好，就加了页面分页，效果也不错。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></li></ul></section></div><div id="bber-tips" style="color: var(--anzhiyu-secondtext);">- 只展示最近30条短文 -</div></div><hr/><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)">匿名评论</a><a href="/privacy" style="margin-left: 4px">隐私政策</a></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div></div></div><div class="comment-barrage"></div></div></div></main><footer id="footer"><div id="footer-wrap"></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">&copy;2024 - 2025 By <a class="footer-bar-link" href="/" title="c2hapmll" target="_blank">c2hapmll</a></div></div><div id="footer-type-tips"></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index" title="皖ICP备20000482号-2">皖ICP备20000482号-2</a><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.mps.gov.cn/#/query/webSearch" title="皖公网安备 34120402000423号">皖公网安备 34120402000423号</a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">6</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">9</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">3</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" target="_blank" rel="noopener" href="https://home.shijie.icu/" title="我的主页"><img class="back-menu-item-icon" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/32.png" alt="我的主页"/><span class="back-menu-item-text">我的主页</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 0.88rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 0.88rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 0.88rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 0.88rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 0.88rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 0.88rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 0.88rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 0.88rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 0.88rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button></div><div id="rightside-config-show"><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><a id="to_comment" href="#post-comment" title="直达评论"><i class="anzhiyufont anzhiyu-icon-comments"></i></a><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><span id="loading-status"></span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="is-center" id="loading-database"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-pulse-icon"></i><span>  数据库加载中</span></div><div class="search-wrap"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div><hr/><div id="local-search-results"></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size: 1rem;"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://y.qq.com/n/ryqq/playlist/8802438608&quot;, &quot;_blank&quot;);" style="display: none;"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://cdn.cbd.int/instant.page@5.2.0/instantpage.js" type="module"></script><script src="https://cdn.cbd.int/vanilla-lazyload@17.8.5/dist/lazyload.iife.min.js"></script><script src="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.js"></script><canvas id="universe"></canvas><script async src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script async src="/anzhiyu/random.js"></script><script src="/js/search/local-search.js"></script><div class="js-pjax"><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="essay"></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
        region: '',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script>var visitorMail = "";
</script><script async data-pjax src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script async src="https://at.alicdn.com/t/c/font_4729035_3m5int5al8c.js"></script><script src="/js/countdown.js"></script><script src="/js/getnowplaying.js"></script><script src="/js/badge.js"></script><script src="/js/update.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/aplayer/APlayer.min.css" media="print" onload="this.media='all'"><script src="https://cdn.cbd.int/anzhiyu-blog-static@1.0.1/js/APlayer.min.js"></script><script src="https://cdn.cbd.int/hexo-anzhiyu-music@1.0.1/assets/js/Meting2.min.js"></script><script src="https://cdn.cbd.int/pjax@0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: false,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script><script charset="UTF-8" src="https://cdn.cbd.int/anzhiyu-theme-static@1.1.5/accesskey/accesskey.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div></body></html>
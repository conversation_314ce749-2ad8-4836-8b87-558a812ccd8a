{"name": "hexo-generator-category", "version": "2.0.0", "description": "Category generator for Hexo.", "main": "index", "scripts": {"eslint": "eslint .", "test": "mocha test/index.js", "test-cov": "c8 --reporter=lcovonly npm run test"}, "directories": {"lib": "./lib"}, "files": ["index.js", "lib/"], "repository": "hexojs/hexo-generator-category", "homepage": "https://hexo.io/", "keywords": ["hexo", "generator", "category"], "author": "<PERSON> <<EMAIL>> (https://zespia.tw)", "license": "MIT", "devDependencies": {"c8": "^7.12.0", "chai": "^4.3.6", "coveralls": "^3.1.1", "eslint": "^8.25.0", "eslint-config-hexo": "^5.0.0", "hexo": "^6.3.0", "mocha": "^10.0.0"}, "dependencies": {"hexo-pagination": "3.0.0"}, "engines": {"node": ">=14"}}
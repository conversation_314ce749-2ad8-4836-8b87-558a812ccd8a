---
title: 2024年12月03日-<PERSON>xo安知鱼主题魔改记录
date: 2024-12-03 09:00:00
categories:
  - 主题
tags:
  - 主题
  - 魔改
cover: /images/posts/2024120301/top.webp
---

## 1. 即刻短文

### 1.1 多行文本

#### 1.1.1 效果展示

![即刻短文多行文本](/images/posts/2024120301/202412030101.png)

#### 1.1.2 开始修改

找到 `/themes/anzhiyu/layout/includes/page/essay.pug`文件，将
```pug
p.datacont= item.content
```
修改为
```pug
div.datacont
  each paragraph in item.content.split("\n")
    if paragraph.trim() !== ""
      p= paragraph
```
修改时注意代码缩进，p与div同级替换。

`essay.yml`文件中，使用 `|` 保证段落换行，例如：
```yml
  essay_list:
    - content: |
        你求名利，他卜吉凶，可怜我全无心肝，怎出得什么主意？
        殿遏烟云，堂列钟鼎，堪笑人供此泥木，空费了许多钱财。
      date: 2024/11/27 9:25:00
```

不想换行就按照之前的说说样式写就行，或者`|`只写一行

## 2. 侧边栏

### 2.1 steam游戏卡片

参考：[李小白的博客-侧边栏steam卡片](https://cf.lxb.icu/undefined/29816)

#### 2.1.1 效果展示

![ steam游戏卡片](/images/posts/2024120301/202412030102.png)

#### 2.1.1 开始修改

跳转到 steam 卡片生成网站：[Steam Card](https://cardn.yuy1n.io/)，使用steam账户登录，按照网页中的Config项配置你想要的卡片效果，右侧找到`HTML`标签卡片，点击复制。

新建`widget.yml`文件，完整路径为`source/_data/widget.yml`，内容如下
```yml
# top: 创建的 widget 会出现在非 sticky 区域（即所有页面都会显示)
# bottom: 创建的 widget 会出现在 sticky 区域（除了文章页都会显示)
top:
  # steam 游戏卡片
  - class_name: game-card-widget
    id_name: game-card-widget
    name: 游戏卡片
    icon: fas fa-gamepad  # 你可以根据需要添加图标
    order: 1
    html: |
      <div id="game-card-widget">
        这里填入你复制的HTML标签卡片代码全选替换
      </div>
```

新建`costom_widget.css`文件，完整路径为`source/css/costom_widget.css`，内容如下：
```css
#game-card-widget {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;  
  }
#game-card-widget img {
  max-width: 100%;  
  height: auto;  
}
```

自定义的css文件需要在主题配置文件`_config.anzhiyu.yml`中`inject`项导入
```yml
inject:
  head:
    # 自定义css
    - <link rel="stylesheet" href="/css/costom_widget.css">
  bottom:
    # 自定义js
    # - <script src="/js/xxx"></script>
```

命令行执行 `hexo clean && hexo g` 查看效果

### 2.2 网易云音乐卡片(失效)

参考：[李小白的博客-侧边栏网易云音乐](https://cf.lxb.icu/undefined/57573)

#### 2.2.1 效果展示

![网易云音乐卡片](/images/posts/2024120301/202412030103.png)

#### 2.2.2 开始修改

`注意，由于网络原因，vercel域名被屏蔽，请在项目中自己解析自己的域名，不然只能使用本地部署方式，否则将无法访问云音乐卡片服务。`

首先需要部署 [Netease Recent Profile](https://github.com/zonemeen/netease-recent-profile) 服务，按照项目教程使用vercel部署或本地部署，我用的是vercel方式，部署完毕后访问云音乐卡片模式的标准网址是：

[https://netease-recent-profile.vercel.app/?id=126764012&size=60](https://netease-recent-profile.vercel.app/?id=126764012&size=60)

其中 `netease-recent-profile.vercel.app`来自于你的vercel项目域名，请替换为你的项目域名

ID `126764012`来自于你的网易云音乐ID，具体获取方式查看该项目教程，请替换为你的网易云ID

接着，新建`widget.yml`文件，完整路径为`source/_data/widget.yml`，内容如下，如果你按照本文已经创建过`widget.yml`文件，在其中追加即可，本小节示例为默认版本，具体src配置项请参照[Netease Recent Profile](https://github.com/zonemeen/netease-recent-profile) 项目修改。

```yml
# top: 创建的 widget 会出现在非 sticky 区域（即所有页面都会显示)  
# bottom: 创建的 widget 会出现在 sticky 区域（除了文章页都会显示)  
# 将 src 中的域名和id替换为你自己的项目域名和id。
top:  
  - class_name: card-music  
    id_name: card-widget-netease  
    name: 最近播放  
    icon: fas fa-headphones  
    order: 2  
    html: '<img src="https://music.siyouyun.eu.org/?id=1939753826&theme=card&show_rainbow=1&size=300" alt="Netease recently played" width="300" />'
```

命令行执行 `hexo clean && hexo g` 查看效果

#### 2.2.3 常见问题

1. 部署完成后报错：Something went wrong! file an issue at [https://github.com/zonemeen/netease-recent-profile](https://github.com/zonemeen/netease-recent-profile)
这是代表服务正常运行的情况，想要访问卡片内容，需要在域名中加入id信息，例如：[https://netease-recent-profile.vercel.app/?id=126764012&size=60](https://netease-recent-profile.vercel.app/?id=126764012&size=60)

2. 部署完毕后，域名可以正常访问，但是无法加载音乐内容
查看网易云音乐账户最近是否听过歌曲，没有数据无法生成
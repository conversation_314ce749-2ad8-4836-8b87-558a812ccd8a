import moment from 'moment-timezone';
import type { LocalsType } from '../../types';
declare function toISOString(date?: string | number | Date | moment.Moment): string;
declare function dateHelper(this: LocalsType, date?: moment.Moment | moment.MomentInput, format?: string): string;
declare function timeHelper(this: LocalsType, date?: moment.Moment | moment.MomentInput, format?: string): string;
declare function fullDateHelper(this: LocalsType, date?: moment.Moment | moment.MomentInput, format?: string): string;
declare function relativeDateHelper(this: LocalsType, date?: moment.Moment | moment.MomentInput): string;
declare function timeTagHelper(this: LocalsType, date?: string | number | Date | moment.Moment, format?: string): string;
/**
 * Convert Hexo language code to Moment locale code.
 * examples:
 *   default => en
 *   zh-CN => zh-cn
 *
 * Moment defined locales: https://github.com/moment/moment/tree/master/locale
 */
declare function _toMomentLocale(lang?: string): string;
export { dateHelper as date };
export { toISOString as date_xml };
export { timeHelper as time };
export { fullDateHelper as full_date };
export { relativeDateHelper as relative_date };
export { timeTagHelper as time_tag };
export { moment };
export declare const toMomentLocale: import("moize").Moized<typeof _toMomentLocale, Partial<{
    isDeepEqual: boolean;
    isPromise: boolean;
    isReact: boolean;
    isSerialized: boolean;
    isShallowEqual: boolean;
    matchesArg: import("moize").IsEqual;
    matchesKey: import("moize").IsMatchingKey;
    maxAge: number;
    maxArgs: number;
    maxSize: number;
    onCacheAdd: import("moize").OnCacheOperation<typeof _toMomentLocale>;
    onCacheChange: import("moize").OnCacheOperation<typeof _toMomentLocale>;
    onCacheHit: import("moize").OnCacheOperation<typeof _toMomentLocale>;
    onExpire: import("moize").OnExpire;
    profileName: string;
    serializer: import("moize").Serialize;
    transformArgs: import("moize").TransformKey;
    updateCacheForKey: import("moize").UpdateCacheForKey;
    updateExpire: boolean;
}> & {
    isShallowEqual: true;
}>;

{"name": "hexo-server", "version": "3.0.0", "description": "Server module of Hexo.", "main": "index", "scripts": {"eslint": "eslint .", "test": "mocha test/index.js", "test-cov": "nyc --reporter=lcovonly npm run test"}, "directories": {"lib": "./lib"}, "files": ["index.js", "lib/"], "repository": "hexojs/hexo-server", "homepage": "http://hexo.io/", "keywords": ["hexo", "server", "connect"], "author": "<PERSON> <<EMAIL>> (http://zespia.tw)", "maintainers": ["<PERSON><PERSON> <<EMAIL>> (http://abnerchou.me)"], "license": "MIT", "dependencies": {"bluebird": "^3.5.5", "compression": "^1.7.4", "connect": "^3.7.0", "mime": "^3.0.0", "morgan": "^1.9.1", "picocolors": "^1.0.0", "open": "^8.0.9", "serve-static": "^1.14.1"}, "devDependencies": {"chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^8.1.0", "eslint-config-hexo": "^4.0.0", "hexo": "^6.0.0", "hexo-fs": "^3.0.1", "hexo-util": "^2.1.0", "mocha": "^9.1.3", "nyc": "^15.0.0", "sinon": "^12.0.1", "supertest": "^6.1.3", "uuid": "^8.0.0"}, "engines": {"node": ">=12.13.0"}}
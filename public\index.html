<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"><title>我的博客</title><meta name="author" content="c2hapmll"><meta name="copyright" content="c2hapmll"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f7f9fe"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="我的博客"><meta name="application-name" content="我的博客"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f7f9fe"><meta property="og:type" content="website"><meta property="og:title" content="我的博客"><meta property="og:url" content="https://shijie.icu/index.html"><meta property="og:site_name" content="我的博客"><meta property="og:description" content="1111"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://shijie.icu/images/themes/144.png"><meta property="article:author" content="c2hapmll"><meta property="article:tag"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://shijie.icu/images/themes/144.png"><meta name="description" content="1111"><link rel="shortcut icon" href="/images/themes/32.png"><link rel="canonical" href="https://shijie.icu/"><link rel="preconnect" href="//cdn.cbd.int"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><meta name="google-site-verification" content="xxx"/><meta name="baidu-site-verification" content="code-xxx"/><meta name="msvalidate.01" content="xxx"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.cbd.int/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media='all'"><script>const GLOBAL_CONFIG = {
  linkPageTop: undefined,
  peoplecanvas: undefined,
  postHeadAiDescription: undefined,
  diytitle: undefined,
  LA51: undefined,
  greetingBox: undefined,
  twikooEnvId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
  commentBarrageConfig:undefined,
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: false,
  mainTone: undefined,
  authorStatus: {"skills":["🤖️ 数码科技爱好者","🔍 分享与热心帮助","🏠 智能家居小能手"]},
  algolia: undefined,
  localSearch: {"path":"/search.xml","preload":true,"languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: undefined,
  highlight: {"plugin":"prismjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    simplehomepage: true,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: c2hapmll","link":"链接: ","source":"来源: 我的博客","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: false,
  shortcutKey: undefined,
  autoDarkmode: true
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  configTitle: '我的博客',
  title: '我的博客',
  postAI: '',
  pageFillDescription: '',
  isPost: false,
  isHome: true,
  isHighlightShrink: false,
  isToc: false,
  postUpdate: '2025-05-08 09:56:55',
  postMainColor: '',
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#18171d')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f7f9fe')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/costom_widget.css"><link rel="stylesheet" href="/css/badge.css"><meta name="generator" content="Hexo 7.3.0"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="/images/themes/32.png"/><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"/><script async="async" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><script>function initMourn() {
    const date = new Date();
    const today = (date.getMonth() + 1) + "-" + date.getDate()
    const mourn_days = ["4-5","5-12","7-7","9-18","12-13"]
    if (mourn_days.includes(today)) {
        document.documentElement.style.filter = "grayscale(1)";
    }}
initMourn();</script><div class="page" id="body-wrap"><header class="full_page" id="page-header" style="background: url(/images/themes/index_img.jpg) top / cover no-repeat"><nav id="nav"><div id="nav-group"><span id="blog_name"><a id="site-name" href="/" accesskey="h"><div class="title">我的博客</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"/><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole();"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" alt="微信" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/wechat.jpg"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" alt="支付宝" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/alipay.jpg"/></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title"> 最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 1.05rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 1.05rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 1.05rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 1.05rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 1.05rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 1.05rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 1.05rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 1.05rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 1.05rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/02/"><span class="card-archive-list-date">二月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/12/"><span class="card-archive-list-date">十二月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/11/"><span class="card-archive-list-date">十一月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/10/"><span class="card-archive-list-date">十月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/09/"><span class="card-archive-list-date">九月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li></ul></div><hr/></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="site-info"><h1 id="site-title">我的博客</h1><div id="site-subtitle"><span id="subtitle"></span></div><div id="site_social_icons"><a class="social-icon faa-parent animated-hover" href="https://steamcommunity.com/profiles/76561199172600200/" target="_blank" title="Steam"><svg class="icon faa-tada" aria-hidden="true"><use xlink:href="#icon-steam"></use></svg></a></div></div><div id="scroll-down"><i class="anzhiyufont anzhiyu-icon-angle-down scroll-down-effects"></i></div></header><main id="blog-container"><div class="bbTimeList container" id="bbTimeList"><i class="anzhiyufont anzhiyu-icon-jike bber-logo fontbold" onclick="pjax.loadUrl(&quot;/essay/&quot;);" title="即刻短文" href="javascript:void(0);" aria-hidden="true"></i><div class="swiper-container swiper-no-swiping essay_bar_swiper_container" id="bbtalk" tabindex="-1"><div class="swiper-wrapper" id="bber-talk" onclick="pjax.loadUrl(&quot;/essay/&quot;);"><a class="li-style swiper-slide" href="javascript:void(0);">本来想写个文章讲选购宽带的，但是落笔发现没什么可讲的，就是淘宝、拼多多找低价的宽带就行了，没什么坑，营业厅办的宽带坑才叫多，又贵，速度还慢。
</a><a class="li-style swiper-slide" href="javascript:void(0);">1. 节前最后一天，已经完全没有气力工作了。
2. 近期看大麦的演出信息的时候，发现自己的眼界还是低，在我还不关注的领域，挣钱的方法还是很多。
3. 等我不干了，就整合自己了解的领域，不求赚钱，只求收支平衡的话还是比较简单的。
</a><a class="li-style swiper-slide" href="javascript:void(0);">我不明白，明明经济独立自主，为什么要偏偏给自己戴上紧箍咒，工资上交无论男女完全就是非常离谱的操作。金融行业投资都要做风险管理，怎么到了结婚就完全不管了呢？
所以人一定要有壮士断腕的勇气，当你不能确定100%能够达成目标，沉没成本就一定要下决心放弃。
</a><a class="li-style swiper-slide" href="javascript:void(0);">小时候经常跑游戏厅去玩，那个时候没有钱，都是在游戏厅看着别人打，看人家打三国战纪玩诸葛亮放大招，又是什么&quot;雷霆万钧&quot;，又是什么爆气后的&quot;呼风唤雨&quot;，津津有味能看到天黑，然后就被薅着耳朵拎回家。到大了一些游戏厅又流行什么捕鱼达人什么的，老板还能上分下分，当时已经有些是非观念了，觉得这就是赌博，后来逐渐就对游戏厅这个地点不感兴趣了。
很多人问我为什么喜欢打游戏，我觉得根本原因就是因为游戏是我从小到大能够接触到的，比较高级的娱乐方式。如果说我小时候从未接触过游戏，也许就没有现在的作为程序员的我，我有可能在别的行业工作，也有可能在家里的店里干活，还有可能在工地上搬砖。
我对现状应该是满意的，因为我本身是一个很纠结的人，如果你只给我两个选项，我会仔细的评估每个选择，做出符合当下的决策。但如果你不给我选项，我就会立刻陷入迷茫，我不知道该如何做决定，这也是我在关于页面中写到我是个怠惰因循、得过且过的人的原因。
(整理 RetroArch 街机游戏库的时候突然emo了，不知道为什么，可能是吃饱喝足和平静的生活闲的，我是否应该花点钱去做下心理评估？花这钱可能还不如再买两款游戏)
</a><a class="li-style swiper-slide" href="javascript:void(0);">玩了太多的游戏，对于游戏的品质要求是越来越高了，
导致现在很想要一个没玩过游戏的脑子，这样那些
经典游戏我就能再玩一遍了。
</a><a class="li-style swiper-slide" href="javascript:void(0);">每当通关一款游戏、读完一本小说、看完一整部剧集，我总是会觉得悸动，
好像这段故事、这次冒险就曾经发生在我的身边，内心久久不能平静。
(ta嘴里喊着什么友情啊、羁绊啊、未来啊什么的就向我冲了过来.gif)
</a><a class="li-style swiper-slide" href="javascript:void(0);">感谢阿成的礼物 [图片]</a><a class="li-style swiper-slide" href="javascript:void(0);">感觉现在很多好看的图片都是处理过的，尤其是旅游类App上的图片，眼睛看到的和拍到的完全不一样。</a><a class="li-style swiper-slide" href="javascript:void(0);">快过年了，不要再讨论什么NAS、HTPC、OpenWrt 了。你带你的大机箱回到家并不能给你带来任何实质性作用，
朋友们兜里掏出一大把钱吃喝玩乐，你默默的在家里摆弄你的破群晖。亲戚朋友吃饭问你收获了什么，
你说我组了一个RAID 0的ALL IN ONE，亲戚们懵逼了，你还在心里默默嘲笑他们，笑他们不懂你的刮削器，
不懂你的Auto Backup，也笑他们看爱奇艺还要忍受会员专属广告。你父母的同事都在说自己的子女一年的收获，
儿子买了个房，女儿买了个车，姑娘升职加薪了，你的父母默默无言，说我的儿子装了个黑盒子，开起来嗡嗡响，家里电表走得越来越快了。
</a><a class="li-style swiper-slide" href="javascript:void(0);">考虑到支出记录每个月都要开一篇文章有点浪费，准备做成页面的形式，直接在yml里面进行数据更新。</a></div></div><a class="bber-gotobb anzhiyufont anzhiyu-icon-circle-arrow-right" onclick="pjax.loadUrl(&quot;/essay/&quot;);" href="javascript:void(0);" title="查看全文"></a></div><script src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.js"></script><div class="layout" id="content-inner"><div class="recent-posts" id="recent-posts"><div id="categoryBar"><div class="category-bar" id="category-bar"><div id="catalog-bar"><div id="catalog-list"><div class="catalog-list-item" id="首页"><a href="/">首页</a></div>
    <div class="catalog-list-item" id="/categories/日常/">
      <a href="/categories/日常/">
        日常
      </a>
    </div>
    
    <div class="catalog-list-item" id="/categories/游戏/">
      <a href="/categories/游戏/">
        游戏
      </a>
    </div>
    
    <div class="catalog-list-item" id="/categories/主题/">
      <a href="/categories/主题/">
        主题
      </a>
    </div>
    </div><div class="category-bar-next" id="category-bar-next" onclick="anzhiyu.scrollCategoryBarToRight()"><i class="anzhiyufont anzhiyu-icon-angle-double-right"></i></div><a class="catalog-more" href="/categories/">更多</a></div></div></div><div class="recent-post-item lastestpost-item" onclick="pjax.loadUrl('/post/20250422110000.html')"><div class="post_cover left"><a href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇" style="display: flex;height: 100%;"><img class="post_bg" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/042201/202504220200_top.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2025年04月22日 - RetroArch模拟器 - 联机篇" style="pointer-events: none"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><div class="article-categories-original">游戏</div><span class="newPost">最新</span><a class="unvisited-post" href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇">未读</a></div><a class="article-title" href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇">2025年04月22日 - RetroArch模拟器 - 联机篇</a></div><div class="article-meta-wrap"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days" style="font-size: 15px; display:none"></i><span class="article-meta-label">发表于</span><time datetime="2025-04-22T03:00:00.000Z" title="发表于 2025-04-22 11:00:00" time="2025-04-22 11:00:00">2025-04-22</time><time class="time_hidden" datetime="2025-04-21T07:00:00.000Z" title="更新于 2025-04-21 15:00:00" time="2025-04-21 15:00:00">2025-04-21</time></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E6%97%A5%E5%B8%B8/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>日常</span></a><a class="article-meta__tags" href="/tags/%E6%8C%87%E5%8D%97/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>指南</span></a><a class="article-meta__tags" href="/tags/%E6%B8%B8%E6%88%8F/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>游戏</span></a><a class="article-meta__tags" href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>模拟器</span></a></span></div><div class="content">RetroArch 对于联机的要求比较严格，需要RetroArch 版本一致、核心版本一致、游戏rom一致才能进行联机，此为前提条件需要注意。
1. 设置用户名为防止意外和找不到房间的情况发生，Retroarch需要设置一个用户名用于辨认，房主和玩家尽量都设置，步骤如下

主菜单 -&gt; 设置 -&gt; 用户 -&gt; 用户名，设置为你想要的名称即可

2. 局域网联机过于简单，不再详解。
3. 公网联机3.1 UPnP和端口转发（内网穿透）RetroArch 在不设置代理服务器的情况下默认使用此种方式进行公网联机。
原理是房主主机作为服务端，进行内网穿透映射到外网才可以正常联机，如果你不理解该节的一些名词，请勿参照该节内容，防止路由器暴露公网端口。
3.1.1 路由器进入路由器管理页面，找到UPnP功能并开启，步骤如下

访问192.168.1.1（小米默认是192.168.31.1）进入路由管理页面，开启UPnP功能。

RetroArch 启动后会自动增加UPnP记录，如果没有自动记录请使用端口转发服务

设置端口转发，TCP，主机端局域网地址，端口为55435

3.1 ...</div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/post/20250215190000.html')"><div class="post_cover left"><a href="/post/20250215190000.html" title="2025年02月15日-记一次去医院看牙的经历" style="display: flex;height: 100%;"><img class="post_bg" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/021501/top.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2025年02月15日-记一次去医院看牙的经历" style="pointer-events: none"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><div class="article-categories-original">日常</div><a class="unvisited-post" href="/post/20250215190000.html" title="2025年02月15日-记一次去医院看牙的经历">未读</a></div><a class="article-title" href="/post/20250215190000.html" title="2025年02月15日-记一次去医院看牙的经历">2025年02月15日-记一次去医院看牙的经历</a></div><div class="article-meta-wrap"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days" style="font-size: 15px; display:none"></i><span class="article-meta-label">发表于</span><time datetime="2025-02-15T11:00:00.000Z" title="发表于 2025-02-15 19:00:00" time="2025-02-15 19:00:00">2025-02-15</time><time class="time_hidden" datetime="2025-02-27T07:37:49.000Z" title="更新于 2025-02-27 15:37:49" time="2025-02-27 15:37:49">2025-02-27</time></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%8C%BB%E9%99%A2/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>医院</span></a><a class="article-meta__tags" href="/tags/%E7%89%99%E7%A7%91/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>牙科</span></a></span></div><div class="content">

如果你是真的能扛顶着牙疼过日子，那恭喜你，最终结果只有一个，那就是根管治疗。
“牙医恐惧”，首先一定是疼。除了生理上的疼之外，也有心理上的，心疼钱。


前因24年年中就感觉牙齿不适，偶尔吃饭时会掉落牙齿碎片，当时没觉得是自己的牙齿损坏了，而是以为饭里有些骨头啥的东西咬碎了，后来刷牙时发现右边牙齿根部似乎出了个洞，但是也不痒也不疼，加上本人对于医院有点惧怕，于是就没有太过在意。
25年春节情况变得更加严重，由于过年期间大吃大喝，可能刺激到了损坏的牙齿根部，嚼着嚼着一下子咬到硬物时会发生剧烈疼痛，脸都能疼变形的那种，无法忍受的疼，经历了几次疼痛后右侧牙齿几乎无法正常咬碎食物，只能依靠左侧正常牙齿吃饭，但当时想着回苏州可以刷医保，能省不少钱，于是硬生生忍受了下来（我真是神人）。
终于过完了年，来到苏州的第一件事就是挂牙科的号，经历一顿波折才挂上15号周六上午的号。这期间吃饭时是真的折磨，加上去之前12号的时候牙齿好像还发炎了，隐隐作痛，只能吃布洛芬止痛，夜里也无法好好休息，还好去医院的时候已经消炎了。
初诊专家号是别想了，打工人想在周末挂主流科室的专家号还是很困难的。在苏州12320上 ...</div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/post/20241203090000.html')"><div class="post_cover left"><a href="/post/20241203090000.html" title="2024年12月03日-Hexo安知鱼主题魔改记录" style="display: flex;height: 100%;"><img class="post_bg" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024120301/top.webp" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年12月03日-Hexo安知鱼主题魔改记录" style="pointer-events: none"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><div class="article-categories-original">主题</div><a class="unvisited-post" href="/post/20241203090000.html" title="2024年12月03日-Hexo安知鱼主题魔改记录">未读</a></div><a class="article-title" href="/post/20241203090000.html" title="2024年12月03日-Hexo安知鱼主题魔改记录">2024年12月03日-Hexo安知鱼主题魔改记录</a></div><div class="article-meta-wrap"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days" style="font-size: 15px; display:none"></i><span class="article-meta-label">发表于</span><time datetime="2024-12-03T01:00:00.000Z" title="发表于 2024-12-03 09:00:00" time="2024-12-03 09:00:00">2024-12-03</time><time class="time_hidden" datetime="2025-02-06T05:33:22.000Z" title="更新于 2025-02-06 13:33:22" time="2025-02-06 13:33:22">2025-02-06</time></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%B8%BB%E9%A2%98/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>主题</span></a><a class="article-meta__tags" href="/tags/%E9%AD%94%E6%94%B9/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>魔改</span></a></span></div><div class="content">1. 即刻短文1.1 多行文本1.1.1 效果展示
1.1.2 开始修改找到 /themes/anzhiyu/layout/includes/page/essay.pug文件，将
p.datacont= item.content
修改为
div.datacont
  each paragraph in item.content.split("\n")
    if paragraph.trim() !== ""
      p= paragraph
修改时注意代码缩进，p与div同级替换。
essay.yml文件中，使用 | 保证段落换行，例如：
essay_list:
  - content: |
      你求名利，他卜吉凶，可怜我全无心肝，怎出得什么主意？
      殿遏烟云，堂列钟鼎，堪笑人供此泥木，空费了许多钱财。
    date: 2024/11/27 9:25:00

不想换行就按照之前的说说样式写就行，或者|只写一行
2. 侧边栏2.1 steam游戏卡片参考：李小白的博客-侧边栏steam卡片
2.1.1 效果展示
2.1.1 开始修改跳转到 steam 卡片生 ...</div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/post/20241120092654.html')"><div class="post_cover left"><a href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器" style="display: flex;height: 100%;"><img class="post_bg" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024112001/top.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年11月20日-RetroArch全能游戏模拟器" style="pointer-events: none"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><div class="article-categories-original">游戏</div><a class="unvisited-post" href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器">未读</a></div><a class="article-title" href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器">2024年11月20日-RetroArch全能游戏模拟器</a></div><div class="article-meta-wrap"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days" style="font-size: 15px; display:none"></i><span class="article-meta-label">发表于</span><time datetime="2024-11-20T01:26:54.000Z" title="发表于 2024-11-20 09:26:54" time="2024-11-20 09:26:54">2024-11-20</time><time class="time_hidden" datetime="2025-04-18T03:00:00.000Z" title="更新于 2025-04-18 11:00:00" time="2025-04-18 11:00:00">2025-04-18</time></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E6%97%A5%E5%B8%B8/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>日常</span></a><a class="article-meta__tags" href="/tags/%E6%8C%87%E5%8D%97/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>指南</span></a><a class="article-meta__tags" href="/tags/%E6%B8%B8%E6%88%8F/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>游戏</span></a><a class="article-meta__tags" href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>模拟器</span></a></span></div><div class="content">

本文根据我目前已知的信息撰写，如有误请直接指出，望各位不吝赐教。
非常有意思的是，如果你不是为了整理自己的ROM库，那么完全可以使用合集包，也不用再看下面这么繁琐的教程了。


1. 前言众所周知，很多游戏的玩法并不过时，但运行这些游戏的平台和设备可能已经非常陈旧了。有些设备和平台已经彻底停产，只能通过二手渠道购买（非常贵），有些甚至连二手都无处可寻。所以，想要重温这些经典游戏，通过模拟器来实现是最划算的。
另外，我在家想玩游戏模拟器时，肯定会选择PC或者平板，大屏设备总是能带来更好的体验；出门在外的闲暇时光，则直接使用手机游玩。因此，我更加需要一个能够跨平台的模拟器，且存档文件能够共享。虽然RetroArch无法直接在软件中设置云存档，但在拥有群晖NAS的情况下，存档同步问题就再简单不过了。
2. RetroArch 模拟器RetroArch 是一个非常强大的开源模拟器前端，支持在线下载核心文件。模拟器通常分为前端和核心两个部分：前端主要是UI界面，而核心才是真正负责运行游戏的部分。如果你对某些软件领域的特定术语不熟悉，也无需深入理解，可以简单地将 RetroArch 理解为一款 ...</div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/post/20241010144421.html')"><div class="post_cover left"><a href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南" style="display: flex;height: 100%;"><img class="post_bg" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024101001/top.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年10月10日-流量卡选购指南" style="pointer-events: none"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><div class="article-categories-original">日常</div><a class="unvisited-post" href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南">未读</a></div><a class="article-title" href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南">2024年10月10日-流量卡选购指南</a></div><div class="article-meta-wrap"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days" style="font-size: 15px; display:none"></i><span class="article-meta-label">发表于</span><time datetime="2024-10-10T06:44:21.000Z" title="发表于 2024-10-10 14:44:21" time="2024-10-10 14:44:21">2024-10-10</time><time class="time_hidden" datetime="2025-02-17T07:03:54.000Z" title="更新于 2025-02-17 15:03:54" time="2025-02-17 15:03:54">2025-02-17</time></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E6%97%A5%E5%B8%B8/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>日常</span></a><a class="article-meta__tags" href="/tags/%E6%8C%87%E5%8D%97/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>指南</span></a></span></div><div class="content">
注意，2025年2月中旬起，好像各大运营商又出比较高质量的流量卡套餐了，各位可以去瞅瞅。


本文仅作为科普文章，本人不参与其中任何交易环节，如果有任何问题都可以通过社交软件联系我，我非常乐意提供力所能及的帮助。


非常遗憾的是，截止到本文的写作开始，最近一次的三网竞合已经结束，现在已经选购不到我副卡2的这种套餐了，现在大都是29元80G流量的套餐，优惠力度已然大减。

当前情况我总共拥有4张SIM卡，每张卡都有不同的用途，并且每张卡都是正规运营商的卡，且和身份信息绑定，不存在违规违法的情况。

主卡联通卡1：月租8元，早先申请的联通大王卡，后来加了一张流量卡就把这张主卡的套餐更换为了联通8元保号套餐。是联系我的最终方式，其中绑定了许多网站、银行，基本无法舍弃。

副卡电信流量卡2：月租19元，是前几年申请的湖北流量卡，主套餐是29元星卡，但是叠加了125G的全国流量和30G的定向流量，且可以参与充100反220（每月反10）的活动，活动返完可继续参与，所以综合月租是19元。是我没有WIFI的情况下主要的上网方式，实际上每个月根本用不了这么多。

宽带卡移动卡3：月租33元，8月租 ...</div></div></div><div class="recent-post-item" onclick="pjax.loadUrl('/post/20240902145411.html')"><div class="post_cover left"><a href="/post/20240902145411.html" title="2024年09月02日-大抵只有我被困在那段时光里了" style="display: flex;height: 100%;"><img class="post_bg" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024090201/top.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年09月02日-大抵只有我被困在那段时光里了" style="pointer-events: none"></a></div><div class="recent-post-info"><div class="recent-post-info-top"><div class="recent-post-info-top-tips"><div class="article-categories-original">日常</div><a class="unvisited-post" href="/post/20240902145411.html" title="2024年09月02日-大抵只有我被困在那段时光里了">未读</a></div><a class="article-title" href="/post/20240902145411.html" title="2024年09月02日-大抵只有我被困在那段时光里了">2024年09月02日-大抵只有我被困在那段时光里了</a></div><div class="article-meta-wrap"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days" style="font-size: 15px; display:none"></i><span class="article-meta-label">发表于</span><time datetime="2024-09-02T06:54:11.000Z" title="发表于 2024-09-02 14:54:11" time="2024-09-02 14:54:11">2024-09-02</time><time class="time_hidden" datetime="2024-11-07T07:00:41.000Z" title="更新于 2024-11-07 15:00:41" time="2024-11-07 15:00:41">2024-11-07</time></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E6%83%85%E7%BB%AA/" event.cancelbubble onclick="window.event.cancelBubble=true;"><span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>情绪</span></a></span></div><div class="content">高中时喜欢一个女生，从上大学起就没联系了，但个人单方面一直念念不忘。前段时间鼓起勇气要到了她的微信，冒犯地看了朋友圈，看了看最近动态，照片里的她依旧显得如此可爱。有些不同的是，这相片里的女生，我好像有些认不出来了。
印象当中她头发长长的，眼睛大大的，闪闪发亮，微微翘起的嘴角笑起来很甜很可爱。
如果不是很确定微信里的就是本人，可能让我重新看我大抵也是认不出来了(笑)。明明同样是在笑，可我总是感觉，她不如她。
可笑的是我的悸动在看到最近的照片之后突然有点停止了，然后再回忆她之前的样子，感觉区别好像有点太大了。
说来也奇怪，到现在为止，我接触过的女生里，她是唯一一个能让我念念不忘的人。算上年头今年应该八年了，我经常跟朋友开玩笑说，想忘记一个人最好的方法就是去跟她见一面，看看她最近的样子。可要是真见到了，又会感觉到惆怅。也会开始怀疑，这些年的时间，我究竟是在想念着她，还是在眷恋与她在学校独处时相笑的时光。
大抵只有我被困在那段时光里了，清晰记得，上晚修的前夕，我和她走在操场上，晚风轻拂，吹过我们的身旁。这一刻时间仿佛静止了一般，她看着操场，而我看着她，看着她在夕阳下的模样，我的心不自主的开始跳 ...</div></div></div><nav id="pagination"><div class="pagination"><span class="page-number current">1</span><div class="toPageGroup"><input id="toPageText" oninput="value=value.replace(/[^0-9]/g,'')" maxlength="3" onkeyup="this.value=this.value.replace(/[^u4e00-u9fa5w]/g,'')" aria-label="toPage"><a id="toPageButton" onclick="anzhiyu.toPage()"><i class="anzhiyufont anzhiyu-icon-angles-right" style="font-weight: inherit; font-size: 1rem;"></i></a></div></div></nav></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="card-content"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div><div class="author-info-avatar"><img class="avatar-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/><div class="author-status"><img class="g-status" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="https://bu.dusays.com/2021/03/03/c50ad1cd452e8.png" alt="status"/></div></div><div class="author-info__description"><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">欢迎访问我的博客，这里是我分享<b style="color:#fff">生活</b>的地方，另外还有一些技能和经验的<b style="color:#fff">记录</b>。</div><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">希望可以帮助到你。</div></div><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">c2hapmll</h1><div class="author-info__desc"></div></a><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://steamcommunity.com/profiles/76561199172600200/" target="_blank" title="Steam"><svg class="icon faa-tada" aria-hidden="true"><use xlink:href="#icon-steam"></use></svg></a></div></div></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bullhorn anzhiyu-shake"></i><span>公告</span></div><div class="announcement_content">刚从Halo迁移至Hexo，还在调整中，欢迎访问~ <br/> 本站开放时间为：<b>8:30-23:00</b>，可能会提前关站哦~ <br/> 历史更新请点击<a href="https://shijie.icu/update/"><b>博客更新日志</b></a> <br/></div></div><div class="card-widget card-countdown"><div class="item-headline"><i></i><span></span></div><div class="item-content"><div class="cd-count-left">
  <span class="cd-text">距离</span>
  <span class="cd-name" id="eventName"></span>
  <span class="cd-time" id="daysUntil"></span>
  <span class="cd-date" id="eventDate"></span>
</div>
<div id="countRight" class="cd-count-right"></div>
</div></div><div class="card-widget music-widget"><div class="item-headline"><i></i><span></span></div><div class="item-content"><div class="music-widget">
  <div id="musicStatus" class="music-status"></div>
  <img id="musicCover" class="music-cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="" alt="专辑封面">
</div>
<div class="music-info">
  <div id="musicTitle" class="music-title"></div>
  <div id="musicArtist" class="music-artist"></div>
  <div id="musicAlbum" class="music-album"></div>
</div>
</div></div><div class="card-widget card-categories"><div class="item-headline">
            <i class="anzhiyufont anzhiyu-icon-folder-open"></i>
            <span>分类</span>
            
            </div>
            <ul class="card-category-list" id="aside-cat-list">
            <li class="card-category-list-item "><a class="card-category-list-link" href="/categories/%E4%B8%BB%E9%A2%98/"><span class="card-category-list-name">主题</span><span class="card-category-list-count">1</span></a></li><li class="card-category-list-item "><a class="card-category-list-link" href="/categories/%E6%97%A5%E5%B8%B8/"><span class="card-category-list-name">日常</span><span class="card-category-list-count">3</span></a></li><li class="card-category-list-item "><a class="card-category-list-link" href="/categories/%E6%B8%B8%E6%88%8F/"><span class="card-category-list-name">游戏</span><span class="card-category-list-count">2</span></a></li>
            </ul></div><div class="sticky_layout"><div class="card-widget"><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 1.05rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 1.05rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 1.05rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 1.05rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 1.05rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 1.05rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 1.05rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 1.05rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 1.05rem;">魔改<sup>1</sup></a></div></div><hr/><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/02/"><span class="card-archive-list-date">二月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/12/"><span class="card-archive-list-date">十二月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/11/"><span class="card-archive-list-date">十一月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/10/"><span class="card-archive-list-date">十月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/09/"><span class="card-archive-list-date">九月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li></ul></div><hr/><div class="card-webinfo"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-chart-line"></i><span>网站资讯</span></div><div class="webinfo"><div class="webinfo-item"><div class="webinfo-item-title"><i class="anzhiyufont anzhiyu-icon-file-lines"></i><div class="item-name">文章总数 :</div></div><div class="item-count">6</div></div><div class="webinfo-item"><div class="webinfo-item-title"><i class="anzhiyufont anzhiyu-icon-stopwatch"></i><div class="item-name">建站天数 :</div></div><div class="item-count" id="runtimeshow" data-publishDate="2021-03-31T16:00:00.000Z"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></div></div><div class="webinfo-item"><div class="webinfo-item-title"><i class="anzhiyufont anzhiyu-icon-font"></i><div class="item-name">全站字数 :</div></div><div class="item-count">9.6k</div></div><div class="webinfo-item"><div class="webinfo-item-title"><i class="anzhiyufont anzhiyu-icon-universal-access"></i><div class="item-name">总访客数 :</div></div><div class="item-count" id="busuanzi_value_site_uv"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></div></div><div class="webinfo-item"><div class="webinfo-item-title"><i class="anzhiyufont anzhiyu-icon-square-poll-vertical"></i><div class="item-name">总访问量 :</div></div><div class="item-count" id="busuanzi_value_site_pv"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></div></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">&copy;2024 - 2025 By <a class="footer-bar-link" href="/" title="c2hapmll" target="_blank">c2hapmll</a></div></div><div id="footer-type-tips"></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index" title="皖ICP备20000482号-2">皖ICP备20000482号-2</a><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.mps.gov.cn/#/query/webSearch" title="皖公网安备 34120402000423号">皖公网安备 34120402000423号</a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">6</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">9</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">3</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" target="_blank" rel="noopener" href="https://home.shijie.icu/" title="我的主页"><img class="back-menu-item-icon" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/32.png" alt="我的主页"/><span class="back-menu-item-text">我的主页</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 0.88rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 0.88rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 0.88rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 0.88rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 0.88rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 0.88rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 0.88rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 0.88rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 0.88rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><span id="loading-status"></span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="is-center" id="loading-database"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-pulse-icon"></i><span>  数据库加载中</span></div><div class="search-wrap"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div><hr/><div id="local-search-results"></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size: 1rem;"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://y.qq.com/n/ryqq/playlist/8802438608&quot;, &quot;_blank&quot;);" style="display: none;"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://cdn.cbd.int/instant.page@5.2.0/instantpage.js" type="module"></script><script src="https://cdn.cbd.int/vanilla-lazyload@17.8.5/dist/lazyload.iife.min.js"></script><script src="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.js"></script><canvas id="universe"></canvas><script async src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script async src="/anzhiyu/random.js"></script><script src="/js/search/local-search.js"></script><div class="js-pjax"><script>function subtitleType () {
  if (true) { 
    window.typed = new Typed("#subtitle", {
      strings: ["记录生活 &#124; 感受世界"],
      startDelay: 300,
      typeSpeed: 150,
      loop: true,
      backSpeed: 50
    })
  } else {
    document.getElementById("subtitle").innerHTML = '记录生活 &#124; 感受世界'
  }
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://cdn.cbd.int/typed.js@2.1.0/dist/typed.umd.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script><input type="hidden" name="page-type" id="page-type" value="anzhiyu"></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
        region: '',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script>var visitorMail = "";
</script><script async data-pjax src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script async src="https://at.alicdn.com/t/c/font_4729035_3m5int5al8c.js"></script><script src="/js/countdown.js"></script><script src="/js/getnowplaying.js"></script><script src="/js/badge.js"></script><script src="/js/update.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/aplayer/APlayer.min.css" media="print" onload="this.media='all'"><script src="https://cdn.cbd.int/anzhiyu-blog-static@1.0.1/js/APlayer.min.js"></script><script src="https://cdn.cbd.int/hexo-anzhiyu-music@1.0.1/assets/js/Meting2.min.js"></script><script src="https://cdn.cbd.int/pjax@0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: false,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script><script charset="UTF-8" src="https://cdn.cbd.int/anzhiyu-theme-static@1.1.5/accesskey/accesskey.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div></body></html>
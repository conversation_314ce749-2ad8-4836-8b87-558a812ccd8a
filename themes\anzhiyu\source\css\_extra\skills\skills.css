#skills-tags-group-all {
  display: flex;
  transform: rotate(0);
  transition: 0.3s;
}
#skills-tags-group-all .tags-group-wrapper {
  margin-top: 40px;
  display: flex;
  flex-wrap: nowrap;
  animation: rowup 60s linear infinite;
}
#skills-tags-group-all .tags-group-icon-pair {
  margin-left: 1rem;
}
#skills-tags-group-all .tags-group-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 66px;
  font-weight: 700;
  box-shadow: var(--anzhiyu-shadow-blackdeep);
  width: 120px;
  height: 120px;
  border-radius: 30px;
}
#skills-tags-group-all .tags-group-icon img {
  width: 60px;
  margin: 0 auto !important;
}
[data-theme="dark"] #skills-tags-group-all .tags-group-icon img {
  filter: none;
}
#skills-tags-group-all .tags-group-icon-pair .tags-group-icon:nth-child(even) {
  margin-top: 1rem;
  transform: translate(-60px);
}

#skills-tags-group-all .etc {
  margin-right: 10px;
  margin-top: 10px;
}

@keyframes rowup {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

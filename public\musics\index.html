<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"><title>音乐世界 | 我的博客</title><meta name="author" content="c2hapmll"><meta name="copyright" content="c2hapmll"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f7f9fe"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="音乐世界"><meta name="application-name" content="音乐世界"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f7f9fe"><meta property="og:type" content="website"><meta property="og:title" content="音乐世界"><meta property="og:url" content="https://shijie.icu/musics/index.html"><meta property="og:site_name" content="我的博客"><meta property="og:description" content="音乐世界"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://shijie.icu/img/default_cover.jpg"><meta property="article:author" content="c2hapmll"><meta property="article:tag"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://shijie.icu/img/default_cover.jpg"><meta name="description" content="音乐世界"><link rel="shortcut icon" href="/images/themes/32.png"><link rel="canonical" href="https://shijie.icu/musics/"><link rel="preconnect" href="//cdn.cbd.int"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><meta name="google-site-verification" content="xxx"/><meta name="baidu-site-verification" content="code-xxx"/><meta name="msvalidate.01" content="xxx"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.cbd.int/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media='all'"><script>const GLOBAL_CONFIG = {
  linkPageTop: undefined,
  peoplecanvas: undefined,
  postHeadAiDescription: undefined,
  diytitle: undefined,
  LA51: undefined,
  greetingBox: undefined,
  twikooEnvId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
  commentBarrageConfig:undefined,
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: false,
  mainTone: undefined,
  authorStatus: {"skills":["🤖️ 数码科技爱好者","🔍 分享与热心帮助","🏠 智能家居小能手"]},
  algolia: undefined,
  localSearch: {"path":"/search.xml","preload":true,"languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: undefined,
  highlight: {"plugin":"prismjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    simplehomepage: true,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: c2hapmll","link":"链接: ","source":"来源: 我的博客","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: false,
  shortcutKey: undefined,
  autoDarkmode: true
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  configTitle: '我的博客',
  title: '音乐世界',
  postAI: '',
  pageFillDescription: '音乐世界',
  isPost: false,
  isHome: false,
  isHighlightShrink: false,
  isToc: false,
  postUpdate: '2024-11-12 13:58:10',
  postMainColor: '',
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#18171d')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f7f9fe')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/costom_widget.css"><link rel="stylesheet" href="/css/badge.css"><meta name="generator" content="Hexo 7.3.0"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="/images/themes/32.png"/><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"/><script async="async" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="page" id="body-wrap"><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><a id="site-name" href="/" accesskey="h"><div class="title">我的博客</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"/><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole();"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" alt="微信" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/wechat.jpg"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" alt="支付宝" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/alipay.jpg"/></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title"> 最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 1.05rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 1.05rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 1.05rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 1.05rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 1.05rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 1.05rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 1.05rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 1.05rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 1.05rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/02/"><span class="card-archive-list-date">二月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/12/"><span class="card-archive-list-date">十二月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/11/"><span class="card-archive-list-date">十一月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/10/"><span class="card-archive-list-date">十月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/09/"><span class="card-archive-list-date">九月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li></ul></div><hr/></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout hide-aside" id="content-inner"><div id="page"><h1 class="page-title">音乐世界</h1><div id="musics"><div class="author-content author-content-item musicsPage single" style="background: url(/images/musics/musics_top.jpg) left 37% / cover no-repeat !important;"><div class="card-content"><div class="author-content-item-tips">音乐世界</div><span class="author-content-item-title">聆听触动心灵的声音</span><div class="content-bottom"><div class="tips">三界 四洲 无所求 不可救，长夜 今朝 是非黑白 颠倒</div></div><div class="banner-button-group"><a class="banner-button" target="_blank" rel="noopener" href="https://www.hifini.com/"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right" style="font-size: 1.3rem"></i><span class="banner-button-text">HIFINI</span></a></div></div></div><div class="goodmusics-item"><h2 class="goodmusics-title">正在听de 音乐和专辑</h2><div class="goodmusics-item-description"></div><div class="musics-item"><div class="musics-item-content"><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/002wGjPc1oHGpt" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220018.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="百年孤寂"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;百年孤寂&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【百年孤寂】&quot;);" title="百年孤寂">百年孤寂</div><div class="musics-item-content-item-specification">王菲 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">一百年后 没有你 也没有我</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/0029B8mN2eOjhw" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220017.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="爱人错过"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;爱人错过&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【爱人错过】&quot;);" title="爱人错过">爱人错过</div><div class="musics-item-content-item-specification">告五人 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">我肯定在几百年前就说过爱你 只是你忘了</div></div></div></div></div></div><div class="goodmusics-item"><h2 class="goodmusics-title">听过的音乐（16）</h2><div class="goodmusics-item-description"></div><div class="musics-item"><div class="musics-item-content"><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/0029HIG72th1A5" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/2025/202504220101.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="黄梅戏"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;黄梅戏&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【黄梅戏】&quot;);" title="黄梅戏">黄梅戏</div><div class="musics-item-content-item-specification">慕容晓晓 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">为救李郎离家园，谁料皇榜中状元</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/002wGjPc1oHGpt" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220018.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="百年孤寂"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;百年孤寂&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【百年孤寂】&quot;);" title="百年孤寂">百年孤寂</div><div class="musics-item-content-item-specification">王菲 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">一百年后 没有你 也没有我</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/0029B8mN2eOjhw" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220017.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="爱人错过"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;爱人错过&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【爱人错过】&quot;);" title="爱人错过">爱人错过</div><div class="musics-item-content-item-specification">告五人 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">我肯定在几百年前就说过爱你 只是你忘了</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://www.bilibili.com/video/BV1Lb411r7JL" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220016.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="达尼亚"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;达尼亚&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【达尼亚】&quot;);" title="达尼亚">达尼亚</div><div class="musics-item-content-item-specification">谭晶 / 翻唱 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">好听</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/003UyFpC3buJwd" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220015.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="慢慢"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;慢慢&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【慢慢】&quot;);" title="慢慢">慢慢</div><div class="musics-item-content-item-specification">谭晶 / 翻唱 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">无敌</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/0014ykcj2SHFlN" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220014.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="九儿"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;九儿&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【九儿】&quot;);" title="九儿">九儿</div><div class="musics-item-content-item-specification">谭晶 / 翻唱 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">无敌</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://www.bilibili.com/video/BV1u84y1W7Kw" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220013.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="欲水"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;欲水&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【欲水】&quot;);" title="欲水">欲水</div><div class="musics-item-content-item-specification">谭晶 / 翻唱 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">无敌</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/004CnFw70aM9Cu" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220012.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="贝加尔湖畔"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;贝加尔湖畔&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【贝加尔湖畔】&quot;);" title="贝加尔湖畔">贝加尔湖畔</div><div class="musics-item-content-item-specification">谭晶 / 翻唱 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">如听仙乐</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/003XMyG91NpwQ1" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220011.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="奢香夫人"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;奢香夫人&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【奢香夫人】&quot;);" title="奢香夫人">奢香夫人</div><div class="musics-item-content-item-specification">凤凰传奇 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">凤凰传奇这首歌出的很早了，但现在听歌兴趣都被音乐软件掌控了，怎么才能跳出程序预设？</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/004eKy6z0rtuPO" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220009.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="Fly Away"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;Fly Away&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【Fly Away】&quot;);" title="Fly Away">Fly Away</div><div class="musics-item-content-item-specification">G.E.M.邓紫棋 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">我没找到单曲的图，只有和专辑一样了</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/000Fz7zP3FDuSz" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220009.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="摩天动物园"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;摩天动物园&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【摩天动物园】&quot;);" title="摩天动物园">摩天动物园</div><div class="musics-item-content-item-specification">G.E.M.邓紫棋 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">近似说唱的形式，挺带感的</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/004GK4aP4TGbbG" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220008.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="Love Story"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;Love Story&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【Love Story】&quot;);" title="Love Story">Love Story</div><div class="musics-item-content-item-specification">Taylor Swift / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">似乎所有的音乐，现场总是比录音更加动人</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://www.bilibili.com/video/BV12a41147UE" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220007.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="怨苍天变了心"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;怨苍天变了心&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【怨苍天变了心】&quot;);" title="怨苍天变了心">怨苍天变了心</div><div class="musics-item-content-item-specification">谭晶 / 翻唱 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">如果让我遇见你，而你正当年轻，用最真的心换你最深的情</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/004R7qfh1ALctJ" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220006.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="危险派对"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;危险派对&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【危险派对】&quot;);" title="危险派对">危险派对</div><div class="musics-item-content-item-specification">王以太、刘至佳 / ⭐⭐⭐⭐</div><div class="musics-item-content-item-description">节奏很不错，写代码常听</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/003nOlBr1LAT8e" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220005.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="屁"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;屁&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【屁】&quot;);" title="屁">屁</div><div class="musics-item-content-item-specification">者来女 / 游戏科学 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">既见未来，为何不拜。</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/001WZvRd3QL051" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220004.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="看见"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;看见&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【看见】&quot;);" title="看见">看见</div><div class="musics-item-content-item-specification">陈鸿宇 / 游戏科学 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">参禅百年，不敌贪心一瞬，放不下的又何止一件袈裟？</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/003UTVCN0QvffG" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220003.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="New Boy"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;New Boy&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【New Boy】&quot;);" title="New Boy">New Boy</div><div class="musics-item-content-item-specification">房东的猫 / 翻唱 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">既复古又时尚，很活泼，很阳光</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://www.bilibili.com/video/BV1ao4y197Fn" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220002.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="赤伶"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;赤伶&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【赤伶】&quot;);" title="赤伶">赤伶</div><div class="musics-item-content-item-specification">谭晶 / 翻唱 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">翻唱歌曲，国家队，非常好听</div></div></div><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/songDetail/003uzbfx0ltShX" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/music/2220001.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="Bones"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;Bones&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【Bones】&quot;);" title="Bones">Bones</div><div class="musics-item-content-item-specification">Imagine Dragons / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">梦龙乐队经典曲目</div></div></div></div></div></div><div class="goodmusics-item"><h2 class="goodmusics-title">听过的专辑</h2><div class="goodmusics-item-description"></div><div class="musics-item"><div class="musics-item-content"><div class="musics-item-content-item"><div class="musics-item-content-item-cover"><a class="music-link" href="https://y.qq.com/n/ryqq/albumDetail/0049MVh824D7bM" target="_blank"><img class="musics-item-content-item-image music-image" data-lazy-src="/images/musics/album/2230001.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="摩天动物园"></a></div><div class="musics-item-content-item-info"><div class="musics-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;摩天动物园&quot;);anzhiyu.snackbarShow(&quot;已复制音乐名： 【摩天动物园】&quot;);" title="摩天动物园">摩天动物园</div><div class="musics-item-content-item-specification">G.E.M. 邓紫棋 / ⭐⭐⭐⭐⭐</div><div class="musics-item-content-item-description">专辑内的所有曲目我都很喜欢听</div></div></div></div></div></div></div><hr/><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)">匿名评论</a><a href="/privacy" style="margin-left: 4px">隐私政策</a></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div></div></div><div class="comment-barrage"></div></div></div></main><footer id="footer"><div id="footer-wrap"></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">&copy;2024 - 2025 By <a class="footer-bar-link" href="/" title="c2hapmll" target="_blank">c2hapmll</a></div></div><div id="footer-type-tips"></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index" title="皖ICP备20000482号-2">皖ICP备20000482号-2</a><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.mps.gov.cn/#/query/webSearch" title="皖公网安备 34120402000423号">皖公网安备 34120402000423号</a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">6</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">9</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">3</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" target="_blank" rel="noopener" href="https://home.shijie.icu/" title="我的主页"><img class="back-menu-item-icon" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/32.png" alt="我的主页"/><span class="back-menu-item-text">我的主页</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 0.88rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 0.88rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 0.88rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 0.88rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 0.88rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 0.88rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 0.88rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 0.88rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 0.88rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button></div><div id="rightside-config-show"><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><a id="to_comment" href="#post-comment" title="直达评论"><i class="anzhiyufont anzhiyu-icon-comments"></i></a><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><span id="loading-status"></span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="is-center" id="loading-database"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-pulse-icon"></i><span>  数据库加载中</span></div><div class="search-wrap"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div><hr/><div id="local-search-results"></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size: 1rem;"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://y.qq.com/n/ryqq/playlist/8802438608&quot;, &quot;_blank&quot;);" style="display: none;"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://cdn.cbd.int/instant.page@5.2.0/instantpage.js" type="module"></script><script src="https://cdn.cbd.int/vanilla-lazyload@17.8.5/dist/lazyload.iife.min.js"></script><script src="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.js"></script><canvas id="universe"></canvas><script async src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script async src="/anzhiyu/random.js"></script><script src="/js/search/local-search.js"></script><div class="js-pjax"><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="musics"></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
        region: '',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script>var visitorMail = "";
</script><script async data-pjax src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script async src="https://at.alicdn.com/t/c/font_4729035_3m5int5al8c.js"></script><script src="/js/countdown.js"></script><script src="/js/getnowplaying.js"></script><script src="/js/badge.js"></script><script src="/js/update.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/aplayer/APlayer.min.css" media="print" onload="this.media='all'"><script src="https://cdn.cbd.int/anzhiyu-blog-static@1.0.1/js/APlayer.min.js"></script><script src="https://cdn.cbd.int/hexo-anzhiyu-music@1.0.1/assets/js/Meting2.min.js"></script><script src="https://cdn.cbd.int/pjax@0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: false,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script><script charset="UTF-8" src="https://cdn.cbd.int/anzhiyu-theme-static@1.1.5/accesskey/accesskey.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div></body></html>
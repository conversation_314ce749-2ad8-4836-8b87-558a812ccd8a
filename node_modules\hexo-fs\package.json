{"name": "hexo-fs", "version": "4.1.3", "description": "File system module for Hexo.", "main": "./dist/fs.js", "scripts": {"prepublish ": "npm install && npm run clean && npm run build", "build": "tsc -b", "clean": "tsc -b --clean", "eslint": "eslint .", "test": "mocha test/index.ts --require ts-node/register", "test-cov": "c8 --reporter=lcovonly npm run test"}, "files": ["dist/**"], "types": "./dist/fs.d.ts", "repository": "hexojs/hexo-fs", "homepage": "https://hexo.io/", "keywords": ["file", "file system", "fs", "hexo"], "author": "<PERSON> <<EMAIL>> (https://zespia.tw)", "maintainers": ["<PERSON><PERSON> <<EMAIL>> (https://abnerchou.me)"], "license": "MIT", "dependencies": {"bluebird": "^3.7.2", "chokidar": "^3.5.3", "graceful-fs": "^4.2.10", "hexo-util": "^3.0.1"}, "devDependencies": {"@types/bluebird": "^3.5.36", "@types/chai": "^4.3.12", "@types/graceful-fs": "^4.1.5", "@types/mocha": "^10.0.6", "@types/node": "^18.7.16 <18.19.9", "c8": "^9.1.0", "chai": "^4.3.6", "eslint": "^8.23.0", "eslint-config-hexo": "^5.0.0", "eslint-plugin-import": "^2.26.0", "mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.3"}, "engines": {"node": ">=14"}}
- const { envId, region, option } = theme.twikoo
- const { use, lazyload, count } = theme.comments

script.
  (() => {
    const init = () => {
      twikoo.init(Object.assign({
        el: '#twikoo-wrap',
        envId: '!{envId}',
        region: '!{region}',
        onCommentLoaded: () => {
          anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
        }
      }, !{JSON.stringify(option)}))
    }

    const loadTwikoo = () => {
      if (typeof twikoo === 'object') setTimeout(runFn,0)
      else getScript('!{url_for(theme.asset.twikoo)}').then(runFn)
    }

    const getCount = () => {
      const countELement = document.getElementById('twikoo-count')
      if(!countELement) return
      twikoo.getCommentsCount({
        envId: '!{envId}',
        region: '!{region}',
        urls: [window.location.pathname],
        includeReply: false
      }).then(res => {
        countELement.textContent = res[0].count
      }).catch(err => {
        console.error(err)
      })
    }

    const runFn = () => {
      init();
      !{count ? 'GLOBAL_CONFIG_SITE.isPost && getCount()' : ''}
    }

    if ('!{use[0]}' === 'Twikoo' || !!{lazyload}) {
      if (!{lazyload}) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
      else {
        loadTwikoo()
      }
    } else {
      window.loadOtherComment = loadTwikoo
    }
  })()
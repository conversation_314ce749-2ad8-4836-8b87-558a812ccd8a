const NowPlayingMusic = (() => {
    const config = {
        // hexo框架无后台，仅用于获取当前播放音乐，其他接口没什么用，各位亲朋请勿滥用
        apiKey: '0c3c7200137807bda2042d589a05863d',
        username: 'c2hapmll'
    };

    function fetchNowPlaying() {
        fetch(`https://ws.audioscrobbler.com/2.0/?method=user.getrecenttracks&user=${config.username}&api_key=${config.apiKey}&format=json`)
            .then(response => response.json())
            .then(data => {
                const track = data.recenttracks.track[0];
                if (track['@attr'] && track['@attr'].nowplaying) {
                    const songName = track.name;
                    updateSidebar(songName);
                } else {
                    updateSidebar('当前不在听歌');
                }
            })
            .catch(error => {
                console.error('获取当前播放音乐失败：', error);
                updateSidebar('获取失败');
            });
    }

    function updateSidebar(status) {
        const elements = ['musicStatus']
            .map(id => document.getElementById(id));

        if (elements.some(el => !el)) return;

        const [musicStatus] = elements;

        musicStatus.innerHTML = `~🎸博主正在听🎸~<br>${status}`;
    }

    function injectStyles() {
        const styles = `
            .music-widget {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 60px;
                box-sizing: border-box;
                overflow: hidden;
            }
            #musicStatus {
                font-size: 14px;
                font-weight: bold;
                text-align: center;
                color: #000;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
                display: block;
                line-height: 1.5;
                box-sizing: border-box;
                padding: 0 10px;
            }
        `;

        const styleSheet = document.createElement("style");
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    let timer;
    const start = () => {
        injectStyles();
        fetchNowPlaying();
        timer = setInterval(fetchNowPlaying, 180000); // 每 3 分钟更新一次
    };

    ['pjax:complete', 'DOMContentLoaded'].forEach(event => document.addEventListener(event, start));
    document.addEventListener('pjax:send', () => timer && clearInterval(timer));

    return { start, stop: () => timer && clearInterval(timer) };
})();

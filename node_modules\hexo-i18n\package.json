{"name": "hexo-i18n", "version": "2.0.0", "description": "i18n module for Hexo.", "main": "dist/i18n.js", "scripts": {"prepublish ": "npm run clean && npm run build", "build": "tsc -b", "clean": "tsc -b --clean", "eslint": "eslint .", "pretest": "npm run clean && npm run build", "test": "mocha test/index.js --require ts-node/register", "test-cov": "c8 --reporter=lcovonly npm run test"}, "directories": {"lib": "./lib"}, "files": ["dist/**"], "types": "./dist/i18n.d.ts", "engines": {"node": ">=14"}, "repository": "hexojs/hexo-i18n", "homepage": "https://hexo.io/", "keywords": ["hexo", "i18n", "localization"], "author": "<PERSON> <<EMAIL>> (https://zespia.tw)", "license": "MIT", "dependencies": {"sprintf-js": "^1.1.2"}, "devDependencies": {"@types/node": "^18.11.8", "@types/sprintf-js": "^1.1.2", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "c8": "^7.12.0", "chai": "^4.3.6", "eslint": "^8.26.0", "eslint-config-hexo": "^5.0.0", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "^4.8.4"}}
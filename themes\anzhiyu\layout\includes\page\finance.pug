#finance
  if site.data.finance
    each i in site.data.finance
      .author-content.author-content-item.financePage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          .banner-button-group
            a.banner-button(href=i.buttonLink)
              i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
              span.banner-button-text=i.buttonText
      each item in i.finance_all
        .finance_all-item
          h2.finance_all-title= item.title
          .finance_all-item-description= item.description
          .finance-item
            .finance-item-content
              each iten, indey in item.finance_list
                .finance-item-content-item
                  .finance-item-content-item-cover
                    if iten.back_image
                      .flip-card-inner
                        .flip-card-front
                          img.finance-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                        .flip-card-back
                          img.finance-item-back-image(data-lazy-src=url_for(iten.back_image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                    else
                      img.finance-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                  .finance-item-content-item-info
                    //- .finance-item-content-item-name(onclick=`rm.rightmenuCopyText("${iten.name}");anzhiyu.snackbarShow("${_p('已复制游戏名：') + " 【" + iten.name + "】"}");` title=iten.name)= iten.name
                    .finance-item-content-item-name
                      span.finance-item-month= iten.name
                      span.finance-item-expense= iten.specification
                    //- .finance-item-content-item-specification= iten.specification
                    .finance-item-content-item-description= iten.description
                    .finance-item-content-item-toolbar
                      if iten.link.includes('https://') || iten.link.includes('http://')
                        a.finance-item-content-item-link(href= iten.link, target='_blank') 详情
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message
                      else  
                        a.finance-item-content-item-link(href= iten.link, target='_blank') 查看文章
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message
#vcomment
  font-size: 1.1em

  .vbtn
    border: none
    background: var(--btn-bg)
    color: var(--btn-color)

    &:hover
      background: var(--btn-hover-color)

  .vimg
    transition: all .3s

    &:hover
      transform: rotate(360deg)

  .vcards .vcard .vcontent.expand
    &:before,
    &:after
      z-index: 22

#waline-wrap
  --waline-font-size: 1.1em
  --waline-theme-color: $button-bg
  --waline-active-color: $button-hover-color

  .vuser
    transition: all .5s

    &:hover
      transform: rotate(360deg)

if hexo-config('valine.bg')
  #vcomment
    textarea
      background: url(hexo-config('valine.bg')) 100% 100% no-repeat

      &:focus
        background-image: none

if hexo-config('waline.bg')
  #waline-wrap
    textarea
      background: url(hexo-config('waline.bg')) 100% 100% no-repeat

      &:focus
        background-image: none

.fireworks
  position: fixed
  top: 0
  left: 0
  z-index: $fireworks-zIndex
  pointer-events: none

.medium-zoom-image--opened
  z-index: 99999 !important
  margin: 0 !important

.medium-zoom-overlay
  z-index: 99999 !important

.mermaid-wrap
  margin: 0 0 20px
  text-align: center

  & > svg
    height: 100%

.fb-comments iframe
  width: 100% !important

.katex-wrap
  overflow: auto

  if hexo-config('katex') && hexo-config('katex.hide_scrollbar')
    &::-webkit-scrollbar
      display: none

// mathjax
mjx-container[display],
.has-jax
  overflow-x: auto
  overflow-y: hidden
  line-height: normal !important

.aplayer
  color: $font-black

#article-container
  .aplayer
    margin: 0 0 20px

    if hexo-config('beautify.enable')
      ol,
      ul
        margin: 0
        padding: 0

        li
          margin: 0
          padding: 0 15px

          &:before
            content: none

.snackbar-css
  border-radius: 5px !important
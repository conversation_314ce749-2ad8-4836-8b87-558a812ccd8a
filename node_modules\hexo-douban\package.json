{"name": "hexo-douban", "version": "2.3.6", "description": "Generate douban pages of books , movies and games for Hexo.", "main": "dist/index.js", "targets": {"js": {"source": "index.ts", "distDir": "dist/", "optimize": true}}, "scripts": {"build": "parcel build --no-source-maps"}, "dependencies": {"hexo-fs": "4.1.1", "hexo-log": "3.2.0", "idouban": "1.1.3"}, "repository": {"type": "git", "url": "git+https://github.com/mythsman/hexo-douban.git"}, "files": ["dist"], "keywords": ["hexo-plugin", "books", "movies", "games", "songs", "hexo", "douban", "idouban"], "author": "mythsman", "license": "MIT", "devDependencies": {"@types/hexo": "^3.8.12", "@types/hexo-log": "^0.2.6", "parcel": "^2.12.0"}, "bugs": {"url": "https://github.com/mythsman/hexo-douban/issues"}, "engines": {"node": ">= 18"}, "homepage": "https://github.com/mythsman/hexo-douban/"}
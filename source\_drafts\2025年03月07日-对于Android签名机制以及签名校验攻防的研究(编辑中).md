---
title: 对于Android签名机制以及签名校验攻防的研究
date: 2025-03-07 17:10:00
categories:
  - 工作
tags:
  - 技术
  - 调研
  - 逆向
  - 加固
cover:
---
> 切记，技术对抗不是手段，真正的方案是现实"攻击"，解决不了问题就解决你。

# 一：应用(APK)签名

Android应用主要使用 Android App Bundle 和 APK 两种打包方式，除Google Play市场使用的Android App Bundle(s)打包方式外，国内所有市场使用的都是APK(s)的打包方式，这里我们主要讨论APK文件的签名。

在Android官网中可以得到对于[应用签名的详细解释](https://source.android.google.cn/docs/security/features/apksigning?hl=fi)，通过应用签名，开发者可以标识应用创作者并更新其应用，而无需创建复杂的接口和权限。在 Android 平台上运行的每个应用都必须要[有开发者的签名](https://developer.android.google.cn/studio/publish/app-signing?hl=fi)。

也就是说无签名应用无法通过安装包管理器安装到手机上，这里验证应用是否签名是通过Android设备中的"安装包管理器"完成的，值得注意的是通过某些高权限操作可以略过安装包管理器，例如root情况下的"核心破解"xposed插件，可以无签名安装、降级安装等等操作。

再延申一下，"无签名应用"一般都是在对APK文件进行修改后出现的，并不是真正意义的无签名，而是签名相关信息被修改操作破坏了，对于安装包管理器来说这个APK已经不值得信任了，所以安装时会提示签名损坏或应用无签名。但是有个比较奇怪的规则，Android允许开发者进行自签名，也就是你修改完APK文件后再签一次名就能安装在手机上了。

目前Android签名有共5个版本，各个版本有不同的适用性，如表格所示：

| 签名方案                      | 引入时间       | 适用Android版本                   |
| ------------------------- | ---------- | ----------------------------- |
| APK signature scheme v1   | ALL        | 仅V1签名时，无法在Android 9版本及以上版本安装  |
| APK signature scheme v2   | Android 7  | 仅V2签名时，无法在Android 7版本及以下版本安装  |
| APK signature scheme v3   | Android 9  | 必须包含V2版本，在Android 9版本前不校验     |
| APK signature scheme v3.1 |            |                               |
| APK signature scheme v4   | Android 11 | 必须包含V2、V3版本，在Android 11版本前不校验 |

注意：
1. 从Android 9.0开始，Google要求所有上架Google Play的新APK必须同时使用v1和v2签名方案进行签名。
2. 注意，签名版本之间是不冲突的，可以V1+V2签名，也可以V1+V2+V3，但是不能中间隔一个版本，因为签名版本是向下兼容的，并且如果你要V3、V4签名就必须包含前面的版本直到V2为止，V2是V3、V4版本的基础。
3. 注意，Android系统在校验签名版本时，按照所支持的最高签名版本往下校验，例如当前Android 11支持V4签名，那么系统首先会判断APK是否存在V4签名，不存在则会继续向下。
4. 在高版本的Android系统中可以对一个APK进行两种不同的签名共存，但这个我还没研究过。


# 二：使用命令行对APK进行签名

早先使用jar签名的方式对APK进行V1签名，也就是jarsigner.jar包，但是这个东西实在是太过于久远了，现在一般都是使用apksigner.jar对APK签名。apksigner.jar在Android SDK中存在，如果你有Android Studio开发工具，可以在tools中的sdk目录中找到，如果没有就去Android官方直接下载一个SDK。尽量下载完整的Android SDK包，网上很多单独提供的apksigner.jar虽然也能用，但是非常不推荐。

> **注意**：如果您在使用 `apksigner` 为 APK 签名后又对 APK 做了更改，则 APK 的签名将会失效。如果您使用 [`zipalign`](https://developer.android.com/studio/command-line/zipalign?hl=zh-cn) 对齐 APK，请在为 APK 签名之前使用它。

`apksigner`常用命令如下，如果需要查看全部选项请直接调用该jar包，或查看[官方命令行工具教程](https://developer.android.com/tools/apksigner?hl=zh-cn)。

对应用签名：
```shell
apksigner sign --ks test.jks --ks-pass 123456 --ks-key-alias test --v1-signing-enabled=false --v2-signing-enabled=false --v3-signing-enabled=true --v4-signing-enabled=false --out out.apk src.apk
```

验证应用签名：
```shell
apksigner verify -v src.apk
```

# 三：对应用(APK)进行主动签名校验（本地）

以上可知，APK文件毫无安全性可言，签名信息仅用来显示开发者信息和系统内权限识别，不会对原始APK文件来源做任何保证(可以重签名安装)。这也是早期盗版应用、破解应用频出的原因之一。所以很多开发者会自行在APK中添加”签名校验“，将应用的签名信息提前存储在APK的某个地方，然后APK在运行时主动获取当前APK的签名信息，然后再与存储的信息一对比，如果不相同就退出应用，相同就继续运行，这样就可以判断是不是盗版应用了。能够有效防止被破解的应用运行，维护开发者的合法权益。

而更高级一点的校验方式就是完全不信任本地运行的所有代码，所有验证操作均通过后台服务器执行。比较著名的就是微信、QQ等应用了，早先存在一些修改了页面布局、美化了主题等魔改版的微信APK和QQ APK等，现在也逐渐消失殆尽了。

哎，这里可能就存在一个问题，APK签名是在所有打包操作完成后开始的，那么APK本身如何能提前获取签名信息并存储呢？这个先后顺序非常重要，决定了我们如何实现签名校验方案。

待续...
if hexo-config('darkmode.enable') || hexo-config('display_mode') == 'dark'
  [data-theme='dark']
    --global-bg: darken(#121212, 2)
    --font-color: alpha(#FFFFFF, .7)
    --hr-border: alpha(#FFFFFF, .4)
    --hr-before-color: alpha(#FFFFFF, .7)
    --search-bg: #121212
    --search-input-color: alpha(#FFFFFF, .7)
    --search-result-title: alpha(#FFFFFF, .9)
    --preloader-bg: darken(#121212, 2)
    --preloader-color: alpha(#FFFFFF, .7)
    --tab-border-color: #2c2c2c
    --tab-botton-bg: #2c2c2c
    --tab-botton-color: alpha(#FFFFFF, .7)
    --tab-button-hover-bg: lighten(#121212, 15)
    --tab-button-active-bg: #121212
    --card-bg: #121212
    --sidebar-bg: #121212
    --btn-hover-color: lighten(#121212, 40)
    --btn-color: alpha(#FFFFFF, .7)
    --btn-bg: lighten(#121212, 5)
    --text-bg-hover: lighten(#121212, 15)
    --light-grey: alpha(#FFFFFF, .7)
    --dark-grey: alpha(#FFFFFF, .2)
    --white: alpha(#FFFFFF, .9)
    --text-highlight-color: alpha(#FFFFFF, .9)
    --blockquote-color: alpha(#FFFFFF, .7)
    --blockquote-bg: lighten(#121212, 10)
    --reward-pop: lighten(#121212, 10)
    --toc-link-color: alpha(#FFFFFF, .6)
    --scrollbar-color: lighten(#121212, 5)
    --timeline-bg: lighten(#121212, 5)

    #web_bg:before,
    #footer:before,
    #page-header:before
      position: absolute
      width: 100%
      height: 100%
      background-color: alpha($dark-black, .3)
      content: ''
      z-index: 2
      pointer-events: none
    
    #footer
      background-color: var(--anzhiyu-card-bg);

    #article-container
      pre > code
        background: lighten(#121212, 2)

      figure.highlight
        box-shadow: none

      .note
        code
          background: $code-background

      .aplayer
        filter: brightness(.8)

      kbd
        border-color: #696969
        background-color: #525252
        color: #e2f1ff

    // 頭部
    #page-header
      &.nav-fixed > #nav,
      &.not-top-img > #nav
        background: var(--anzhiyu-black);
        box-shadow: 0 5px 6px -5px rgba(133, 133, 133, 0)
    .post
      #page-header
        &.nav-fixed > #nav
          background: var(--anzhiyu-card-bg);
          box-shadow: none
    #post-comment
      .comment-switch
        if hexo-config('comments.text')
          background: #2c2c2c !important

        #switch-btn
          filter: brightness(.8)

    // note
    if hexo-config('note.style') == 'modern' || hexo-config('note.style') == 'flat'
      .note
        filter: brightness(.8)

    // hide-tags
    .hide-button,
    .btn-anzhiyu,
    .hl-label,
    .post-outdate-notice,
    .error-img,
    #article-container iframe,
    .gist,
    .ads-wrap
      filter: brightness(.8)

    img
      if hexo-config('lazyload.enable') && hexo-config('lazyload.blur') && !hexo-config('lazyload.placeholder')
        filter: blur(0) brightness(.8)
      else
        filter: brightness(.8)

    #aside-content .aside-list > .aside-list-item:not(:last-child)
      border-bottom: 1px dashed alpha(#FFFFFF, .1)
    .post
      #aside-content
        +maxWidth768()
          background: var(--anzhiyu-card-bg);

    #artitalk_main #lazy
      background: #121212

    #operare_artitalk .c2
      background: #121212

    #card-toc
      +maxWidth900()
        background: lighten(#121212, 5)
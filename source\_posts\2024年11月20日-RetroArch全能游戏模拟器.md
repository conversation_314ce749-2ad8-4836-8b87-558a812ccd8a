---
title: 2024年11月20日-RetroArch全能游戏模拟器
date: 2024-11-20 09:26:54
updated: 2025-04-18 11:00:00
categories:
  - 游戏
tags:
  - 日常
  - 指南
  - 游戏
  - 模拟器
cover: /images/posts/2024112001/top.png
---

> 1. 本文根据我目前已知的信息撰写，如有误请直接指出，望各位不吝赐教。
> 2. 非常有意思的是，如果你不是为了整理自己的ROM库，那么完全可以使用合集包，也不用再看下面这么繁琐的教程了。

## 1. 前言

众所周知，很多游戏的玩法并不过时，但运行这些游戏的平台和设备可能已经非常陈旧了。有些设备和平台已经彻底停产，只能通过二手渠道购买（非常贵），有些甚至连二手都无处可寻。所以，想要重温这些经典游戏，通过模拟器来实现是最划算的。

另外，我在家想玩游戏模拟器时，肯定会选择PC或者平板，大屏设备总是能带来更好的体验；出门在外的闲暇时光，则直接使用手机游玩。因此，我更加需要一个能够跨平台的模拟器，且存档文件能够共享。虽然RetroArch无法直接在软件中设置云存档，但在拥有群晖NAS的情况下，存档同步问题就再简单不过了。

## 2. RetroArch 模拟器

RetroArch 是一个非常强大的开源模拟器前端，支持在线下载核心文件。模拟器通常分为前端和核心两个部分：前端主要是UI界面，而核心才是真正负责运行游戏的部分。如果你对某些软件领域的特定术语不熟悉，也无需深入理解，可以简单地将 RetroArch 理解为一款能够运行几乎所有怀旧游戏的强大软件，只需要在 RetroArch 中下载运行怀旧游戏所需的环境（核心）即可。

如果你的英文不错，可以直接查阅 RetroArch 模拟器官方文档：[https://docs.libretro.com/](https://docs.libretro.com/)，内容非常详尽。

### 2.1 下载安装

官网下载地址：[https://retroarch.com/?page=platforms](https://retroarch.com/?page=platforms)。你可以根据自己的需求选择对应平台的安装包进行下载，安装过程非常简单，这里不再赘述。需要注意的是，在 Windows 平台上有两种安装方式：

1. **直接下载安装包**：下载安装后即可运行。
2. **通过 Steam 安装**：在 Steam 平台上安装的 RetroArch 支持云存档，无需额外设置，这一功能是通过 Steam 的云存档服务实现的。

### 2.2 语言设置

RetroArch 安装后运行时可能是英文界面，如果需要切换为中文，可以按以下步骤操作：点击 ⚙（设置） -> User -> Language -> Chinese 简体中文。修改语言后，需要退出软件并重新打开才能完全生效。

### 2.3 核心下载

RetroArch 提供快捷菜单，因此有些功能的设置入口不止一种，建议多加熟悉。下面以我正在游玩的 GBA 游戏《宝可梦绿宝石》为例，讲解相关设置。

原版《宝可梦绿宝石》主要有两种语言版本：日版和美版，但由于对这两种语言不够熟悉，我选择了由 **漫游&TGB&口袋群星SP** 汉化的版本，这个版本被认为是原版绿宝石内容最好的汉化版本。

确定好游戏后，需要选择游戏对应的核心文件。《宝可梦绿宝石》属于 GBA 平台游戏，因此需要下载 GBA 核心才能游玩。RetroArch 提供多种 GBA 核心，可以自行搜索不同 GBA 核心的区别，mGBA 核心下载方法如下：点击 🏠 -> 在线更新 -> 核心下载 -> Nintendo - Game Boy Advance (mGBA)。

同一平台的不同核心在性能和功能上可能略有区别，如果想了解具体差异，可以在 GitHub 上搜索这些核心的项目文档进行查看。

### 2.4 开始游戏

注意，这里只是演示，实际操作时你需要根据自己想玩的游戏选择对应的核心，不要照抄。具体步骤如下：

1. 点击 **🏠 -> 加载核心 -> Nintendo - Game Boy Advance (mGBA)**
2. 然后选择 **加载游戏**，接着找到并选择你想玩的游戏文件。

当你看到游戏画面和模拟摇杆界面时，说明一切设置已经完成，接下来就可以开始愉快的怀旧游戏之旅了。

这是最简单的过程了，但实际上我们可能会遇到各种各样的问题，我会在 进阶设置 中讲解。

### 2.5 存档

RetroArch 模拟器的存档分为两种：游戏存档和状态存档。

- **游戏存档**：顾名思义，就是游戏本身的存档功能，保存的是游戏内的进度。
- **状态存档**：模拟器的即时保存功能，可以保存游戏当前的状态，比游戏存档更强大。

这两种存档对应的文件夹不同，所以如果想在不同平台上继续游玩同一款游戏的进度，就需要将这两个文件夹一起保存并迁移。

无论是哪种平台，存档文件夹的名称都是一致的，只需要将整个文件夹复制保存。具体位置如下：

- **状态存档**：`RetroArch 安装目录/states/`
- **游戏存档**：`RetroArch 安装目录/saves/`

我的建议是直接备份这两个文件夹，而不是单独备份状态文件或游戏存档文件，这样可以避免在迁移时无法识别存档。怕麻烦的话直接在Steam中使用RetroArch模拟器，就无需担心存档的保存问题了。

**！！！注意 ！！！**

各平台的游戏名称请保持一致，存档以文件名为准，如果修改游戏名可能不识别存档。

网上下载的游戏文件，一定要先测试游戏内的存档功能在模拟器上是否可用。如果你过于依赖模拟器的即时存档功能，而没有发现这个问题，就可能导致在开启二周目时无法正确读取存档，进度无法继续。这是因为有些游戏的汉化、特殊版本可能在模拟器中无法正常保存或读取进度，所以务必先确认游戏本身的存档功能正常后，再开始游玩。

## 3. 游戏资源下载

1. 老男人游戏网: https://www.oldmantvg.net/
2. 掌机迷: https://www.gbarom.cn/

## Q&A

1. RetroArch 安装好后打开不了/打开报错怎么办？
注意安装路径要为纯英文目录，保持良好安装习惯

2. RetroArch 中核心下载列表加载不出来/下载失败怎么办？
一般是因为运营商的屏蔽问题，Github访问不了一般也是这个原因，网络问题请自行解决。
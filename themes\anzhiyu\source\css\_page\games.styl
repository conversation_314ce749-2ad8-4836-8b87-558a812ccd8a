// 游戏世界
.goodgames-title
  margin: 1rem 0
  line-height: 1;
.games-item
  .games-item-content
    display: flex
    flex-direction: row
    flex-wrap: wrap
    margin: 0 -8px
    .games-item-content-item
      width: calc(25% - 12px)
      border-radius: 12px
      border: var(--style-border-always)
      overflow: hidden
      margin: 8px 6px
      background: var(--anzhiyu-card-bg)
      box-shadow: var(--anzhiyu-shadow-border)
      min-height: 400px
      position: relative
      +maxWidth1200()
        width: calc(50% - 12px)
      +maxWidth768()
        width: 100%
      
      .games-item-content-item-info
        padding: 8px 16px 16px 16px
        margin-top: 12px

      .games-item-content-item-name
        font-size: 18px
        font-weight: bold
        line-height: 1
        margin-bottom: 8px
        white-space: nowrap
        overflow: visable
        text-overflow: ellipsis
        width: fit-content
        cursor pointer
        &:hover
          color: var(--anzhiyu-main)

      .games-item-content-item-specification
        display: flex
        flex-wrap: wrap
        gap: 0.5rem
        margin: 0.5rem 0
        font-size: 0.9rem
        color: var(--anzhiyu-fontcolor)

      .games-item-content-item-description
        line-height: 20px
        color: var(--anzhiyu-secondtext)
        height: 60px
        display: -webkit-box
        overflow: hidden
        -webkit-line-clamp: 3
        -webkit-box-orient: vertical
        font-size: 14px

      a.games-item-content-item-link
        font-size: 12px
        background: var(--anzhiyu-gray-op)
        padding: 4px 8px
        border-radius: 8px
        cursor: pointer

        &:hover
          background: var(--anzhiyu-main)
          color: var(--anzhiyu-white)

      // 翻转卡片效果
      .games-item-content-item-cover
        position: relative
        width: 100%
        height: 200px
        perspective: 1000px // 创建3D效果

        // 使用外层容器的 `:hover`，避免翻转过程中动画重复触发
        &:hover .flip-card-inner
          transform: rotateY(-180deg)

        .flip-card-inner
          position: relative
          width: 100%
          height: 100%
          transition: transform 0.6s
          transform-style: preserve-3d
          transform: rotateY(0deg) // 初始状态为 0 度

          .flip-card-front, .flip-card-back
            position: absolute
            width: 100%
            height: 100%
            backface-visibility: hidden
            display: flex
            justify-content: center
            align-items: center

          .flip-card-front
            z-index: 2
            img.games-item-content-item-image
              object-fit: cover
              height: 100%
              width: 100%

          .flip-card-back
            transform: rotateY(-180deg)
            z-index: 1
            img.games-item-back-image
              object-fit: cover
              height: 100%
              width: 100%

      img.games-item-content-item-image
        object-fit: cover
        height: 100%
        width: 100%
       // border-radius: 0 
       // 若需要去除图片圆角可以将这里的注释去掉

      .games-item-content-item-toolbar
        display: flex
        justify-content: space-between
        position: absolute
        bottom: 12px
        left: 0
        width: 100%
        padding: 0 16px

body[data-type="games"] #web_bg
  background: var(--anzhiyu-background);
body[data-type="games"] #page
  border: 0;
  box-shadow: none !important;
  padding: 0 !important;
  background: 0 0 !important;
body[data-type="games"] #page .page-title
  display: none;
<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"><title>游戏世界 | 我的博客</title><meta name="author" content="c2hapmll"><meta name="copyright" content="c2hapmll"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f7f9fe"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="游戏世界"><meta name="application-name" content="游戏世界"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f7f9fe"><meta property="og:type" content="website"><meta property="og:title" content="游戏世界"><meta property="og:url" content="https://shijie.icu/games/index.html"><meta property="og:site_name" content="我的博客"><meta property="og:description" content="游戏世界"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://shijie.icu/img/default_cover.jpg"><meta property="article:author" content="c2hapmll"><meta property="article:tag"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://shijie.icu/img/default_cover.jpg"><meta name="description" content="游戏世界"><link rel="shortcut icon" href="/images/themes/32.png"><link rel="canonical" href="https://shijie.icu/games/"><link rel="preconnect" href="//cdn.cbd.int"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><meta name="google-site-verification" content="xxx"/><meta name="baidu-site-verification" content="code-xxx"/><meta name="msvalidate.01" content="xxx"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.cbd.int/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media='all'"><script>const GLOBAL_CONFIG = {
  linkPageTop: undefined,
  peoplecanvas: undefined,
  postHeadAiDescription: undefined,
  diytitle: undefined,
  LA51: undefined,
  greetingBox: undefined,
  twikooEnvId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
  commentBarrageConfig:undefined,
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: false,
  mainTone: undefined,
  authorStatus: {"skills":["🤖️ 数码科技爱好者","🔍 分享与热心帮助","🏠 智能家居小能手"]},
  algolia: undefined,
  localSearch: {"path":"/search.xml","preload":true,"languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: undefined,
  highlight: {"plugin":"prismjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    simplehomepage: true,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: c2hapmll","link":"链接: ","source":"来源: 我的博客","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: false,
  shortcutKey: undefined,
  autoDarkmode: true
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  configTitle: '我的博客',
  title: '游戏世界',
  postAI: '',
  pageFillDescription: '游戏世界',
  isPost: false,
  isHome: false,
  isHighlightShrink: false,
  isToc: false,
  postUpdate: '2024-11-12 17:41:59',
  postMainColor: '',
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#18171d')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f7f9fe')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/costom_widget.css"><link rel="stylesheet" href="/css/badge.css"><meta name="generator" content="Hexo 7.3.0"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="/images/themes/32.png"/><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"/><script async="async" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="page" id="body-wrap"><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><a id="site-name" href="/" accesskey="h"><div class="title">我的博客</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"/><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole();"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" alt="微信" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/wechat.jpg"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" alt="支付宝" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/alipay.jpg"/></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title"> 最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 1.05rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 1.05rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 1.05rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 1.05rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 1.05rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 1.05rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 1.05rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 1.05rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 1.05rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/02/"><span class="card-archive-list-date">二月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/12/"><span class="card-archive-list-date">十二月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/11/"><span class="card-archive-list-date">十一月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/10/"><span class="card-archive-list-date">十月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/09/"><span class="card-archive-list-date">九月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li></ul></div><hr/></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout hide-aside" id="content-inner"><div id="page"><h1 class="page-title">游戏世界</h1><div id="games"><div class="author-content author-content-item GamesPage single" style="background: url(/images/games/top_games.jpg) left 37% / cover no-repeat !important;"><div class="card-content"><div class="author-content-item-tips">游戏世界</div><span class="author-content-item-title">第九艺术</span><div class="content-bottom"><div class="tips">逃避现实的最好方式（并不）, 🏆标识全收集，🏅标识全成就</div></div><div class="banner-button-group"><a class="banner-button" target="_blank" rel="noopener" href="https://steamcommunity.com/profiles/76561199172600200/"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right" style="font-size: 1.3rem"></i><span class="banner-button-text">Steam</span></a></div></div></div><div class="goodgames-item"><h2 class="goodgames-title">正在游玩</h2><div class="goodgames-item-description">正在游玩的网游和未通关的单机游戏放在这里</div><div class="games-item"><div class="games-item-content"><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/do/100002.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="宝可梦：绿宝石"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;宝可梦：绿宝石&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【宝可梦：绿宝石】&quot;);" title="宝可梦：绿宝石">宝可梦：绿宝石</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">GBA / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.1</span></div><div class="games-item-content-item-description">同样使用RetroArch模拟器玩的，目前打到了第七道馆，123号公路附近</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://www.pokemon.co.jp/game/gba/emerald/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('宝可梦：绿宝石 undefined 同样使用RetroArch模拟器玩的，目前打到了第七道馆，123号公路附近')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><div class="flip-card-inner"><div class="flip-card-front"><img class="games-item-content-item-image" data-lazy-src="/images/games/do/100001.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="三角洲行动"></div><div class="flip-card-back"><img class="games-item-back-image" data-lazy-src="/images/games/do/100001_b.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="三角洲行动"></div></div></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;三角洲行动&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【三角洲行动】&quot;);" title="三角洲行动">三角洲行动</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">PC / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.22</span></div><div class="games-item-content-item-description">国产网游最好的一点就是不需要加速器。在最初的几个赛季爆率没有调整还是很好玩的，现在要和策划对抗才能有有良好的的体验。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://df.qq.com/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('三角洲行动 undefined 国产网游最好的一点就是不需要加速器。在最初的几个赛季爆率没有调整还是很好玩的，现在要和策划对抗才能有有良好的的体验。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div></div></div></div><div class="goodgames-item"><h2 class="goodgames-title">已经玩过</h2><div class="goodgames-item-description">已经通关的单机游戏或者玩过的网游放在这里</div><div class="games-item"><div class="games-item-content"><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/2025/202505060101.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="无人深空"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;无人深空&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【无人深空】&quot;);" title="无人深空">无人深空</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.5.6</span></div><div class="games-item-content-item-description">值得慢慢玩的游戏，虽然可以联机，但是联机属性比较弱，比较倾向于冒险探索类型。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/275850/No_Mans_Sky/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('无人深空 undefined 值得慢慢玩的游戏，虽然可以联机，但是联机属性比较弱，比较倾向于冒险探索类型。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/2025/202504270101.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="塞尔达传说:众神的三角力量与四人之剑"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;塞尔达传说:众神的三角力量与四人之剑&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【塞尔达传说:众神的三角力量与四人之剑】&quot;);" title="塞尔达传说:众神的三角力量与四人之剑">塞尔达传说:众神的三角力量与四人之剑</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">GBA / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.27</span></div><div class="games-item-content-item-description">重置了SFC塞尔达传说:众神的三角力量(1991)的版本，额外增加了联机游戏四支剑，跑图比较麻烦，解谜有点难度。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://www.igdb.com/games/the-legend-of-zelda-a-link-to-the-past-and-four-swords" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('塞尔达传说:众神的三角力量与四人之剑 undefined 重置了SFC塞尔达传说:众神的三角力量(1991)的版本，额外增加了联机游戏四支剑，跑图比较麻烦，解谜有点难度。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/2025/202504210201.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="龙珠大冒险"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;龙珠大冒险&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【龙珠大冒险】&quot;);" title="龙珠大冒险">龙珠大冒险</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">GBA / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.21</span></div><div class="games-item-content-item-description">我小时候明明没有GBA游戏机，为什么脑海中存在这款游戏的游玩记忆？印象中有好多隐藏可以收集，就是堆怪比较严重。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://www.igdb.com/games/dragon-ball-advanced-adventure" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('龙珠大冒险 undefined 我小时候明明没有GBA游戏机，为什么脑海中存在这款游戏的游玩记忆？印象中有好多隐藏可以收集，就是堆怪比较严重。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/2025/202504210101.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="三国战纪(正宗plus)"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;三国战纪(正宗plus)&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【三国战纪(正宗plus)】&quot;);" title="三国战纪(正宗plus)">三国战纪(正宗plus)</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">街机 / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.21</span></div><div class="games-item-content-item-description">街机经典之一，最常玩的是正宗plus版本和风云再起版本，过关可选择的路线很多，道具多种多样，打起来眼花缭乱，游戏厅一景。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://www.igdb.com/games/knights-of-valour--1" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('三国战纪(正宗plus) undefined 街机经典之一，最常玩的是正宗plus版本和风云再起版本，过关可选择的路线很多，道具多种多样，打起来眼花缭乱，游戏厅一景。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/2025/202504170101.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="炸弹人机皇版 (1997)"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;炸弹人机皇版 (1997)&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【炸弹人机皇版 (1997)】&quot;);" title="炸弹人机皇版 (1997)">炸弹人机皇版 (1997)</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">街机 / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.17</span></div><div class="games-item-content-item-description">和恐龙快打一样，也是小时候经常在游戏厅玩的街机之一，在有坐骑的时候能卡无敌，有的老板还不让卡(怕你占机器太久)</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://www.igdb.com/games/neo-bomberman" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('炸弹人机皇版 (1997) undefined 和恐龙快打一样，也是小时候经常在游戏厅玩的街机之一，在有坐骑的时候能卡无敌，有的老板还不让卡(怕你占机器太久)')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/2025/202504160101.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="恐龙快打 (1993)"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;恐龙快打 (1993)&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【恐龙快打 (1993)】&quot;);" title="恐龙快打 (1993)">恐龙快打 (1993)</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">街机 / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.16</span></div><div class="games-item-content-item-description">恐龙快打是我为数不多能够一币通关的街机游戏之一，也是因为这个小时候老被揍。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://www.igdb.com/games/cadillacs-and-dinosaurs" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('恐龙快打 (1993) undefined 恐龙快打是我为数不多能够一币通关的街机游戏之一，也是因为这个小时候老被揍。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/2025/202504080101.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="小丑牌"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;小丑牌&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【小丑牌】&quot;);" title="小丑牌">小丑牌</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.8</span></div><div class="games-item-content-item-description">可以换牌的德州扑克的肉鸽道具版，玩起来挺上头而且和现实中的赌博差异比较大，不用担心影响现实。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/2379780/Balatro/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('小丑牌 undefined 可以换牌的德州扑克的肉鸽道具版，玩起来挺上头而且和现实中的赌博差异比较大，不用担心影响现实。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/2025/202504070101.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="星之海"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;星之海&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【星之海】&quot;);" title="星之海">星之海</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.6</span><span class="games-item-content-item-specification-achievements"> / 🏆</span></div><div class="games-item-content-item-description">非常优秀的RPG游戏，唯美的画风穿插着精致的动画，真结局是HE结局(我就喜欢大团圆)，战斗系统稍显不足，打得会比较累。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/1244090/Sea_of_Stars/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('星之海 undefined 非常优秀的RPG游戏，唯美的画风穿插着精致的动画，真结局是HE结局(我就喜欢大团圆)，战斗系统稍显不足，打得会比较累。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/2025/202504030101.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="蟹蟹寻宝奇遇"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;蟹蟹寻宝奇遇&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【蟹蟹寻宝奇遇】&quot;);" title="蟹蟹寻宝奇遇">蟹蟹寻宝奇遇</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.4.3</span></div><div class="games-item-content-item-description">难度是真的离谱，我这魂游也玩了不少了，玩这个老是死，BOSS伤害太高了。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/1887840/_/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('蟹蟹寻宝奇遇 undefined 难度是真的离谱，我这魂游也玩了不少了，玩这个老是死，BOSS伤害太高了。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/202503310101.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="王国保卫战5：联盟"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;王国保卫战5：联盟&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【王国保卫战5：联盟】&quot;);" title="王国保卫战5：联盟">王国保卫战5：联盟</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.3.30</span></div><div class="games-item-content-item-description">经典塔防IP，钢铁难度反而是最简单的，难度设置不合理，玩的时候脑子转不过来，多线程失效</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/2849080/_5/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('王国保卫战5：联盟 undefined 经典塔防IP，钢铁难度反而是最简单的，难度设置不合理，玩的时候脑子转不过来，多线程失效')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/202503260101.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="收获日3"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;收获日3&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【收获日3】&quot;);" title="收获日3">收获日3</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.3.26</span></div><div class="games-item-content-item-description">好兄弟送的礼物，我非常喜欢游戏里面的潜行要素，可惜关卡比较少，重复游玩会极大的减少乐趣。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/1272080/PAYDAY3_3/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('收获日3 undefined 好兄弟送的礼物，我非常喜欢游戏里面的潜行要素，可惜关卡比较少，重复游玩会极大的减少乐趣。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/202503190101.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="GTA5"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;GTA5&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【GTA5】&quot;);" title="GTA5">GTA5</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Epic / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.3.18</span></div><div class="games-item-content-item-description">5年前就玩过，但是因为网络问题弃之，最近捡起来和好友玩线上模式，网络问题还是很严重，扣分。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/271590/Grand_Theft_Auto_V/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('GTA5 undefined 5年前就玩过，但是因为网络问题弃之，最近捡起来和好友玩线上模式，网络问题还是很严重，扣分。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/202503140101.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="小飞船大冒险"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;小飞船大冒险&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【小飞船大冒险】&quot;);" title="小飞船大冒险">小飞船大冒险</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.3.16</span></div><div class="games-item-content-item-description">俯视角射击游戏独立冒险游戏，流程大约8小时左右。令人惊艳的视觉风格，带有收集要素，就是最后成神要删除存档，记得提前保存。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/1634860/_Minishoot_Adventures/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('小飞船大冒险 undefined 俯视角射击游戏独立冒险游戏，流程大约8小时左右。令人惊艳的视觉风格，带有收集要素，就是最后成神要删除存档，记得提前保存。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/202503050118.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="大头菜小子避税历险记"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;大头菜小子避税历险记&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【大头菜小子避税历险记】&quot;);" title="大头菜小子避税历险记">大头菜小子避税历险记</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.3.9</span><span class="games-item-content-item-specification-achievements"> / 🏆🏅</span></div><div class="games-item-content-item-description">流程比较短，很有意思的一款小游戏，画风独特，音乐动感。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/1205450/Turnip_Boy_Commits_Tax_Evasion/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('大头菜小子避税历险记 undefined 流程比较短，很有意思的一款小游戏，画风独特，音乐动感。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/202503050117.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="僵尸世界大战：劫后余生"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;僵尸世界大战：劫后余生&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【僵尸世界大战：劫后余生】&quot;);" title="僵尸世界大战：劫后余生">僵尸世界大战：劫后余生</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Epic / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.3.6</span></div><div class="games-item-content-item-description">适合组队游玩的FPS打僵尸游戏，高难度难度很高(废话)，单人不好过关。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/699130/World_War_Z/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('僵尸世界大战：劫后余生 undefined 适合组队游玩的FPS打僵尸游戏，高难度难度很高(废话)，单人不好过关。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/202503050116.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="杀戮尖塔"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;杀戮尖塔&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【杀戮尖塔】&quot;);" title="杀戮尖塔">杀戮尖塔</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.3.5</span></div><div class="games-item-content-item-description">非常上头的卡牌肉鸽爬塔游戏，近些天重新拾起准备打全成就，最后一个成就卡在盛宴击杀甜甜圈了，要么没盛宴，要么没甜甜圈。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/646570/Slay_the_Spire/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('杀戮尖塔 undefined 非常上头的卡牌肉鸽爬塔游戏，近些天重新拾起准备打全成就，最后一个成就卡在盛宴击杀甜甜圈了，要么没盛宴，要么没甜甜圈。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300015.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="怪兽仙境1"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;怪兽仙境1&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【怪兽仙境1】&quot;);" title="怪兽仙境1">怪兽仙境1</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Java / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-play-date">2025.1.16</span></div><div class="games-item-content-item-description">仙掌软件推出的一款宠物角色扮演游戏，需要发很多次2元短信解锁游戏内购功能，当时各种找破解版，那时我才9岁。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://tieba.baidu.com/f?kw=%E6%80%AA%E5%85%BD%E4%BB%99%E5%A2%83&amp;ie=utf-8" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('怪兽仙境1 undefined 仙掌软件推出的一款宠物角色扮演游戏，需要发很多次2元短信解锁游戏内购功能，当时各种找破解版，那时我才9岁。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300014.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="泰拉瑞亚"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;泰拉瑞亚&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【泰拉瑞亚】&quot;);" title="泰拉瑞亚">泰拉瑞亚</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">横板像素沙盒冒险，游戏性非常高，高难度下boss比较难打</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/105600/Terraria/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('泰拉瑞亚 undefined 横板像素沙盒冒险，游戏性非常高，高难度下boss比较难打')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300013.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="Counter-Strike 2"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;Counter-Strike 2&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【Counter-Strike 2】&quot;);" title="Counter-Strike 2">Counter-Strike 2</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-achievements"> / 🏆</span></div><div class="games-item-content-item-description">一款经典的FPS游戏联机对战游戏，玩了1652小时左右</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/730/CounterStrike_Global_Offensive/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('Counter-Strike 2 undefined 一款经典的FPS游戏联机对战游戏，玩了1652小时左右')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300012.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="Helltaker"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;Helltaker&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【Helltaker】&quot;);" title="Helltaker">Helltaker</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-achievements"> / 🏆</span></div><div class="games-item-content-item-description">推箱子解谜游戏，画风很独特，而且是免费游戏</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/1289310/Helltaker/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('Helltaker undefined 推箱子解谜游戏，画风很独特，而且是免费游戏')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300011.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="宝可梦：叶绿"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;宝可梦：叶绿&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【宝可梦：叶绿】&quot;);" title="宝可梦：叶绿">宝可梦：叶绿</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">GBA / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">不久之前在RetroArch模拟器上玩通关的，虽然通关但是一周目开局游戏文件选错了，导致二周目无法开启，留有遗憾。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="http://www.pokemon.co.jp/game/gba/fl/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('宝可梦：叶绿 undefined 不久之前在RetroArch模拟器上玩通关的，虽然通关但是一周目开局游戏文件选错了，导致二周目无法开启，留有遗憾。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300001.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="黑神话：悟空"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;黑神话：悟空&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【黑神话：悟空】&quot;);" title="黑神话：悟空">黑神话：悟空</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span><span class="games-item-content-item-specification-achievements"> / 🏆🏅</span></div><div class="games-item-content-item-description">制作极其精良的国产3A游戏，画面表现非常出色，游戏性强。已售出2700万份，总销售72亿元。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/2358720/_/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('黑神话：悟空 undefined 制作极其精良的国产3A游戏，画面表现非常出色，游戏性强。已售出2700万份，总销售72亿元。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300002.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="星露谷物语"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;星露谷物语&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【星露谷物语】&quot;);" title="星露谷物语">星露谷物语</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">一款像素风格模拟农场游戏，制作非常精良，属于是打开了就停不下来的那种。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/413150/Stardew_Valley/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('星露谷物语 undefined 一款像素风格模拟农场游戏，制作非常精良，属于是打开了就停不下来的那种。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300003.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="绿色地狱"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;绿色地狱&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【绿色地狱】&quot;);" title="绿色地狱">绿色地狱</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">亚马逊雨林生存游戏，单人玩有点恐怖，难度比较高，多人就变成娱乐游戏了，很不错的冒险生存游戏。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/815370/Green_Hell/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('绿色地狱 undefined 亚马逊雨林生存游戏，单人玩有点恐怖，难度比较高，多人就变成娱乐游戏了，很不错的冒险生存游戏。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300004.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="艾尔登法环"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;艾尔登法环&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【艾尔登法环】&quot;);" title="艾尔登法环">艾尔登法环</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">2022年年度游戏，魂系列，画面精美，剧情丰富，难度适中，我的评价是：神作，还有多人无缝联机mod可以一起冒险。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/1245620/_/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('艾尔登法环 undefined 2022年年度游戏，魂系列，画面精美，剧情丰富，难度适中，我的评价是：神作，还有多人无缝联机mod可以一起冒险。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300005.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="空洞骑士"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;空洞骑士&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【空洞骑士】&quot;);" title="空洞骑士">空洞骑士</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">一款横板动作冒险游戏，难度较高，我没完全通关，只过了苦痛之路，四锁五门没有打，中途间隔时间有点久了，就不太想继续打下去了。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/367520/Hollow_Knight/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('空洞骑士 undefined 一款横板动作冒险游戏，难度较高，我没完全通关，只过了苦痛之路，四锁五门没有打，中途间隔时间有点久了，就不太想继续打下去了。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300006.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="无主之地3"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;无主之地3&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【无主之地3】&quot;);" title="无主之地3">无主之地3</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">一款FPS+RPG游戏，剧情有点颠，刷刷刷游戏，和好友开黑很有意思。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/397540/Borderlands_3/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('无主之地3 undefined 一款FPS+RPG游戏，剧情有点颠，刷刷刷游戏，和好友开黑很有意思。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300008.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="消逝的光芒2"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;消逝的光芒2&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【消逝的光芒2】&quot;);" title="消逝的光芒2">消逝的光芒2</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">二代相较一代确实差一点，但毕竟是后面出的还是很有可玩性的。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/534380/2/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('消逝的光芒2 undefined 二代相较一代确实差一点，但毕竟是后面出的还是很有可玩性的。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300009.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="消逝的光芒"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;消逝的光芒&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【消逝的光芒】&quot;);" title="消逝的光芒">消逝的光芒</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">丧尸生存冒险捡垃圾，神作</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/239140/Dying_Light/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('消逝的光芒 undefined 丧尸生存冒险捡垃圾，神作')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300010.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="饥荒联机版"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;饥荒联机版&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【饥荒联机版】&quot;);" title="饥荒联机版">饥荒联机版</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Steam / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">俯视角生存冒险游戏，神作，好友开黑很有意思。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://store.steampowered.com/app/322330/Dont_Starve_Together/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('饥荒联机版 undefined 俯视角生存冒险游戏，神作，好友开黑很有意思。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div><div class="games-item-content-item"><div class="games-item-content-item-cover"><img class="games-item-content-item-image" data-lazy-src="/images/games/collect/300007.jpeg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="我的世界"></div><div class="games-item-content-item-info"><div class="games-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;我的世界&quot;);anzhiyu.snackbarShow(&quot;已复制游戏名： 【我的世界】&quot;);" title="我的世界">我的世界</div><div class="games-item-content-item-specification"><span class="games-item-content-item-specification-platform">Xbox / </span><span class="games-item-content-item-specification-rating">⭐⭐⭐⭐⭐ / </span></div><div class="games-item-content-item-description">一款沙盒游戏，可以自由创造，MOD非常多，像素沙盒游戏的代表作。</div><div class="games-item-content-item-toolbar"><a class="games-item-content-item-link" href="https://www.minecraft.net/" target="_blank">详情</a><div class="bber-reply" onclick="rm.rightMenuCommentText('我的世界 undefined 一款沙盒游戏，可以自由创造，MOD非常多，像素沙盒游戏的代表作。')"><i class="anzhiyufont anzhiyu-icon-message"></i></div></div></div></div></div></div></div></div><hr/><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)">匿名评论</a><a href="/privacy" style="margin-left: 4px">隐私政策</a></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div></div></div><div class="comment-barrage"></div></div></div></main><footer id="footer"><div id="footer-wrap"></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">&copy;2024 - 2025 By <a class="footer-bar-link" href="/" title="c2hapmll" target="_blank">c2hapmll</a></div></div><div id="footer-type-tips"></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index" title="皖ICP备20000482号-2">皖ICP备20000482号-2</a><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.mps.gov.cn/#/query/webSearch" title="皖公网安备 34120402000423号">皖公网安备 34120402000423号</a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">6</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">9</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">3</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" target="_blank" rel="noopener" href="https://home.shijie.icu/" title="我的主页"><img class="back-menu-item-icon" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/32.png" alt="我的主页"/><span class="back-menu-item-text">我的主页</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 0.88rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 0.88rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 0.88rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 0.88rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 0.88rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 0.88rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 0.88rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 0.88rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 0.88rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button></div><div id="rightside-config-show"><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><a id="to_comment" href="#post-comment" title="直达评论"><i class="anzhiyufont anzhiyu-icon-comments"></i></a><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><span id="loading-status"></span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="is-center" id="loading-database"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-pulse-icon"></i><span>  数据库加载中</span></div><div class="search-wrap"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div><hr/><div id="local-search-results"></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size: 1rem;"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://y.qq.com/n/ryqq/playlist/8802438608&quot;, &quot;_blank&quot;);" style="display: none;"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://cdn.cbd.int/instant.page@5.2.0/instantpage.js" type="module"></script><script src="https://cdn.cbd.int/vanilla-lazyload@17.8.5/dist/lazyload.iife.min.js"></script><script src="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.js"></script><canvas id="universe"></canvas><script async src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script async src="/anzhiyu/random.js"></script><script src="/js/search/local-search.js"></script><div class="js-pjax"><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="games"></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
        region: '',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script>var visitorMail = "";
</script><script async data-pjax src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script async src="https://at.alicdn.com/t/c/font_4729035_3m5int5al8c.js"></script><script src="/js/countdown.js"></script><script src="/js/getnowplaying.js"></script><script src="/js/badge.js"></script><script src="/js/update.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/aplayer/APlayer.min.css" media="print" onload="this.media='all'"><script src="https://cdn.cbd.int/anzhiyu-blog-static@1.0.1/js/APlayer.min.js"></script><script src="https://cdn.cbd.int/hexo-anzhiyu-music@1.0.1/assets/js/Meting2.min.js"></script><script src="https://cdn.cbd.int/pjax@0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: false,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script><script charset="UTF-8" src="https://cdn.cbd.int/anzhiyu-theme-static@1.1.5/accesskey/accesskey.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div></body></html>
#subscriptions
  if site.data.subscriptions
    each i in site.data.subscriptions
      .author-content.author-content-item.SubscriptionsPage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          .banner-button-group
            a.banner-button(href=i.buttonLink)
              i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
              span.banner-button-text=i.buttonText
      each item in i.subscription_categories
        .goodsubscriptions-item
          h2.goodsubscriptions-title= item.title
          .goodsubscriptions-item-description= item.description
          .subscriptions-item
            .subscriptions-item-content
              each subscription, index in item.subscriptions_list
                .subscriptions-item-content-item
                  .subscriptions-item-content-item-cover
                    if subscription.back_image
                      .flip-card-inner
                        .flip-card-front
                          img.subscriptions-item-content-item-image(data-lazy-src=subscription.image alt=subscription.name)
                        .flip-card-back
                          img.subscriptions-item-content-item-image(data-lazy-src=subscription.back_image alt=subscription.name)
                    else
                      img.subscriptions-item-content-item-image(data-lazy-src=subscription.image alt=subscription.name)
                  .subscriptions-item-content-item-info
                    .subscriptions-item-content-item-name= subscription.name
                    .subscriptions-item-content-item-type= subscription.type
                    .subscriptions-item-content-item-rating= subscription.rating
                    if subscription.cancel_date
                      .subscriptions-item-content-item-date 取消日期: #{subscription.cancel_date}
                    .subscriptions-item-content-item-description= subscription.description
                  .subscriptions-item-content-item-toolbar
                    if subscription.link
                      a.subscriptions-item-content-item-link(href=subscription.link target='_blank' rel='noopener noreferrer')
                        i.anzhiyufont.anzhiyu-icon-link
                        span 访问

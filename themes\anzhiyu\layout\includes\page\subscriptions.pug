#subscriptions
  if site.data.subscriptions
    each i in site.data.subscriptions
      .author-content.author-content-item.SubscriptionsPage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          .banner-button-group
            a.banner-button(href=i.buttonLink)
              i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
              span.banner-button-text=i.buttonText
      each item in i.good_subscriptions
        .goodsubscriptions-item
          h2.goodsubscriptions-title= item.title
          .goodsubscriptions-item-description= item.description
          .subscriptions-item
            .subscriptions-item-content
              each iten, indey in item.subscriptions_list
                .subscriptions-item-content-item
                  .subscriptions-item-content-item-cover
                    if iten.back_image
                      .flip-card-inner
                        .flip-card-front
                          img.subscriptions-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                        .flip-card-back
                          img.subscriptions-item-back-image(data-lazy-src=url_for(iten.back_image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                    else
                      img.subscriptions-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                  .subscriptions-item-content-item-info
                    .subscriptions-item-content-item-name(onclick=`rm.rightmenuCopyText("${iten.name}");anzhiyu.snackbarShow("${_p('已复制订阅名：') + " 【" + iten.name + "】"}");` title=iten.name)= iten.name
                    .subscriptions-item-content-item-specification
                      if iten.type
                        span.subscriptions-item-content-item-specification-type= iten.type + " / "
                      if iten.rating
                        span.subscriptions-item-content-item-specification-rating= iten.rating
                      if iten.date
                        span.subscriptions-item-content-item-specification-date= " / " + iten.date
                    if iten.note
                      .subscriptions-item-content-item-note= iten.note
                    .subscriptions-item-content-item-description= iten.description
                    .subscriptions-item-content-item-toolbar
                      if iten.link && (iten.link.includes('https://') || iten.link.includes('http://'))
                        a.subscriptions-item-content-item-link(href= iten.link, target='_blank') 详情
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.type + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message
                      else if iten.link
                        a.subscriptions-item-content-item-link(href= iten.link, target='_blank') 查看文章
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.type + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message

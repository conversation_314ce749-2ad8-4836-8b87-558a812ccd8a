<?xml version="1.0" encoding="utf-8"?>
<search> 
  
  
    
    <entry>
      <title>2025年04月22日 - RetroArch模拟器 - 联机篇</title>
      <link href="/post/20250422110000.html"/>
      <url>/post/20250422110000.html</url>
      
        <content type="html"><![CDATA[<p>RetroArch 对于联机的要求比较严格，需要RetroArch 版本一致、核心版本一致、游戏rom一致才能进行联机，此为前提条件需要注意。</p><h1 id="1-设置用户名"><a href="#1-设置用户名" class="headerlink" title="1. 设置用户名"></a>1. 设置用户名</h1><p>为防止意外和找不到房间的情况发生，Retroarch需要设置一个用户名用于辨认，房主和玩家尽量都设置，步骤如下</p><ol><li>主菜单 -&gt; 设置 -&gt; 用户 -&gt; 用户名，设置为你想要的名称即可</li></ol><h1 id="2-局域网联机"><a href="#2-局域网联机" class="headerlink" title="2. 局域网联机"></a>2. 局域网联机</h1><p>过于简单，不再详解。</p><h1 id="3-公网联机"><a href="#3-公网联机" class="headerlink" title="3. 公网联机"></a>3. 公网联机</h1><h2 id="3-1-UPnP和端口转发（内网穿透）"><a href="#3-1-UPnP和端口转发（内网穿透）" class="headerlink" title="3.1 UPnP和端口转发（内网穿透）"></a>3.1 UPnP和端口转发（内网穿透）</h2><p>RetroArch 在不设置代理服务器的情况下默认使用此种方式进行公网联机。</p><p>原理是房主主机作为服务端，进行内网穿透映射到外网才可以正常联机，如果你不理解该节的一些名词，请勿参照该节内容，防止路由器暴露公网端口。</p><h3 id="3-1-1-路由器"><a href="#3-1-1-路由器" class="headerlink" title="3.1.1 路由器"></a>3.1.1 路由器</h3><p>进入路由器管理页面，找到<strong>UPnP功能</strong>并开启，步骤如下</p><ol><li>访问192.168.1.1（小米默认是192.168.31.1）进入路由管理页面，开启UPnP功能。</li></ol><p>RetroArch 启动后会自动增加UPnP记录，如果没有自动记录请使用<strong>端口转发服务</strong></p><ol><li>设置端口转发，TCP，主机端局域网地址，端口为55435</li></ol><h3 id="3-1-2-主机端设置（房主）"><a href="#3-1-2-主机端设置（房主）" class="headerlink" title="3.1.2 主机端设置（房主）"></a>3.1.2 主机端设置（房主）</h3><p>在联机菜单下选择主机并运行游戏，步骤如下：</p><ol><li>主菜单 -&gt; 联机 -&gt; 主机 -&gt; 作为游戏主机</li></ol><p>如若一切就绪，回车后将在左下角提示”联机游戏将在内容加载后开始”，此时选择核心、选择游戏启动，会在左下角出现提示”您已作为玩家1进入”、”正在等待客户端连接”，此时客户端（其他玩家）连进主机即可正常联机游玩游戏。</p><p>如果弹出的不是以上提示，或者为类似下面的提示时将无法进行联机操作，请使用其他方式进行联机。</p><ol><li><strong>您的网络有前置，推荐使用中继服务</strong></li><li><strong>您的网络无法在公网访问</strong></li><li><strong>联机服务开启失败</strong></li></ol><h3 id="3-1-3-客户端设置（其他玩家）"><a href="#3-1-3-客户端设置（其他玩家）" class="headerlink" title="3.1.3 客户端设置（其他玩家）"></a>3.1.3 客户端设置（其他玩家）</h3><p>无需任何设置，只要你能正常联网即可，具体步骤如下</p><ol><li>主菜单 -&gt; 联机 -&gt; 刷新联机主机列表 -&gt; 往下翻找到对应的房间名称回车即可，名称为主机端的用户名。</li></ol><p>如果你没有玩过房主开的房间选择的游戏，那么会提示”未找到匹配rom，将在你启动对应rom时进行联机”，此时选择相同的核心、相同的游戏启动即可加入联机房间。</p><h2 id="3-2-公网直连"><a href="#3-2-公网直连" class="headerlink" title="3.2 公网直连"></a>3.2 公网直连</h2><p>我没有ipv4公网，无法尝试，如果有的话理论上和局域网联机一样的方式，只需路由器开放55435端口进出即可。</p><h2 id="3-3-官方联机服务器（中继）"><a href="#3-3-官方联机服务器（中继）" class="headerlink" title="3.3 官方联机服务器（中继）"></a>3.3 官方联机服务器（中继）</h2><p>不推荐，官方的git仓库中pr的只有国外的服务器，我尝试了所有的官方服务器，要么连接失败，要么延迟逆天。</p><h2 id="3-4-自建联机服务器（中继）"><a href="#3-4-自建联机服务器（中继）" class="headerlink" title="3.4 自建联机服务器（中继）"></a>3.4 自建联机服务器（中继）</h2><p>需要自行准备公网服务器（具备ipv4公网地址，目前没有试验ipv6的情况），如果没有请参照其他节进行联机。</p><h3 id="3-4-1-配置联机服务"><a href="#3-4-1-配置联机服务" class="headerlink" title="3.4.1 配置联机服务"></a>3.4.1 配置联机服务</h3><p>官方服务器源码地址：<a href="https://github.com/libretro/netplay-tunnel-server">https://github.com/libretro/netplay-tunnel-server</a></p><p>下载其中内容上传至服务器 或 在服务器安装git直接clone上面的地址。</p><p>环境需求非常简单，需要python3.7版本及以上，安装过程自行百度不再赘述。</p><p>运行也非常简单，readme.md中直接使用下面的命令即可</p><pre class="line-numbers language-python" data-language="python"><code class="language-python">python3 <span class="token operator">-</span>OO retroarch_tunnel_server<span class="token punctuation">.</span>py或 自定义路径的配置文件python3 <span class="token operator">-</span>OO retroarch_tunnel_server<span class="token punctuation">.</span>py <span class="token operator">/</span>path<span class="token operator">/</span>retroarch_tunnel_server<span class="token punctuation">.</span>ini<span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span></span></code></pre><p>python执行成功返回如下图所示：</p><p><img src="/images/posts/2025/042201/202504220201.png" alt="Python运行脚本"></p><p>防火墙放行端口 55435，如果没有启用防火墙就不需要此步骤，如果是iptables自行搜索端口放行命令（我忘了，懒得搜）</p><pre class="line-numbers language-bash" data-language="bash"><code class="language-bash"><span class="token comment"># Centos firewalld</span><span class="token function">sudo</span> firewall-cmd <span class="token parameter variable">--permanent</span> --add-port<span class="token operator">=</span><span class="token number">55435</span>/tcp<span class="token function">sudo</span> firewall-cmd <span class="token parameter variable">--reload</span><span class="token comment"># Ubuntu ufw</span><span class="token function">sudo</span> ufw allow <span class="token number">55435</span>/tcp<span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span><span></span><span></span><span></span></span></code></pre><p>别忘记放行服务商的防火墙，如果你是国内服务器，例如阿里云的服务器，还需要登录网页控制台，</p><ol><li>控制台 -&gt; 服务器 -&gt; 防火墙 -&gt; 放行55435端口</li></ol><p>两套防火墙都要放行，一套是服务器本身的防火墙，一套是服务商提供的防火墙</p><h3 id="3-4-2-主机端设置（房主）"><a href="#3-4-2-主机端设置（房主）" class="headerlink" title="3.4.2 主机端设置（房主）"></a>3.4.2 主机端设置（房主）</h3><p>需要启用<strong>代理服务器功能</strong>，并设置为自定义和填写服务器的公网地址，步骤如下</p><ol><li>主菜单 -&gt; 联机 -&gt; 网络 -&gt; 启用代理服务器</li><li>设置<strong>代理服务器位置</strong>，选择最下面的 “自定义”</li><li>设置<strong>自定义代理服务器地址</strong>，填写你的服务器地址即可</li><li>（可选）设置<strong>服务器密码</strong>，可以避免无关人员加入捣乱</li></ol><p>随后在联机菜单下选择主机并运行游戏，步骤如下：</p><ol><li>主菜单 -&gt; 联机 -&gt; 主机 -&gt; 作为游戏主机</li></ol><p>如若一切就绪，回车后将在左下角提示”联机游戏将在内容加载后开始”，此时选择核心、选择游戏启动，会在左下角出现提示”您已作为玩家1进入”、”正在等待客户端连接”，此时客户端（其他玩家）连进主机即可正常联机游玩游戏。</p><p>如果左下角提示其他内容，请参照本文目录QA节中的内容进行排查。</p><h3 id="3-4-3-客户端设置（其他玩家）"><a href="#3-4-3-客户端设置（其他玩家）" class="headerlink" title="3.4.3 客户端设置（其他玩家）"></a>3.4.3 客户端设置（其他玩家）</h3><p>无需任何设置，只要你能正常联网即可，具体步骤如下</p><ol><li>主菜单 -&gt; 联机 -&gt; 刷新联机主机列表 -&gt; 往下翻找到对应的房间名称回车即可，名称为主机端的用户名。</li></ol><p>如果你没有玩过房主开的房间选择的游戏，那么会提示”未找到匹配rom，将在你启动对应rom时进行联机”，此时选择相同的核心、相同的游戏启动即可加入联机房间。</p><h2 id="3-5-软件异地组网联机（异地组网）"><a href="#3-5-软件异地组网联机（异地组网）" class="headerlink" title="3.5 软件异地组网联机（异地组网）"></a>3.5 软件异地组网联机（异地组网）</h2><p>很多软件提供多台外网机器组局域网的能力，这样就可以参照<strong>局域网联机</strong>一节进行局域网联机操作。但比较麻烦的是，和你玩的朋友可能不愿意下载一些软件，尤其当这些软件设置起来还需要一定的网络知识的情况。</p><p>因为几乎所有的异地组网软件都是通过内网穿透打洞的方式进行的，网络质量会非常影响打洞的成功率，而且延迟问题也不能保证。由于软件众多不再进行详解，异地组网方式请自行搜索皎月连、贝瑞蒲公英、zerotier、Netbird等异地组网教程。</p><p>当所有机器连接到同一网段时就可以使用局域网联机的方式进行游玩了。</p><h1 id="4-问题排查"><a href="#4-问题排查" class="headerlink" title="4. 问题排查"></a>4. 问题排查</h1><ol><li><p>联机支持哪些游戏？哪些核心?<br>不清楚，我目前只使用了Fbneo核心进行街机游戏的联机功能，具体的支持范围你可以参考官方教程或各核心的github库查询。</p></li><li><p>能够实现哪些功能？<br>宝可梦交换、通信进化，同网络不同屏幕联机游戏，不同网络不同屏幕联机游戏。</p></li><li><p>延迟问题？<br>自建服务器取决于你的服务器，国内的服务器大约在30ms-150ms波动，对于一些对战类比较勉强，对于闯关类还不错。<br>官方服务器本身就没有国内cn的服务器，延迟大很正常<br>理论上最好的为直连，少了一个中继服务器的延迟，但直连要公网地址，目前运营商都不给了。</p></li><li><p>可以直接使用<strong>连接到联机主机</strong>输入ip地址吗？<br>可以的，但只有局域网和直连可用，中继的话填写不了主机ip（存疑，70%把握）</p></li><li><p>直接使用<strong>连接到联机主机</strong>功能输入服务器地址提示”服务器版本较低”怎么办？<br>请注意，<strong>连接到联机主机</strong>功能输入的是主机（房主）机器的IP地址，不是你搭建的服务器地址。</p></li></ol>]]></content>
      
      
      <categories>
          
          <category> 游戏 </category>
          
      </categories>
      
      
        <tags>
            
            <tag> 日常 </tag>
            
            <tag> 指南 </tag>
            
            <tag> 游戏 </tag>
            
            <tag> 模拟器 </tag>
            
        </tags>
      
    </entry>
    
    
    
    <entry>
      <title>2025年02月15日-记一次去医院看牙的经历</title>
      <link href="/post/20250215190000.html"/>
      <url>/post/20250215190000.html</url>
      
        <content type="html"><![CDATA[<blockquote><ol><li>如果你是真的能扛顶着牙疼过日子，那恭喜你，最终结果只有一个，那就是根管治疗。</li><li>“牙医恐惧”，首先一定是疼。除了生理上的疼之外，也有心理上的，心疼钱。</li></ol></blockquote><h1 id="前因"><a href="#前因" class="headerlink" title="前因"></a>前因</h1><p>24年年中就感觉牙齿不适，偶尔吃饭时会掉落牙齿碎片，当时没觉得是自己的牙齿损坏了，而是以为饭里有些骨头啥的东西咬碎了，后来刷牙时发现右边牙齿根部似乎出了个洞，但是也不痒也不疼，加上本人对于医院有点惧怕，于是就没有太过在意。</p><p>25年春节情况变得更加严重，由于过年期间大吃大喝，可能刺激到了损坏的牙齿根部，嚼着嚼着一下子咬到硬物时会发生剧烈疼痛，脸都能疼变形的那种，无法忍受的疼，经历了几次疼痛后右侧牙齿几乎无法正常咬碎食物，只能依靠左侧正常牙齿吃饭，但当时想着回苏州可以刷医保，能省不少钱，于是硬生生忍受了下来（我真是神人）。</p><p>终于过完了年，来到苏州的第一件事就是挂牙科的号，经历一顿波折才挂上15号周六上午的号。这期间吃饭时是真的折磨，加上去之前12号的时候牙齿好像还发炎了，隐隐作痛，只能吃布洛芬止痛，夜里也无法好好休息，还好去医院的时候已经消炎了。</p><h1 id="初诊"><a href="#初诊" class="headerlink" title="初诊"></a>初诊</h1><p>专家号是别想了，打工人想在周末挂主流科室的专家号还是很困难的。在苏州12320上挂的苏州华夏口腔医院-牙体牙髓病二科普通门诊的号，时间为15号周六上午10:00-10:30。</p><p>去的时候晚了一点，10:05才在自主取号机取到号，去导引台签到分配医生，由于我第一次来没有熟悉的医生，分配的随机医生，然后找地方坐着等叫号。去的比较晚，没过几分钟就叫到我了，走到门诊室后医生问我有什么问题，我说下右侧的牙疼，医生让我躺在专门的牙科椅子上检查牙齿，上方的灯光还晃了我一下。</p><p>短暂的检查过后说，最里面的牙蛀的很严重，已经影响到牙神经了，可能需要做根管治疗，倒数第二颗也有点损坏，需要补一下，开的诊断单写的慢性牙周炎和龋齿(qǔ chǐ)，治疗方案经过我同意后让我去拍个牙片，给我开了个牙片单子去放射区签到排队，拍了片后不用取，直接回牙医处就行，医生在电脑上看过片后说确定要做根管治疗的，问我要不要做，我同意后方案后医生拿出“根管治疗知情同意书”让我签名，签名后就到了最折磨人的治疗阶段了。</p><h2 id="治疗"><a href="#治疗" class="headerlink" title="治疗"></a>治疗</h2><p>医生会提前说明如有不适、吐口水（有专门的医疗器具吸）、剧烈疼痛需要举左手示意，且会询问你来之前是否吃过饭，我是提前吃了瓶八宝粥和面包。</p><p>根管治疗的时间比较长，所以先做的是补牙，补牙需要将棉花垫在需要补的那颗牙的两侧，用于吸收唾液，防止补牙材料被污染。这个阶段我就出问题了，我的呕吐反应非常严重，舌头不习惯旁边有异物，垫了两次后一直出现干呕无法补牙，于是就让我左侧头控制唾液流向，才可以进行下一步。补牙前需要先把被蛀成黑色的部分磨除，再填充材料，这个过程非常离谱，因为我右侧牙神经已经损坏，有一点轻微的震动就会非常疼，磨得过程得声音也很恐怖，比较反人类。不过过程没什么问题，忍忍就行了。</p><p>接下来时间比较长的就是根管治疗了，在需要根管治疗的牙齿旁边围上一块有弹性的手术布，因为我也看不见，就只能感觉到又是磨又是喷一种冰凉的喷剂，根管的过程基本没什么感觉，偶尔有些疼痛是震动带来的，但因为冰凉的关系感觉也不太痛。我一直以为需要打麻醉针，原来不需要。期间没什么问题，但时间很长，直到11点半左右才完成。</p><h2 id="价格-术后"><a href="#价格-术后" class="headerlink" title="价格&amp;术后"></a>价格&amp;术后</h2><p>首先就是漱口，漱完一阵清爽，起来后医生说2小时内不要吃东西，补的那颗牙暂时不要用舌头去舔，根管的那颗牙要想完全恢复还需要做个牙冠，右侧牙齿近期不要咀嚼，等25号复诊再看看情况。于是我又提前挂了25号上午10点的号。</p><p>本次开销刷的是医保，消费总费用1208.03元，统筹支付186.75元，个人账户支付1021.28元。</p><p>目前的感觉非常好，就是吃饭的时候只能用左侧牙吃饭有些不习惯，吃着吃着就不自觉的左侧头。有人术后还有牙疼的情况，我这里几乎感觉不到，等牙冠一做就又能正常吃饭了，就等着复诊了…</p><h1 id="复诊"><a href="#复诊" class="headerlink" title="复诊"></a>复诊</h1><p>依旧自行取号，但是这次去导诊台签到的同时需要指定复诊医生，签完到坐等叫号，轮到我时医生让我先去拍之前做根管牙的牙片（是真快啊，大约10秒就出片），拍完后回到医生处就开始做检查根管情况。中途也没什么意外，做完牙冠后又让我去拍个片子，拍完回来后问我是否牙冠过高影响咀嚼，然后开始修正一下牙冠高度，让牙冠和周围牙齿齐平，然后就完事了，不过仍然要在90天后看根管的恢复情况。</p><h2 id="价格-术后-1"><a href="#价格-术后-1" class="headerlink" title="价格&amp;术后"></a>价格&amp;术后</h2><p>本次开销刷医保卡，消费总费用815.03，统筹支付312.3元，个人支付502.73元<br><img src="/images/posts/2025/021501/2025021501001.png" alt="复诊开销"></p><p>这里我注意到有个根管填充术的收费项目，难道复诊时还需要清理根管內部？</p><p>2025&#x2F;02&#x2F;27，已经过去两天了，现在能够正常吃饭简直是太爽了~</p><h1 id="其他"><a href="#其他" class="headerlink" title="其他"></a>其他</h1><ol><li>复诊完成后一直在听医生说的注意事项，忘记让医生写张病假单了，只能用一天年假血亏。</li><li>医生说左边的牙齿情况也要注意，但是如果要进一步检查和治疗的话得多等一段时间后。</li><li>当天下午预约了 苏州博物馆本馆 ，我以为牙冠做的很快，实际消耗了不少时间，我又没带相机，只能又做地铁回家拿，浪费了不少时间（血亏年假）。</li></ol><h1 id="参考"><a href="#参考" class="headerlink" title="参考"></a>参考</h1><ol><li><a href="https://www.bilibili.com/video/BV13j411V7J6">内有大矿！超近距离拍龋齿修复，隔着屏幕看痛了？！  – 影视飓风</a> </li><li><a href="https://www.upi.com/Health_News/2009/03/27/Survey-Most-fear-the-dentist-root-canals/85781238210145/">Survey: Most fear the dentist, root canals.   – 牙医恐惧调查报告</a></li><li><a href="https://sspai.com/post/54300">克服恐惧，我终于也成了有假牙的人  – 少数派</a></li></ol>]]></content>
      
      
      <categories>
          
          <category> 日常 </category>
          
      </categories>
      
      
        <tags>
            
            <tag> 医院 </tag>
            
            <tag> 牙科 </tag>
            
        </tags>
      
    </entry>
    
    
    
    <entry>
      <title>2024年12月03日-Hexo安知鱼主题魔改记录</title>
      <link href="/post/20241203090000.html"/>
      <url>/post/20241203090000.html</url>
      
        <content type="html"><![CDATA[<h2 id="1-即刻短文"><a href="#1-即刻短文" class="headerlink" title="1. 即刻短文"></a>1. 即刻短文</h2><h3 id="1-1-多行文本"><a href="#1-1-多行文本" class="headerlink" title="1.1 多行文本"></a>1.1 多行文本</h3><h4 id="1-1-1-效果展示"><a href="#1-1-1-效果展示" class="headerlink" title="1.1.1 效果展示"></a>1.1.1 效果展示</h4><p><img src="/images/posts/2024120301/202412030101.png" alt="即刻短文多行文本"></p><h4 id="1-1-2-开始修改"><a href="#1-1-2-开始修改" class="headerlink" title="1.1.2 开始修改"></a>1.1.2 开始修改</h4><p>找到 <code>/themes/anzhiyu/layout/includes/page/essay.pug</code>文件，将</p><pre class="line-numbers language-pug" data-language="pug"><code class="language-pug"><span class="token tag">p<span class="token attr-class">.datacont</span></span><span class="token punctuation">=</span><span class="token code"> item<span class="token punctuation">.</span>content</span><span aria-hidden="true" class="line-numbers-rows"><span></span></span></code></pre><p>修改为</p><pre class="line-numbers language-pug" data-language="pug"><code class="language-pug"><span class="token tag">div<span class="token attr-class">.datacont</span></span>  <span class="token flow-control"><span class="token each"><span class="token keyword">each</span> paragraph <span class="token keyword">in</span></span> item<span class="token punctuation">.</span>content<span class="token punctuation">.</span><span class="token function">split</span><span class="token punctuation">(</span><span class="token string">"\n"</span><span class="token punctuation">)</span></span>    <span class="token flow-control"><span class="token branch keyword">if</span> paragraph<span class="token punctuation">.</span><span class="token function">trim</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">!==</span> <span class="token string">""</span></span>      <span class="token tag">p</span><span class="token punctuation">=</span><span class="token code"> paragraph</span><span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span><span></span></span></code></pre><p>修改时注意代码缩进，p与div同级替换。</p><p><code>essay.yml</code>文件中，使用 <code>|</code> 保证段落换行，例如：</p><pre class="line-numbers language-yaml" data-language="yaml"><code class="language-yaml"><span class="token key atrule">essay_list</span><span class="token punctuation">:</span>  <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> <span class="token punctuation">|</span><span class="token scalar string">      你求名利，他卜吉凶，可怜我全无心肝，怎出得什么主意？      殿遏烟云，堂列钟鼎，堪笑人供此泥木，空费了许多钱财。</span>    <span class="token key atrule">date</span><span class="token punctuation">:</span> 2024/11/27 9<span class="token punctuation">:</span><span class="token datetime number">25:00</span><span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span><span></span><span></span></span></code></pre><p>不想换行就按照之前的说说样式写就行，或者<code>|</code>只写一行</p><h2 id="2-侧边栏"><a href="#2-侧边栏" class="headerlink" title="2. 侧边栏"></a>2. 侧边栏</h2><h3 id="2-1-steam游戏卡片"><a href="#2-1-steam游戏卡片" class="headerlink" title="2.1 steam游戏卡片"></a>2.1 steam游戏卡片</h3><p>参考：<a href="https://cf.lxb.icu/undefined/29816">李小白的博客-侧边栏steam卡片</a></p><h4 id="2-1-1-效果展示"><a href="#2-1-1-效果展示" class="headerlink" title="2.1.1 效果展示"></a>2.1.1 效果展示</h4><p><img src="/images/posts/2024120301/202412030102.png" alt=" steam游戏卡片"></p><h4 id="2-1-1-开始修改"><a href="#2-1-1-开始修改" class="headerlink" title="2.1.1 开始修改"></a>2.1.1 开始修改</h4><p>跳转到 steam 卡片生成网站：<a href="https://cardn.yuy1n.io/">Steam Card</a>，使用steam账户登录，按照网页中的Config项配置你想要的卡片效果，右侧找到<code>HTML</code>标签卡片，点击复制。</p><p>新建<code>widget.yml</code>文件，完整路径为<code>source/_data/widget.yml</code>，内容如下</p><pre class="line-numbers language-yaml" data-language="yaml"><code class="language-yaml"><span class="token comment"># top: 创建的 widget 会出现在非 sticky 区域（即所有页面都会显示)</span><span class="token comment"># bottom: 创建的 widget 会出现在 sticky 区域（除了文章页都会显示)</span><span class="token key atrule">top</span><span class="token punctuation">:</span>  <span class="token comment"># steam 游戏卡片</span>  <span class="token punctuation">-</span> <span class="token key atrule">class_name</span><span class="token punctuation">:</span> game<span class="token punctuation">-</span>card<span class="token punctuation">-</span>widget    id_name<span class="token punctuation">:</span> game<span class="token punctuation">-</span>card<span class="token punctuation">-</span>widget    name<span class="token punctuation">:</span> 游戏卡片    icon<span class="token punctuation">:</span> fas fa<span class="token punctuation">-</span>gamepad  <span class="token comment"># 你可以根据需要添加图标</span>    order<span class="token punctuation">:</span> <span class="token number">1</span>    html<span class="token punctuation">:</span> <span class="token punctuation">|</span>      &lt;div id="game<span class="token punctuation">-</span>card<span class="token punctuation">-</span>widget"<span class="token punctuation">></span>        这里填入你复制的HTML标签卡片代码全选替换      &lt;/div<span class="token punctuation">></span><span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span></span></code></pre><p>新建<code>costom_widget.css</code>文件，完整路径为<code>source/css/costom_widget.css</code>，内容如下：</p><pre class="line-numbers language-css" data-language="css"><code class="language-css"><span class="token selector">#game-card-widget</span> <span class="token punctuation">&#123;</span>    <span class="token property">display</span><span class="token punctuation">:</span> flex<span class="token punctuation">;</span>    <span class="token property">flex-direction</span><span class="token punctuation">:</span> column<span class="token punctuation">;</span>    <span class="token property">justify-content</span><span class="token punctuation">:</span> flex-end<span class="token punctuation">;</span>    <span class="token punctuation">&#125;</span><span class="token selector">#game-card-widget img</span> <span class="token punctuation">&#123;</span>  <span class="token property">max-width</span><span class="token punctuation">:</span> 100%<span class="token punctuation">;</span>    <span class="token property">height</span><span class="token punctuation">:</span> auto<span class="token punctuation">;</span>  <span class="token punctuation">&#125;</span><span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span></span></code></pre><p>自定义的css文件需要在主题配置文件<code>_config.anzhiyu.yml</code>中<code>inject</code>项导入</p><pre class="line-numbers language-yaml" data-language="yaml"><code class="language-yaml"><span class="token key atrule">inject</span><span class="token punctuation">:</span>  head<span class="token punctuation">:</span>    <span class="token comment"># 自定义css</span>    <span class="token punctuation">-</span> &lt;link rel="stylesheet" href="/css/costom_widget.css"<span class="token punctuation">></span>  bottom<span class="token punctuation">:</span>    <span class="token comment"># 自定义js</span>    <span class="token comment"># - &lt;script src="/js/xxx">&lt;/script></span><span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span><span></span><span></span><span></span><span></span></span></code></pre><p>命令行执行 <code>hexo clean &amp;&amp; hexo g</code> 查看效果</p><h3 id="2-2-网易云音乐卡片-失效"><a href="#2-2-网易云音乐卡片-失效" class="headerlink" title="2.2 网易云音乐卡片(失效)"></a>2.2 网易云音乐卡片(失效)</h3><p>参考：<a href="https://cf.lxb.icu/undefined/57573">李小白的博客-侧边栏网易云音乐</a></p><h4 id="2-2-1-效果展示"><a href="#2-2-1-效果展示" class="headerlink" title="2.2.1 效果展示"></a>2.2.1 效果展示</h4><p><img src="/images/posts/2024120301/202412030103.png" alt="网易云音乐卡片"></p><h4 id="2-2-2-开始修改"><a href="#2-2-2-开始修改" class="headerlink" title="2.2.2 开始修改"></a>2.2.2 开始修改</h4><p><code>注意，由于网络原因，vercel域名被屏蔽，请在项目中自己解析自己的域名，不然只能使用本地部署方式，否则将无法访问云音乐卡片服务。</code></p><p>首先需要部署 <a href="https://github.com/zonemeen/netease-recent-profile">Netease Recent Profile</a> 服务，按照项目教程使用vercel部署或本地部署，我用的是vercel方式，部署完毕后访问云音乐卡片模式的标准网址是：</p><p><a href="https://netease-recent-profile.vercel.app/?id=126764012&size=60">https://netease-recent-profile.vercel.app/?id=126764012&size=60</a></p><p>其中 <code>netease-recent-profile.vercel.app</code>来自于你的vercel项目域名，请替换为你的项目域名</p><p>ID <code>126764012</code>来自于你的网易云音乐ID，具体获取方式查看该项目教程，请替换为你的网易云ID</p><p>接着，新建<code>widget.yml</code>文件，完整路径为<code>source/_data/widget.yml</code>，内容如下，如果你按照本文已经创建过<code>widget.yml</code>文件，在其中追加即可，本小节示例为默认版本，具体src配置项请参照<a href="https://github.com/zonemeen/netease-recent-profile">Netease Recent Profile</a> 项目修改。</p><pre class="line-numbers language-yaml" data-language="yaml"><code class="language-yaml"><span class="token comment"># top: 创建的 widget 会出现在非 sticky 区域（即所有页面都会显示)  </span><span class="token comment"># bottom: 创建的 widget 会出现在 sticky 区域（除了文章页都会显示)  </span><span class="token comment"># 将 src 中的域名和id替换为你自己的项目域名和id。</span><span class="token key atrule">top</span><span class="token punctuation">:</span>    <span class="token punctuation">-</span> <span class="token key atrule">class_name</span><span class="token punctuation">:</span> card<span class="token punctuation">-</span>music      <span class="token key atrule">id_name</span><span class="token punctuation">:</span> card<span class="token punctuation">-</span>widget<span class="token punctuation">-</span>netease      <span class="token key atrule">name</span><span class="token punctuation">:</span> 最近播放      <span class="token key atrule">icon</span><span class="token punctuation">:</span> fas fa<span class="token punctuation">-</span>headphones      <span class="token key atrule">order</span><span class="token punctuation">:</span> <span class="token number">2</span>      <span class="token key atrule">html</span><span class="token punctuation">:</span> <span class="token string">'&lt;img src="https://music.siyouyun.eu.org/?id=1939753826&amp;theme=card&amp;show_rainbow=1&amp;size=300" alt="Netease recently played" width="300" />'</span><span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span></span></code></pre><p>命令行执行 <code>hexo clean &amp;&amp; hexo g</code> 查看效果</p><h4 id="2-2-3-常见问题"><a href="#2-2-3-常见问题" class="headerlink" title="2.2.3 常见问题"></a>2.2.3 常见问题</h4><ol><li><p>部署完成后报错：Something went wrong! file an issue at <a href="https://github.com/zonemeen/netease-recent-profile">https://github.com/zonemeen/netease-recent-profile</a><br>这是代表服务正常运行的情况，想要访问卡片内容，需要在域名中加入id信息，例如：<a href="https://netease-recent-profile.vercel.app/?id=126764012&size=60">https://netease-recent-profile.vercel.app/?id=126764012&size=60</a></p></li><li><p>部署完毕后，域名可以正常访问，但是无法加载音乐内容<br>查看网易云音乐账户最近是否听过歌曲，没有数据无法生成</p></li></ol>]]></content>
      
      
      <categories>
          
          <category> 主题 </category>
          
      </categories>
      
      
        <tags>
            
            <tag> 主题 </tag>
            
            <tag> 魔改 </tag>
            
        </tags>
      
    </entry>
    
    
    
    <entry>
      <title>2024年11月20日-RetroArch全能游戏模拟器</title>
      <link href="/post/20241120092654.html"/>
      <url>/post/20241120092654.html</url>
      
        <content type="html"><![CDATA[<blockquote><ol><li>本文根据我目前已知的信息撰写，如有误请直接指出，望各位不吝赐教。</li><li>非常有意思的是，如果你不是为了整理自己的ROM库，那么完全可以使用合集包，也不用再看下面这么繁琐的教程了。</li></ol></blockquote><h2 id="1-前言"><a href="#1-前言" class="headerlink" title="1. 前言"></a>1. 前言</h2><p>众所周知，很多游戏的玩法并不过时，但运行这些游戏的平台和设备可能已经非常陈旧了。有些设备和平台已经彻底停产，只能通过二手渠道购买（非常贵），有些甚至连二手都无处可寻。所以，想要重温这些经典游戏，通过模拟器来实现是最划算的。</p><p>另外，我在家想玩游戏模拟器时，肯定会选择PC或者平板，大屏设备总是能带来更好的体验；出门在外的闲暇时光，则直接使用手机游玩。因此，我更加需要一个能够跨平台的模拟器，且存档文件能够共享。虽然RetroArch无法直接在软件中设置云存档，但在拥有群晖NAS的情况下，存档同步问题就再简单不过了。</p><h2 id="2-RetroArch-模拟器"><a href="#2-RetroArch-模拟器" class="headerlink" title="2. RetroArch 模拟器"></a>2. RetroArch 模拟器</h2><p>RetroArch 是一个非常强大的开源模拟器前端，支持在线下载核心文件。模拟器通常分为前端和核心两个部分：前端主要是UI界面，而核心才是真正负责运行游戏的部分。如果你对某些软件领域的特定术语不熟悉，也无需深入理解，可以简单地将 RetroArch 理解为一款能够运行几乎所有怀旧游戏的强大软件，只需要在 RetroArch 中下载运行怀旧游戏所需的环境（核心）即可。</p><p>如果你的英文不错，可以直接查阅 RetroArch 模拟器官方文档：<a href="https://docs.libretro.com/">https://docs.libretro.com/</a>，内容非常详尽。</p><h3 id="2-1-下载安装"><a href="#2-1-下载安装" class="headerlink" title="2.1 下载安装"></a>2.1 下载安装</h3><p>官网下载地址：<a href="https://retroarch.com/?page=platforms">https://retroarch.com/?page=platforms</a>。你可以根据自己的需求选择对应平台的安装包进行下载，安装过程非常简单，这里不再赘述。需要注意的是，在 Windows 平台上有两种安装方式：</p><ol><li><strong>直接下载安装包</strong>：下载安装后即可运行。</li><li><strong>通过 Steam 安装</strong>：在 Steam 平台上安装的 RetroArch 支持云存档，无需额外设置，这一功能是通过 Steam 的云存档服务实现的。</li></ol><h3 id="2-2-语言设置"><a href="#2-2-语言设置" class="headerlink" title="2.2 语言设置"></a>2.2 语言设置</h3><p>RetroArch 安装后运行时可能是英文界面，如果需要切换为中文，可以按以下步骤操作：点击 ⚙（设置） -&gt; User -&gt; Language -&gt; Chinese 简体中文。修改语言后，需要退出软件并重新打开才能完全生效。</p><h3 id="2-3-核心下载"><a href="#2-3-核心下载" class="headerlink" title="2.3 核心下载"></a>2.3 核心下载</h3><p>RetroArch 提供快捷菜单，因此有些功能的设置入口不止一种，建议多加熟悉。下面以我正在游玩的 GBA 游戏《宝可梦绿宝石》为例，讲解相关设置。</p><p>原版《宝可梦绿宝石》主要有两种语言版本：日版和美版，但由于对这两种语言不够熟悉，我选择了由 <strong>漫游&amp;TGB&amp;口袋群星SP</strong> 汉化的版本，这个版本被认为是原版绿宝石内容最好的汉化版本。</p><p>确定好游戏后，需要选择游戏对应的核心文件。《宝可梦绿宝石》属于 GBA 平台游戏，因此需要下载 GBA 核心才能游玩。RetroArch 提供多种 GBA 核心，可以自行搜索不同 GBA 核心的区别，mGBA 核心下载方法如下：点击 🏠 -&gt; 在线更新 -&gt; 核心下载 -&gt; Nintendo - Game Boy Advance (mGBA)。</p><p>同一平台的不同核心在性能和功能上可能略有区别，如果想了解具体差异，可以在 GitHub 上搜索这些核心的项目文档进行查看。</p><h3 id="2-4-开始游戏"><a href="#2-4-开始游戏" class="headerlink" title="2.4 开始游戏"></a>2.4 开始游戏</h3><p>注意，这里只是演示，实际操作时你需要根据自己想玩的游戏选择对应的核心，不要照抄。具体步骤如下：</p><ol><li>点击 <strong>🏠 -&gt; 加载核心 -&gt; Nintendo - Game Boy Advance (mGBA)</strong></li><li>然后选择 <strong>加载游戏</strong>，接着找到并选择你想玩的游戏文件。</li></ol><p>当你看到游戏画面和模拟摇杆界面时，说明一切设置已经完成，接下来就可以开始愉快的怀旧游戏之旅了。</p><p>这是最简单的过程了，但实际上我们可能会遇到各种各样的问题，我会在 进阶设置 中讲解。</p><h3 id="2-5-存档"><a href="#2-5-存档" class="headerlink" title="2.5 存档"></a>2.5 存档</h3><p>RetroArch 模拟器的存档分为两种：游戏存档和状态存档。</p><ul><li><strong>游戏存档</strong>：顾名思义，就是游戏本身的存档功能，保存的是游戏内的进度。</li><li><strong>状态存档</strong>：模拟器的即时保存功能，可以保存游戏当前的状态，比游戏存档更强大。</li></ul><p>这两种存档对应的文件夹不同，所以如果想在不同平台上继续游玩同一款游戏的进度，就需要将这两个文件夹一起保存并迁移。</p><p>无论是哪种平台，存档文件夹的名称都是一致的，只需要将整个文件夹复制保存。具体位置如下：</p><ul><li><strong>状态存档</strong>：<code>RetroArch 安装目录/states/</code></li><li><strong>游戏存档</strong>：<code>RetroArch 安装目录/saves/</code></li></ul><p>我的建议是直接备份这两个文件夹，而不是单独备份状态文件或游戏存档文件，这样可以避免在迁移时无法识别存档。怕麻烦的话直接在Steam中使用RetroArch模拟器，就无需担心存档的保存问题了。</p><p><strong>！！！注意 ！！！</strong></p><p>各平台的游戏名称请保持一致，存档以文件名为准，如果修改游戏名可能不识别存档。</p><p>网上下载的游戏文件，一定要先测试游戏内的存档功能在模拟器上是否可用。如果你过于依赖模拟器的即时存档功能，而没有发现这个问题，就可能导致在开启二周目时无法正确读取存档，进度无法继续。这是因为有些游戏的汉化、特殊版本可能在模拟器中无法正常保存或读取进度，所以务必先确认游戏本身的存档功能正常后，再开始游玩。</p><h2 id="3-游戏资源下载"><a href="#3-游戏资源下载" class="headerlink" title="3. 游戏资源下载"></a>3. 游戏资源下载</h2><ol><li>老男人游戏网: <a href="https://www.oldmantvg.net/">https://www.oldmantvg.net/</a></li><li>掌机迷: <a href="https://www.gbarom.cn/">https://www.gbarom.cn/</a></li></ol><h2 id="Q-A"><a href="#Q-A" class="headerlink" title="Q&amp;A"></a>Q&amp;A</h2><ol><li><p>RetroArch 安装好后打开不了&#x2F;打开报错怎么办？<br>注意安装路径要为纯英文目录，保持良好安装习惯</p></li><li><p>RetroArch 中核心下载列表加载不出来&#x2F;下载失败怎么办？<br>一般是因为运营商的屏蔽问题，Github访问不了一般也是这个原因，网络问题请自行解决。</p></li></ol>]]></content>
      
      
      <categories>
          
          <category> 游戏 </category>
          
      </categories>
      
      
        <tags>
            
            <tag> 日常 </tag>
            
            <tag> 指南 </tag>
            
            <tag> 游戏 </tag>
            
            <tag> 模拟器 </tag>
            
        </tags>
      
    </entry>
    
    
    
    <entry>
      <title>2024年10月10日-流量卡选购指南</title>
      <link href="/post/20241010144421.html"/>
      <url>/post/20241010144421.html</url>
      
        <content type="html"><![CDATA[<blockquote><p>注意，2025年2月中旬起，好像各大运营商又出比较高质量的流量卡套餐了，各位可以去瞅瞅。</p></blockquote><blockquote><p>本文仅作为科普文章，本人不参与其中任何交易环节，如果有任何问题都可以通过社交软件联系我，我非常乐意提供力所能及的帮助。</p></blockquote><blockquote><p>非常遗憾的是，截止到本文的写作开始，最近一次的三网竞合已经结束，现在已经选购不到我副卡2的这种套餐了，现在大都是29元80G流量的套餐，优惠力度已然大减。</p></blockquote><h2 id="当前情况"><a href="#当前情况" class="headerlink" title="当前情况"></a>当前情况</h2><p>我总共拥有4张SIM卡，每张卡都有不同的用途，并且每张卡都是正规运营商的卡，且和身份信息绑定，不存在违规违法的情况。</p><ol><li><p>主卡联通卡1：月租8元，早先申请的联通大王卡，后来加了一张流量卡就把这张主卡的套餐更换为了联通8元保号套餐。是联系我的最终方式，其中绑定了许多网站、银行，基本无法舍弃。</p></li><li><p>副卡电信流量卡2：月租19元，是前几年申请的湖北流量卡，主套餐是29元星卡，但是叠加了125G的全国流量和30G的定向流量，且可以参与充100反220（每月反10）的活动，活动返完可继续参与，所以综合月租是19元。是我没有WIFI的情况下主要的上网方式，实际上每个月根本用不了这么多。</p></li><li><p>宽带卡移动卡3：月租33元，8月租+15元500M+10元千兆提速，是我来到苏州时办理的宽带卡，家里必备的宽带，这个不可省略，本来是想看看主卡1和副卡2能不能办理跨省宽带的，但是不太划算。</p></li><li><p>英国Giffgaff卡4：无月租，每半年需要变动一次余额才能保号，主要用来注册国外的网站和服务，例如chatgpt、github等等，有些国家和地区是把+86的号码排除在外的，国内的手机不能注册和使用。</p></li></ol><p>总共的月租是8+19+33&#x3D;60元，感觉确实有点高了，准备过段时间把宽带的资费降一降。</p><h2 id="流量卡"><a href="#流量卡" class="headerlink" title="流量卡"></a>流量卡</h2><p>首先明确一件事，物联卡有时也被叫做流量卡，但是物联卡和流量卡确实不是同一种，你在网购平台搜索流量卡可能会遇到一些标为流量卡但是实际是物联卡的情况，要仔细辨别。</p><h3 id="认识流量卡"><a href="#认识流量卡" class="headerlink" title="认识流量卡"></a>认识流量卡</h3><h4 id="历史"><a href="#历史" class="headerlink" title="历史"></a>历史</h4><p>早先在三网竞合还未开始之前，移动、联通、电信为了抢占用户，推出了许多月租极低、流量极多的套餐，搭载了这些性价比极高套餐的SIM卡被叫做流量卡。</p><p>前几年有些无限流量卡就是这么出来的，这些流量卡一般是用到40G就降速到128k，但是随着时代发展运营商发现这些套餐非常不划算，于是三家收紧策略，统一不再推出无限流量卡套餐，直到无限流量套餐逐渐消失的无影无踪，你现在去贴吧还能看到有人高价收购带有这些无限套餐的流量卡。</p><p>不记得多少年前，三网各个省份的运营商还有着自己独立的App，后来也逐渐整合到一个App，就是我们现在用的中国移动App、中国电信App、中国联通App，但还是能在应用内选择地区运营商，你地区选苏州就是国内服务+地区运营商的活动。</p><h4 id="现状"><a href="#现状" class="headerlink" title="现状"></a>现状</h4><p>同样，现在各省份地区运营商为了新用户会推出一些优惠套餐，一般是星卡主套餐29元 + 叠加包（叠加包内容一般是赠送）。例如我的副卡流量卡2，是被地区运营商叫做阳光卡，套餐我在上面也有做介绍，这种流量卡是正规运营商的SIM卡，在对应的运营商App中能够查询套餐信息，任意平台都能够充值话费，根本的说，就是一张普普通通的卡。</p><h4 id="选购对比"><a href="#选购对比" class="headerlink" title="选购对比"></a>选购对比</h4><p>现在的流量卡又分为三种，一种是短期卡、一种是长期卡、最后一种是短期可续长期但是续约定义比较模糊的，这里的短期和长期不是指SIM的使用期限，而是指套餐的优惠长短。有些套餐是长期优惠，优惠时间可达5年、20年、40年，有些套餐是短期优惠，优惠时间短则6个月，长则2年。下面我会用几个例子做对比。</p><h5 id="例子1：移动沧蕾卡"><a href="#例子1：移动沧蕾卡" class="headerlink" title="例子1：移动沧蕾卡"></a>例子1：移动沧蕾卡</h5><ol><li><p>月租29元、首月免月租</p></li><li><p>原套餐：5G通用流量+30G定向流量</p></li><li><p>优惠详情：任意渠道首充100元可享受优惠活动，叠加每月45G通用流量，优惠期12个月，到期无异议可续。</p></li><li><p>禁发北京、云南、西藏、黑龙江</p></li></ol><h5 id="例子2：电信沧欢卡"><a href="#例子2：电信沧欢卡" class="headerlink" title="例子2：电信沧欢卡"></a>例子2：电信沧欢卡</h5><ol><li><p>月租39元</p></li><li><p>原套餐：5G通用流量+30G定向流量</p></li><li><p>激活赠送40元体验金，体验金不可结转（首月免月租）</p></li><li><p>优惠详情：快递处或指定链接首充50元可享受优惠活动，充50送120，50即刻到账，120分12月返还，每月反10元，每月赠送45G通用流量叠加包，可参加两次。</p></li><li><p>店铺叠加优惠：参加首充活动后2-13个月月底反10元，第14月再次参加50送120后，第14月反20，15-24月反10元</p></li><li><p>综上所述，首月免月租，2-13月19元每月，14月19元每月，15-24月19元每月，24月后29元每月。</p></li><li><p>禁发北京、云南、西藏、黑龙江</p></li></ol><h5 id="例子3：联通长期卡"><a href="#例子3：联通长期卡" class="headerlink" title="例子3：联通长期卡"></a>例子3：联通长期卡</h5><ol><li><p>月租39元</p></li><li><p>原套餐：15G通用流量+30G定向流量</p></li><li><p>优惠详情：任意渠道首充100元可享受优惠活动，叠加每月200G通用流量，有效期至2029年12月31日，到期自动续约。</p></li><li><p>禁发北京、云南、西藏、黑龙江</p></li></ol><p>我们逐个分析，首先要确认优惠时长，例子1明显算是短转长，这个“到期无异议可续”是运营商的补充条款，所以含有此种类似描述的，也有概率不可续约。例子2明显是两年短期套餐，这种套餐一般都是优惠比较大，但是两年后就必须注销更换，比较麻烦，否则优惠结束非常不划算。例子3是最容易懂的长期套餐了，这种标识套餐有效期到2029年、2049年、2099年的一般都是长期套餐，这种就无需担忧运营商续约的情况了。这三种套餐我最不推荐例子2的短期套餐，到优惠结束还要注销费时费力。但这个就见仁见智，各自选择了。</p><h4 id="购买方式"><a href="#购买方式" class="headerlink" title="购买方式"></a>购买方式</h4><p>由前文我们可以知道，流量卡一般是地区运营商为了拉新推出的优惠套餐，这种拉新一般都存在代理架构，我们在淘宝、拼多多、各大短视频、长视频网站上能够搜索到店铺的一般都是区域运营商下的代理商，代理商也有不同的级别划分，各家代理销售的流量卡都有返利，不同级别的代理返利不同。例如你在这个代理商这里下单了例子3的长期卡，这个卡你拿到后激活并首充，这个代理如果是黑钻V1级别，返利就是120元现金，而你在各个平台上下单有时还要收你一笔钱，这是不是很不可思议，代理能够两头赚就是源于信息差。</p><p>一级代理商一般是以公司为代表的平台机构，例如172号卡平台（仅做科普，不含任何链接、推广，不含任何利益相关，请注意辨别官方平台真假），例子3的长期卡就是我在里面摘抄的资料。但是该平台不接受个人的一级代理，我在一个好友那里拿到了他的下级黑钻V1代理，可以直接看到例子3的长期卡的返利是120元，激活现结。</p><p>综上所述，如果你真的很想要一张流量卡作为副卡，那么完全可以自己做代理，然后自己办卡，拿自己的返利，100左右的返利相当于3-4个月的月租了，并且如果有首充100赠220的话，相当于白嫖运营商一年还多了。如果你真的很懒，不想这么麻烦，可以按照 选购对比 中的方式找到你想要的套餐，直接在代理商下单即可，一般是免费的，也不排除有黑心商家想两头赚，再收你一笔。</p><h2 id="注意事项"><a href="#注意事项" class="headerlink" title="注意事项"></a>注意事项</h2><ol><li><p>流量卡套餐优先选择限制较小的，使用期限长的。</p></li><li><p>广电的流量卡谨慎选择，优先选择移动、联通、电信。</p></li><li><p>有些卡有注销限制，尽量选择能够在线注销的，尽管都可以停机注销。</p></li><li><p>下单尽量选择粉丝多的博主或者销量高的店铺，少一点套路，多一点真诚。</p></li><li><p>激活时要严格按照当时选购时的首充要求，否则可能没有优惠。</p></li></ol>]]></content>
      
      
      <categories>
          
          <category> 日常 </category>
          
      </categories>
      
      
        <tags>
            
            <tag> 日常 </tag>
            
            <tag> 指南 </tag>
            
        </tags>
      
    </entry>
    
    
    
    <entry>
      <title>2024年09月02日-大抵只有我被困在那段时光里了</title>
      <link href="/post/20240902145411.html"/>
      <url>/post/20240902145411.html</url>
      
        <content type="html"><![CDATA[<p>高中时喜欢一个女生，从上大学起就没联系了，但个人单方面一直念念不忘。前段时间鼓起勇气要到了她的微信，冒犯地看了朋友圈，看了看最近动态，照片里的她依旧显得如此可爱。有些不同的是，这相片里的女生，我好像有些认不出来了。</p><p>印象当中她头发长长的，眼睛大大的，闪闪发亮，微微翘起的嘴角笑起来很甜很可爱。</p><p>如果不是很确定微信里的就是本人，可能让我重新看我大抵也是认不出来了(笑)。明明同样是在笑，可我总是感觉，她不如她。</p><p>可笑的是我的悸动在看到最近的照片之后突然有点停止了，然后再回忆她之前的样子，感觉区别好像有点太大了。</p><p>说来也奇怪，到现在为止，我接触过的女生里，她是唯一一个能让我念念不忘的人。算上年头今年应该八年了，我经常跟朋友开玩笑说，想忘记一个人最好的方法就是去跟她见一面，看看她最近的样子。可要是真见到了，又会感觉到惆怅。也会开始怀疑，这些年的时间，我究竟是在想念着她，还是在眷恋与她在学校独处时相笑的时光。</p><p>大抵只有我被困在那段时光里了，清晰记得，上晚修的前夕，我和她走在操场上，晚风轻拂，吹过我们的身旁。这一刻时间仿佛静止了一般，她看着操场，而我看着她，看着她在夕阳下的模样，我的心不自主的开始跳动了起来，从未停止，直到现在。</p><p>也许，是时候继续前行了。</p>]]></content>
      
      
      <categories>
          
          <category> 日常 </category>
          
      </categories>
      
      
        <tags>
            
            <tag> 情绪 </tag>
            
        </tags>
      
    </entry>
    
    
  
  
</search>

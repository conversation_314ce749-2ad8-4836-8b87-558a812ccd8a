// 检查日期是否在7天内
function isWithinSevenDays(dateStr) {
  const date = new Date(dateStr.replace(/\./g, '-'));
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= 7;
}

// 添加新标识
function addNewBadge() {
  // 检查电影页面
  const movieGroups = document.querySelectorAll('.goodmovies-item');
  if (movieGroups.length > 0) {
    processGroups(movieGroups, 'watch-date', '电影');
  }
  
  // 检查阅读页面
  const bookGroups = document.querySelectorAll('.goodbooks-item');
  if (bookGroups.length > 0) {
    processGroups(bookGroups, 'read-date', '阅读');
  }

  // 检查游戏页面
  const gameGroups = document.querySelectorAll('.goodgames-item');
  if (gameGroups.length > 0) {
    processGroups(gameGroups, 'play-date', '游戏');
  }

  // 检查打卡页面
  const attendanceGroups = document.querySelectorAll('.goodattendance-item');
  if (attendanceGroups.length > 0) {
    processGroups(attendanceGroups, 'checkin-date', '打卡');
  }
}

// 处理分组数据
function processGroups(groups, dateClass, type) {
  groups.forEach((group, groupIndex) => {
    const title = group.querySelector('.goodmovies-title, .goodbooks-title, .goodgames-title, .goodattendance-title')?.textContent || '未命名';
    
    // 获取该标题下的所有项目
    const items = group.querySelectorAll('.movies-item-content-item, .books-item-content-item, .games-item-content-item, .attendance-item-content-item');
    
    // 检查该标题下的项目，直到找到不在7天内的
    let foundRecent = false;
    for (const item of items) {
      const dateElement = item.querySelector(`.games-item-content-item-specification-play-date, .movies-item-content-item-specification-watch-date, .read-date, .attendance-item-content-item-specification-checkin-date, .attendance-item-content-item-specification`);
      if (dateElement) {
        const specText = dateElement.textContent;
        const dateMatch = specText.match(/\d{4}\.\d{1,2}\.\d{1,2}/);
        if (dateMatch) {
          const dateStr = dateMatch[0];
          
          if (isWithinSevenDays(dateStr)) {
            // 创建新标识
            const badge = document.createElement('div');
            badge.className = 'new-badge';
            badge.textContent = '新';
            
            // 将标识添加到整个数据框
            item.style.position = 'relative';
            item.appendChild(badge);
            foundRecent = true;
          } else {
            break;
          }
        }
      }
    }
  });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', addNewBadge);

// PJAX 加载完成后执行
document.addEventListener('pjax:complete', addNewBadge);

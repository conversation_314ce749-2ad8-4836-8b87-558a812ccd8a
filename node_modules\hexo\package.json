{"name": "hexo", "version": "7.3.0", "description": "A fast, simple & powerful blog framework, powered by Node.js.", "main": "dist/hexo", "bin": {"hexo": "./bin/hexo"}, "scripts": {"prepublishOnly": "npm install && npm run clean && npm run build", "build": "tsc -b", "clean": "tsc -b --clean", "eslint": "eslint lib test", "pretest": "npm run clean && npm run build", "test": "mocha test/scripts/**/*.ts --require ts-node/register", "test-cov": "c8 --reporter=lcovonly npm test -- --no-parallel", "prepare": "husky install"}, "files": ["dist/", "bin/"], "types": "./dist/hexo/index.d.ts", "repository": "hexojs/hexo", "homepage": "https://hexo.io/", "funding": {"type": "opencollective", "url": "https://opencollective.com/hexo"}, "keywords": ["website", "blog", "cms", "framework", "hexo"], "author": "<PERSON> <<EMAIL>> (https://zespia.tw)", "maintainers": ["<PERSON><PERSON> <<EMAIL>> (https://abnerchou.me)"], "license": "MIT", "dependencies": {"abbrev": "^2.0.0", "archy": "^1.0.0", "bluebird": "^3.7.2", "hexo-cli": "^4.3.2", "hexo-front-matter": "^4.2.1", "hexo-fs": "^4.1.3", "hexo-i18n": "^2.0.0", "hexo-log": "^4.0.1", "hexo-util": "^3.3.0", "js-yaml": "^4.1.0", "js-yaml-js-types": "^1.0.0", "micromatch": "^4.0.4", "moize": "^6.1.6", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "nunjucks": "^3.2.3", "picocolors": "^1.0.0", "pretty-hrtime": "^1.0.3", "resolve": "^1.22.0", "strip-ansi": "^6.0.0", "text-table": "^0.2.0", "tildify": "^2.0.0", "titlecase": "^1.1.3", "warehouse": "^5.0.1"}, "devDependencies": {"0x": "^5.1.2", "@types/abbrev": "^1.1.3", "@types/bluebird": "^3.5.37", "@types/chai": "^4.3.11", "@types/js-yaml": "^4.0.9", "@types/mocha": "^10.0.6", "@types/node": "^18.11.8 <18.19.9", "@types/nunjucks": "^3.2.2", "@types/sinon": "^17.0.3", "@types/text-table": "^0.2.4", "c8": "^9.0.0", "chai": "^4.3.6", "cheerio": "0.22.0", "decache": "^4.6.1", "eslint": "^8.48.0", "eslint-config-hexo": "^5.0.0", "hexo-renderer-marked": "^6.0.0", "husky": "^8.0.1", "lint-staged": "^15.2.0", "mocha": "^10.0.0", "sinon": "^17.0.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "engines": {"node": ">=14"}}
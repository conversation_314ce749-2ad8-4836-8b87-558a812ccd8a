# top: 创建的 widget 会出现在非 sticky 区域（即所有页面都会显示)
# # bottom: 创建的 widget 会出现在 sticky 区域（除了文章页都会显示)
# bottom:
#   # steam 游戏卡片
#   - class_name: game-card-widget
#     id_name: game-card-widget
#     name: 游戏卡片
#     icon: fas fa-gamepad  # 你可以根据需要添加图标
#     order: 1
#     # html: |
#     #   <div id="game-card-widget">
#     #     <img width="400" height="140" src="https://card.yuy1n.io/card/76561199172600200/dark,en,badge,group,bg-game-2358720">
#     #   </div>
#     html: '<img width="400" height="140" src="https://card.yuy1n.io/card/76561199172600200/dark,en,badge,group,bg-game-2358720">'

  # 网易云音乐卡片 (网易云API失效，暂时注释掉)
  # - class_name: card-music
  #   id_name: card-widget-netease
  #   name: 最近播放
  #   icon: fas fa-headphones
  #   order: 2
  #   html: '<img src="https://netease.shijie.icu/?id=504361477&theme=card&show_rainbow=1&size=300" alt="Netease recently played" width="300" />'

top:
  - class_name: card-countdown
    id_name: 
    name: 
    icon:
    html: |
      <div class="cd-count-left">
        <span class="cd-text">距离</span>
        <span class="cd-name" id="eventName"></span>
        <span class="cd-time" id="daysUntil"></span>
        <span class="cd-date" id="eventDate"></span>
      </div>
      <div id="countRight" class="cd-count-right"></div>
  # last.fm侧边栏卡片
  - class_name: music-widget
    id_name:
    name:
    icon:
    html: |
      <div class="music-widget">
        <div id="musicStatus" class="music-status"></div>
        <img id="musicCover" class="music-cover" src="" alt="专辑封面">
      </div>
      <div class="music-info">
        <div id="musicTitle" class="music-title"></div>
        <div id="musicArtist" class="music-artist"></div>
        <div id="musicAlbum" class="music-album"></div>
      </div>
// 音乐世界页面
.musics-title
  margin: 1rem 0
  line-height: 1;
.musics-item
  .musics-item-content
    display: flex
    flex-direction: row
    flex-wrap: wrap
    gap: 12px // 控制项目间距
    .musics-item-content-item
      display: flex
      flex-direction: row // 保持横向布局
      width: calc(33.33% - 12px) // 每行放置3个
      height: 120px // 统一高度
      border-radius: 12px
      border: var(--style-border-always)
      overflow: hidden
      background: var(--anzhiyu-card-bg)
      box-shadow: var(--anzhiyu-shadow-border)
      position: relative
      transition: transform 0.5s ease

      // 中等屏幕布局调整
      +maxWidth1200()
        width: calc(50% - 16px) // 中屏幕时每行2个

      // 小屏幕布局调整
      +maxWidth768()
        width: 100% // 小屏幕时每行1个，保留横向布局
        flex-direction: row // 强制保持图片在左、文字在右
        height: 120px // 设置小屏幕时盒子的高度

      // 鼠标悬停在整个 box 时触发旋转效果
      &:hover img.musics-item-content-item-image
        animation: rotateCD 8s linear infinite
        transform-origin: center

      // 图片部分
      .musics-item-content-item-cover
        flex: 0 0 120px
        height: 120px
        border-radius: 50%
        overflow: hidden
        background: var(--style-background)
        display: flex
        justify-content: center
        align-items: center
        margin: auto // 添加此行，让图片 box 水平和垂直居中
        position: relative
        padding: 10px

        // 修改为填满整个 a 标签容器
        a
          display: block
          width: 100%
          height: 100%

        img.musics-item-content-item-image
          object-fit: cover
          width: 100%
          height: 100%
          border-radius: 50%
          transition: transform 0.5s ease // 添加过渡效果

      // 文字部分
      .musics-item-content-item-info
        flex: 1
        display: flex
        flex-direction: column

        .musics-item-content-item-name
          font-size: 20px // 加大字体
          font-weight: 700 // 加粗文字
          line-height: 1.1
          margin-bottom: 10px
          margin-top: 15px
          white-space: nowrap
          overflow: hidden
          text-overflow: ellipsis
          cursor: pointer
          &:hover
            color: var(--anzhiyu-main)

        .musics-item-content-item-specification
          font-size: 14px // 中等大小字体
          color: var(--anzhiyu-secondtext)
          line-height: 16px
          margin-bottom: 10px
          white-space: nowrap
          overflow: hidden
          text-overflow: ellipsis

        .musics-item-content-item-description
          font-size: 14px
          font-weight: 700
          line-height: 20px
          color: var(--anzhiyu-secondtext)
          display: -webkit-box
          -webkit-line-clamp: 2
          -webkit-box-orient: vertical
          overflow: hidden
body[data-type="musics"] #web_bg
  background: var(--anzhiyu-background);
body[data-type="musics"] #page
  border: 0;
  box-shadow: none !important;
  padding: 0 !important;
  background: 0 0 !important;
body[data-type="musics"] #page .page-title
  display: none;

// 定义旋转动画
@keyframes rotateCD
  0%
    transform: rotate(0deg)
  100%
    transform: rotate(360deg)
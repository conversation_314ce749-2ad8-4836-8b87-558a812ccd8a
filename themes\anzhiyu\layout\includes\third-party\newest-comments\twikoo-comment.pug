script.
  window.addEventListener('load', () => {
    const changeContent = (content) => {
      if (content === '') return content

      content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[!{_p("aside.card_newest_comments.image")}]') // replace image link
      content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[!{_p("aside.card_newest_comments.link")}]') // replace url
      content = content.replace(/<pre><code>.*?<\/pre>/gi, '[!{_p("aside.card_newest_comments.code")}]') // replace code
      content = content.replace(/<[^>]+>/g,"") // remove html tag

      if (content.length > 150) {
        content = content.substring(0,150) + '...'
      }
      return content
    }

    const getComment = () => {
      const runTwikoo = () => {
        twikoo.getRecentComments({
          envId: '!{theme.twikoo.envId}',
          region: '!{theme.twikoo.region}',
          pageSize: !{theme.newest_comments.limit},
          includeReply: true
        }).then(function (res) {
          const twikooArray = res.map(e => {
            return {
              'content': changeContent(e.comment),
              'avatar': e.avatar,
              'nick': e.nick,
              'url': e.url + '#' + e.id,
              'date': new Date(e.created).toISOString()
            }
          })

          saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), !{theme.newest_comments.storage}/(60*24))
          generateHtml(twikooArray)
        }).catch(function (err) {
          const $dom = document.querySelector('#card-newest-comments .aside-list')
          $dom.textContent= "!{_p('aside.card_newest_comments.error')}"
        })
      }

      if (typeof twikoo === 'object') {
        runTwikoo()
      } else {
        getScript('!{url_for(theme.asset.twikoo)}').then(runTwikoo)
      }
    }

    const generateHtml = array => {
      let result = ''

      if (array.length) {
        for (let i = 0; i < array.length; i++) {
          result += '<div class=\'aside-list-item\'>'

          if (!{theme.newest_comments.avatar}) {
            const name = '!{theme.lazyload.enable ? "data-lazy-src" : "src"}'
            result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
          }
          
          result += `<div class='content'>
          <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
          <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
          </div>`
        }
      } else {
        result += '!{_p("aside.card_newest_comments.zero")}'
      }

      let $dom = document.querySelector('#card-newest-comments .aside-list')
      $dom && ($dom.innerHTML= result)
      window.lazyLoadInstance && window.lazyLoadInstance.update()
      window.pjax && window.pjax.refresh($dom)
    }

    const newestCommentInit = () => {
      if (document.querySelector('#card-newest-comments .aside-list')) {
        const data = saveToLocal.get('twikoo-newest-comments')
        if (data) {
          generateHtml(JSON.parse(data))
        } else {
          getComment()
        }
      }
    }

    newestCommentInit()
    document.addEventListener('pjax:complete', newestCommentInit)
  })

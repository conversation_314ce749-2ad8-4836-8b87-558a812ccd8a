menu:
  文章:
    隧道: /archives/ || anzhiyu-icon-box-archive
    分类: /categories/ || anzhiyu-icon-shapes
    标签: /tags/ || anzhiyu-icon-tags

  # 友链:
  #   友人帐: /link/ || anzhiyu-icon-link
  #   朋友圈: /fcircle/ || anzhiyu-icon-artstation
  #   留言板: /comments/ || anzhiyu-icon-envelope

  生活:
  #   音乐馆: /music/ || anzhiyu-icon-music
  #   追番页: /bangumis/ || anzhiyu-icon-bilibili
    即刻: /essay/ || anzhiyu-icon-lightbulb
    相册: /album/ || anzhiyu-icon-images
    装备: /equipment/ || anzhiyu-icon-keyboard
    财务: /finance/ || anzhiyu-icon-chart-line
    订阅（正在施工）: /subscriptions/ || anzhiyu-icon-cube
  #   小空调: /air-conditioner/ || anzhiyu-icon-fan

  记录:
  #   关于本人: /about/ || anzhiyu-icon-paper-plane
  #   随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1
    阅读: /books/ || anzhiyu-icon-book
    音乐: /musics/ || anzhiyu-icon-music
    影视: /movies/ || anzhiyu-icon-bilibili
    游戏: /games/ || anzhiyu-icon-dice
    打卡: /attendance/ || anzhiyu-icon-paper-plane
    # 更新日志: /update/ || anzhiyu-icon-bullhorn
  
  # 服务:
  #   游戏: https://gm.shijie.icu/ || anzhiyu-icon-dice
  #   影视: https://ny.shijie.icu/ || anzhiyu-icon-bilibili

  关于我: /about/ || anzhiyu-icon-paper-plane

# nav相关配置
nav:
  enable: false
  travelling: false
  clock: false
  menu:
    - title: 项目
      item:
        - name: 主页
          link: https://shijie.icu/
          icon: /images/themes/32.png

# mourn （哀悼日，指定日期网站简单变灰，不包括滚动条）
# 注意： 仅网站首页变灰，其他页面正常显示
mourn:
  enable: true
  days: [4-5, 5-12, 7-7, 9-18, 12-13]

# Code Blocks (代码相关)
# --------------------------------------

highlight_theme: mac #  darker / pale night / light / ocean / mac / mac light / false
highlight_copy: true # copy button
highlight_lang: true # show the code language
highlight_shrink: false # true: shrink the code blocks / false: expand the code blocks | none: expand code blocks and hide the button
highlight_height_limit: 330 # unit: px
code_word_wrap: false

# copy settings
# copyright: Add the copyright information after copied content (複制的内容后面加上版权信息)
# copy: enable 复制后弹窗提示版权信息
copy:
  enable: true
  copyright:
    enable: false
    limit_count: 50

# social settings (社交图标设置)
# formal:
#   name: link || icon
social:
  Steam: https://steamcommunity.com/profiles/76561199172600200/ || icon-steam
  # BiliBili: https://space.bilibili.com/372204786 || anzhiyu-icon-bilibili

# 作者卡片 状态
author_status:
  enable: true
  # 可以是任何图片，建议放表情包或者emoji图片，效果都很好，[表情包速查](https://emotion.xiaokang.me/)
  statusImg: "https://bu.dusays.com/2021/03/03/c50ad1cd452e8.png"
  skills:
    - 🤖️ 数码科技爱好者
    - 🔍 分享与热心帮助
    - 🏠 智能家居小能手
    # - 🔨 设计开发一条龙
    # - 🤝 专修交互与设计
    # - 🏃 脚踏实地行动派
    # - 🧱 团队小组发动机
    # - 💢 壮汉人狠话不多

# search (搜索)
# see https://blog.anheyu.com/posts/c27d.html#搜索系统
# --------------------------------------

# Algolia search
algolia_search:
  enable: false
  hits:
    per_page: 6
  tags:
    # - 前端
    # - Hexo

# Docsearch
# Apply and Option Docs: see https://docsearch.algolia.com/
# Crawler Admin Console: see https://crawler.algolia.com/
# Settings: https://www.algolia.com/
docsearch:
  enable: false
  appId: # see email
  apiKey: # see email
  indexName: # see email
  option:

# Local search
local_search:
  enable: true
  preload: true
  CDN:

# Math (数学)
# --------------------------------------
# About the per_page
# if you set it to true, it will load mathjax/katex script in each page (true 表示每一页都加载js)
# if you set it to false, it will load mathjax/katex script according to your setting (add the 'mathjax: true' in page's front-matter)
# (false 需要时加载，须在使用的 Markdown Front-matter 加上 mathjax: true)

# MathJax
mathjax:
  enable: false
  per_page: false

# KaTeX
katex:
  enable: false
  per_page: false
  hide_scrollbar: true

# Image (图片设置)
# --------------------------------------

# Favicon（网站图标）
favicon: /images/themes/32.png

# Avatar (头像)
avatar:
  img: /images/themes/144.png
  effect: false

# Disable all banner image
disable_top_img: false

# The banner image of home page
# index_img: false # "background: url() top / cover no-repeat"
index_img: "background: url(/images/themes/index_img.jpg) top / cover no-repeat"

# If the banner of page not setting, it will show the top_img
default_top_img: false

cover:
  # display the cover or not (是否显示文章封面)
  index_enable: true
  aside_enable: true
  archives_enable: true
  # the position of cover in home page (封面显示的位置)
  # left/right/both
  position: left
  # When cover is not set, the default cover is displayed (当没有设置cover时，默认的封面显示)
  default_cover:
    - /img/default_cover.jpg

# Replace Broken Images (替换无法显示的图片)
error_img:
  flink: /img/friend_404.gif
  post_page: /img/404.jpg

# A simple 404 page
error_404:
  enable: true
  subtitle: "请尝试站内搜索寻找文章"
  background: https://bu.dusays.com/2023/05/08/645907596997d.gif

post_meta:
  page: # Home Page
    date_type: created # created or updated or both 主页文章日期是创建日或者更新日或都显示
    date_format: simple # date/relative/simple 显示日期还是相对日期 或者 简单日期
    categories: true # true or false 主页是否显示分类
    tags: true # true or false 主页是否显示标籤
    label: false # true or false 显示描述性文字
  post:
    date_type: both # created or updated or both 文章页日期是创建日或者更新日或都显示
    date_format: date # date/relative 显示日期还是相对日期
    categories: true # true or false 文章页是否显示分类
    tags: true # true or false 文章页是否显示标籤
    label: true # true or false 显示描述性文字
    unread: false # true or false 文章未读功能

# 主色调相关配置
mainTone:
  enable: false # true or false 文章是否启用获取图片主色调
  mode: api # cdn/api/both cdn模式为图片url+imageAve参数获取主色调，api模式为请求API获取主色调，both模式会先请求cdn参数，无法获取的情况下将请求API获取，可以在文章内配置main_color: '#3e5658'，使用十六进制颜色，则不会请求both/cdn/api获取主色调，而是直接使用配置的颜色
  # 项目地址：https://github.com/anzhiyu-c/img2color-go
  api: https://img2color-go.vercel.app/api?img= # mode为api时可填写
  cover_change: true # 整篇文章跟随cover修改主色调

# wordcount (字数统计)
# see https://blog.anheyu.com/posts/c27d.html#字数统计
wordcount:
  enable: true
  post_wordcount: true
  min2read: true
  total_wordcount: true

# Display the article introduction on homepage
# 1: description
# 2: both (if the description exists, it will show description, or show the auto_excerpt)
# 3: auto_excerpt (default)
# false: do not show the article introduction
index_post_content:
  method: 3
  length: 500 # if you set method to 2 or 3, the length need to config

# anchor
# when you scroll in post, the URL will update according to header id.
anchor: false

# Post
# --------------------------------------

# toc (目录)
toc:
  post: true
  page: false
  number: true
  expand: false
  style_simple: false # for post

post_copyright:
  enable: true
  decode: false
  author_href:
  location: 苏州
  license: CC BY-NC-SA 4.0
  license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/
  avatarSinks: false # hover时头像下沉
  copyright_author_img_back:
  copyright_author_img_front:
  copyright_author_link: /

# Sponsor/reward
reward:
  enable: true
  QR_code:
    - img: /images/themes/reward/wechat.jpg
      link:
      text: 微信
    - img: /images/themes/reward/alipay.jpg
      link:
      text: 支付宝

# Post edit
# Easily browse and edit blog source code online.
post_edit: # 目前仅可选择一个平台在线编辑
  enable: false
  # github: https://github.com/user-name/repo-name/edit/branch-name/subdirectory-name/
  # For example: https://github.com/jerryc127/butterfly.js.org/edit/main/source/
  github: false

  # yuque: https://www.yuque.com/user-name/repo-name/
  # 示例: https://www.yuque.com/yuque/yuque/
  # 你需要在语雀文章 Front Matter 添加参数 id 并确保其唯一性（例如 “id: yuque”, “id: 01”）
  yuque: false

# Related Articles
related_post:
  enable: true
  limit: 6 # Number of posts displayed
  date_type: created # or created or updated 文章日期显示创建日或者更新日

# figcaption (图片描述文字)
photofigcaption: false

# post_pagination (分页)
# value: 1 || 2 || 3 || 4 || false
# 1: The 'next post' will link to old post
# 2: The 'next post' will link to new post
# 3: 只有下一篇，并且只在文章滚动到评论区时显示下一篇文章(旧文章)
# 4: 只有下一篇，并且只在文章滚动到评论区时显示下一篇文章(旧文章) 显示图片cover
# false: disable pagination
post_pagination: 2

# Displays outdated notice for a post (文章过期提醒)
noticeOutdate:
  enable: false
  style: flat # style: simple/flat
  limit_day: 365 # When will it be shown
  position: top # position: top/bottom
  message_prev: It has been
  message_next: days since the last update, the content of the article may be outdated.

# Share System (分享功能)
# --------------------------------------

# Share.js
# https://github.com/overtrue/share.js
sharejs:
  enable: true
  sites: facebook,twitter,wechat,weibo,qq

# AddToAny
# https://www.addtoany.com/
addtoany:
  enable: false
  item: facebook,twitter,wechat,sina_weibo,email,copy_link

# Comments System
# --------------------------------------

comments:
  # Up to two comments system, the first will be shown as default
  # Choose: Valine/Waline/Twikoo/Artalk
  use: Twikoo # Twikoo/Waline
  text: true # Display the comment name next to the button
  # lazyload: The comment system will be load when comment element enters the browser's viewport.
  # If you set it to true, the comment count will be invalid
  lazyload: false
  count: false # Display comment count in post's top_img
  card_post_count: false # Display comment count in Home Page

# valine
# https://valine.js.org
valine:
  appId: xxxxx # leancloud application app id
  appKey: xxxxx # leancloud application app key
  pageSize: 10 # comment list page size
  avatar: mp # gravatar style https://valine.js.org/#/avatar
  lang: zh-CN # i18n: zh-CN/zh-TW/en/ja
  placeholder: 填写QQ邮箱就会使用QQ头像喔~. # valine comment input placeholder (like: Please leave your footprints)
  guest_info: nick,mail,link # valine comment header info (nick/mail/link)
  recordIP: false # Record reviewer IP
  serverURLs: # This configuration is suitable for domestic custom domain name users, overseas version will be automatically detected (no need to manually fill in)
  bg: /img/comment_bg.png # valine background
  emojiCDN: //i0.hdslb.com/bfs/emote/ # emoji CDN
  enableQQ: true # enable the Nickname box to automatically get QQ Nickname and QQ Avatar
  requiredFields: nick,mail # required fields (nick/mail)
  visitor: false
  master:
    - xxxxx
  friends:
    - xxxxxx
  tagMeta: "博主,小伙伴,访客"
  option:

# waline - A simple comment system with backend support fork from Valine
# https://waline.js.org/
waline:
  serverURL: # Waline server address url
  bg: # Waline background
  pageview: false
  meta_css: false # 是否引入 waline-meta.css ,以便显示 meta图标
  imageUploader: true # 配置为 > 换行后可自定义图片上传逻辑，示例: https://waline.js.org/cookbook/customize/upload-image.html#案例
  # 以下为可选配置，后续若有新增/修改配置参数可在此自行添加/修改
  option:

# Twikoo
# https://github.com/imaegoo/twikoo
twikoo:
  envId: https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo
  region:
  visitor: false
  option:

# Artalk
# https://artalk.js.org/guide/frontend/config.html
artalk:
  server:
  site:
  visitor: false
  option:

# giscus
# https://giscus.app/
giscus:
  repo: # GitHub repository name
  repo_id: # GitHub repository id
  category_id: # GitHub repository category id
  theme:
    light: light
    dark: dark
  option: # options
    data-lang: zh-CN
    data-mapping:
    data-category:
    data-input-position:

# Chat Services
# --------------------------------------

# Chat Button [recommend]
# It will create a button in the bottom right corner of website, and hide the origin button
chat_btn: false

# The origin chat button is displayed when scrolling up, and the button is hidden when scrolling down
chat_hide_show: false

# chatra
# https://chatra.io/
chatra:
  enable: false
  id:

# tidio
# https://www.tidio.com/
tidio:
  enable: false
  public_key:

# daovoice
# http://daovoice.io/
daovoice:
  enable: false
  app_id:

# crisp
# https://crisp.chat/en/
crisp:
  enable: false
  website_id:

# Footer Settings
# --------------------------------------
footer:
  owner:
    enable: true
    since: 2024
  custom_text:
  runtime:
    enable: false
    launch_time: 06/03/2024 00:00:00 # 网站上线时间
    work_img: https://npm.elemecdn.com/anzhiyu-blog@2.0.4/img/badge/安知鱼-上班摸鱼中.svg
    work_description: 距离月入20k也就还差一点点哦~
    offduty_img: https://npm.elemecdn.com/anzhiyu-blog@2.0.4/img/badge/安知鱼-下班啦.svg
    offduty_description: 下班了就该开开心心的玩耍，嘿嘿~
  # 徽标部分配置项 https://shields.io/
  # https://img.shields.io/badge/CDN-jsDelivr-orange?style=flat&logo=jsDelivr
  bdageitem:
    enable: false
    list:
      - link: https://hexo.io/ #徽标指向网站链接
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg #徽标API
        message: 博客框架为Hexo_v5.4.0 #徽标提示语
      - link: https://blog.anheyu.com/
        shields: https://npm.elemecdn.com/anzhiyu-theme-static@1.0.9/img/Theme-AnZhiYu-2E67D3.svg
        message: 本站使用AnZhiYu主题
      # - link: https://www.dogecloud.com/
      #   shields: https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg
      #   message: 本站使用多吉云为静态资源提供CDN加速
      # - link: https://github.com/
      #   shields: https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg
      #   message: 本站项目由Github托管
      # - link: http://creativecommons.org/licenses/by-nc-sa/4.0/
      #   shields: https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg
      #   message: 本站采用知识共享署名-非商业性使用-相同方式共享4.0国际许可协议进行许可
  socialBar:
    enable: false
    centerImg:
    left:
      # - title: email
      #   link: mailto:<EMAIL>
      #   icon: anzhiyu-icon-envelope
      # - title: 微博
      #   link: https://weibo.com/u/**********
      #   icon: anzhiyu-icon-weibo
      # - title: facebook
      #   link: https://www.facebook.com/profile.php?id=100092208016287&sk=about
      #   icon: anzhiyu-icon-facebook1
      # - title: RSS
      #   link: atom.xml
      #   icon: anzhiyu-icon-rss
    right:
      # - title: Github
      #   link: https://github.com/anzhiyu-c
      #   icon: anzhiyu-icon-github
      # - title: Bilibili
      #   link: https://space.bilibili.com/372204786
      #   icon: anzhiyu-icon-bilibili
      # - title: 抖音
      #   link: https://v.douyin.com/DwCpMEy/
      #   icon: anzhiyu-icon-tiktok
      # - title: CC
      #   link: /copyright
      #   icon: anzhiyu-icon-copyright-line
  list:
    enable: false
    randomFriends: 3
    project:
      # - title: 服务
      #   links:
      #     - title: 51la统计
      #       link: https://v6.51.la/
      #     - title: 十年之约
      #       link: https://www.foreverblog.cn/
      #     - title: 开往
      #       link: https://github.com/travellings-link/travellings
      # - title: 主题
      #   links:
      #     - title: 文档
      #       link: /docs/
      #     - title: 源码
      #       link: https://github.com/anzhiyu-c/hexo-theme-anzhiyu
      #     - title: 更新日志
      #       link: /update/
      # - title: 导航
      #   links:
      #     - title: 即刻短文
      #       link: /essay/
      #     - title: 友链文章
      #       link: /fcircle/
      #     - title: 留言板
      #       link: /comments/
      # - title: 协议
      #   links:
      #     - title: 隐私协议
      #       link: /privacy/
      #     - title: Cookies
      #       link: /cookies/
      #     - title: 版权协议
      #       link: /copyright/
  footerBar:
    enable: true
    authorLink: /
    cc:
      enable: false
      link: /copyright
    linkList:
      # - link: https://github.com/anzhiyu-c/hexo-theme-anzhiyu
      #   text: 主题
      # - link: https://image.anheyu.com
      #   text: 图床
      - link: https://beian.miit.gov.cn/#/Integrated/index
        text: 皖ICP备20000482号-2
      - link: https://beian.mps.gov.cn/#/query/webSearch
        text: 皖公网安备 34120402000423号
    subTitle:
      enable: false
      # Typewriter Effect (打字效果)
      effect: true
      # Effect Speed Options (打字效果速度参数)
      startDelay: 200 # time before typing starts in milliseconds
      typeSpeed: 100 # type speed in milliseconds
      backSpeed: 50 # backspacing speed in milliseconds
      # loop (循环打字)
      loop: true
      # source 调用第三方服务
      # source: false 关闭调用
      # source: 1  调用一言网的一句话（简体） https://hitokoto.cn/
      # source: 2  调用一句网（简体） http://yijuzhan.com/
      # source: 3  调用今日诗词（简体） https://www.jinrishici.com/
      # subtitle 会先显示 source , 再显示 sub 的内容
      source: 1
      # 如果关闭打字效果，subtitle 只会显示 sub 的第一行文字
      sub:
        # - 生活明朗&#44; 万物可爱&#44; 人间值得&#44; 未来可期.

# Analysis
# --------------------------------------

# Baidu Analytics
# https://tongji.baidu.com/web/welcome/login
baidu_analytics:

# Google Analytics
# https://analytics.google.com/analytics/web/
google_analytics:

# CNZZ Analytics
# https://www.umeng.com/
cnzz_analytics:

# Cloudflare Analytics
# https://www.cloudflare.com/zh-tw/web-analytics/
cloudflare_analytics:

# Microsoft Clarity
# https://clarity.microsoft.com/
microsoft_clarity:

# Advertisement
# --------------------------------------

# Google Adsense (谷歌广告)
google_adsense:
  enable: false
  auto_ads: true
  js: https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js
  client:
  enable_page_level_ads: true

# Insert ads manually (手动插入广告)
# ad:
#   index:
#   aside:
#   post:

# Verification (站长验证)
# --------------------------------------

site_verification:
  - name: google-site-verification
    content: xxx
  - name: baidu-site-verification
    content: code-xxx
  - name: msvalidate.01
    content: xxx

# Beautify/Effect (美化/效果)
# --------------------------------------

# Theme color for customize
# Notice: color value must in double quotes like "#000" or may cause error!

theme_color:
  enable: true
  main: "#425AEF"
  dark_main: "#f2b94b"
  paginator: "#425AEF"
  #   button_hover: "#FF7242"
  text_selection: "#2128bd"
  link_color: "var(--anzhiyu-fontcolor)"
  meta_color: "var(--anzhiyu-fontcolor)"
  hr_color: "#4259ef23"
  code_foreground: "#fff"
  code_background: "var(--anzhiyu-code-stress)"
  toc_color: "#425AEF"
  #   blockquote_padding_color: "#425AEF"
  #   blockquote_background_color: "#425AEF"
  scrollbar_color: "var(--anzhiyu-scrollbar)"
  meta_theme_color_light: "#f7f9fe"
  meta_theme_color_dark: "#18171d"

# 移动端侧栏
sidebar:
  site_data:
    archive: true
    tag: true
    category: true
  menus_items: true
  tags_cloud: true
  display_mode: true
  nav_menu_project: true

# 文章h2添加分隔线
h2Divider: false

# 表格隔行变色
table_interlaced_discoloration: false

# 首页双栏显示
article_double_row: true

# The top_img settings of home page
# default: top img - full screen, site info - middle (默认top_img全屏，site_info在中间)
# The position of site info, eg: 300px/300em/300rem/10% (主页标题距离顶部距离)
index_site_info_top: 
# The height of top_img, eg: 300px/300em/300rem (主页top_img高度)
index_top_img_height: 

# The user interface setting of category and tag page (category和tag页的UI设置)
# index - same as Homepage UI (index 值代表 UI将与首页的UI一样)
# default - same as archives UI 默认跟archives页面UI一样
category_ui: # 留空或 index
tag_ui: # 留空或 index

# Footer Background
footer_bg: false

# the position of bottom right button/default unit: px (右下角按钮距离底部的距离/默认单位为px)
rightside-bottom: 100px

# Background effects (背景特效)
# --------------------------------------

# canvas_ribbon (静止彩带背景)
# See: https://github.com/hustcc/ribbon.js
canvas_ribbon:
  enable: false
  size: 150
  alpha: 0.6
  zIndex: -1
  click_to_change: false
  mobile: false

# Fluttering Ribbon (动态彩带)
canvas_fluttering_ribbon:
  enable: false
  mobile: false

# canvas_nest
# https://github.com/hustcc/canvas-nest.js
canvas_nest:
  enable: false
  color: "0,0,255" #color of lines, default: '0,0,0'; RGB values: (R,G,B).(note: use ',' to separate.)
  opacity: 0.7 # the opacity of line (0~1), default: 0.5.
  zIndex: -1 # z-index property of the background, default: -1.
  count: 99 # the number of lines, default: 99.
  mobile: false

# Typewriter Effect (打字效果)
# https://github.com/disjukr/activate-power-mode
activate_power_mode:
  enable: false
  colorful: true # open particle animation (冒光特效)
  shake: false #  open shake (抖动特效)
  mobile: false

# Mouse click effects: fireworks (鼠标点击效果: 烟火特效)
fireworks:
  enable: false
  zIndex: 9999 # -1 or 9999
  mobile: false

# Mouse click effects: Heart symbol (鼠标点击效果: 爱心)
click_heart:
  enable: false
  mobile: false

# Mouse click effects: words (鼠标点击效果: 文字)
ClickShowText:
  enable: false
  text:
    # - I
    # - LOVE
    # - YOU
  fontSize: 15px
  random: false
  mobile: false

# Default display mode (网站默认的显示模式)
# light (default) / dark
display_mode: light

# Beautify (美化页面显示)
beautify:
  enable: true
  field: post # site/post
  title-prefix-icon: '\f0c1'
  title-prefix-icon-color: "#F47466"

# Global font settings
# Don't modify the following settings unless you know how they work (非必要不要修改)
font:
  global-font-size: 16px
  code-font-size:
  font-family:
  code-font-family: consolas, Menlo, "PingFang SC", "Microsoft JhengHei", "Microsoft YaHei", sans-serif

# Font settings for the site title and site subtitle
# 左上角网站名字 主页居中网站名字
blog_title_font:
  font_link: 
  font-family: PingFang SC, 'Hiragino Sans GB', 'Microsoft JhengHei', 'Microsoft YaHei', sans-serif

# The setting of divider icon (水平分隔线图标设置)
hr_icon:
  enable: true
  icon: \f0c4 # the unicode value of Font Awesome icon, such as '\f0c4'
  icon-top:

# the subtitle on homepage (主页subtitle)
subtitle:
  enable: true
  # Typewriter Effect (打字效果)
  effect: true
  # Effect Speed Options (打字效果速度参数)
  startDelay: 300 # time before typing starts in milliseconds
  typeSpeed: 150 # type speed in milliseconds
  backSpeed: 50 # backspacing speed in milliseconds
  # loop (循环打字)
  loop: true
  # source 调用第三方服务
  # source: false 关闭调用
  # source: 1  调用一言网的一句话（简体） https://hitokoto.cn/
  # source: 2  调用一句网（简体） http://yijuzhan.com/
  # source: 3  调用今日诗词（简体） https://www.jinrishici.com/
  # subtitle 会先显示 source , 再显示 sub 的内容
  source: false
  # 如果关闭打字效果，subtitle 只会显示 sub 的第一行文字
  sub:
    - 记录生活 &#124; 感受世界

# Loading Animation (加载动画)
preloader:
  enable: true
  # source
  # 1. fullpage-loading
  # 2. pace (progress bar)
  # else all
  source: 3
  # pace theme (see https://codebyzach.github.io/pace/)
  pace_css_url:
  avatar: /images/themes/32.png # 自定加载动画义头像

# aside (侧边栏)
# --------------------------------------

aside:
  enable: true
  hide: false
  button: true
  mobile: true # display on mobile
  position: right # left or right
  display: # 控制对应详情页面是否显示侧边栏
    archive: true
    tag: true
    category: true
  card_author:
    enable: true
    description: <div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">欢迎访问我的博客，这里是我分享<b style="color:#fff">生活</b>的地方，另外还有一些技能和经验的<b style="color:#fff">记录</b>。</div><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">希望可以帮助到你。</div> # 默认为站点描述
    name_link: /
  card_announcement:
    enable: true
    content: 
      刚从Halo迁移至Hexo，还在调整中，欢迎访问~
      <br/>
      本站开放时间为：<b>8:30-23:00</b>，可能会提前关站哦~
      <br/>
      历史更新请点击<a href="https://shijie.icu/update/"><b>博客更新日志</b></a>
      <br/>
      
  card_weixin:
    enable: false
    face: https://bu.dusays.com/2023/01/13/63c02edf44033.png
    backFace: https://bu.dusays.com/2023/05/13/645fa415e8694.png
  card_recent_post:
    enable: true
    limit: 5 # if set 0 will show all
    sort: date # date or updated
    sort_order: # Don't modify the setting unless you know how it works
  card_categories:
    enable: true
    limit: 8 # if set 0 will show all
    expand: none # none/true/false
    sort_order: # Don't modify the setting unless you know how it works
  card_tags:
    enable: true
    limit: 40 # if set 0 will show all
    color: false
    sort_order: # Don't modify the setting unless you know how it works
    highlightTags:
      # - Hexo
      # - 前端
  card_archives:
    enable: true
    type: monthly # yearly or monthly
    format: MMMM YYYY # eg: YYYY年MM月
    order: -1 # Sort of order. 1, asc for ascending; -1, desc for descending
    limit: 8 # if set 0 will show all
    sort_order: # Don't modify the setting unless you know how it works
  card_webinfo:
    enable: true
    post_count: true
    last_push_date: false
    sort_order: # Don't modify the setting unless you know how it works

# busuanzi count for PV / UV in site
# 访问人数
busuanzi:
  site_uv: true
  site_pv: true
  page_pv: true

# Time difference between publish date and now (网页运行时间)
# Formal: Month/Day/Year Time or Year/Month/Day Time
runtimeshow:
  enable: true
  publish_date: 4/1/2021 00:00:00

# Console - Newest Comments
newest_comments:
  enable: true
  sort_order: # Don't modify the setting unless you know how it works
  limit: 6
  storage: 10 # unit: mins, save data to localStorage
  avatar: true

# Bottom right button (右下角按钮)
# --------------------------------------

# Conversion between Traditional and Simplified Chinese (简繁转换)
translate:
  enable: true
  # The text of a button
  default: 繁
  # Right-click menu default text
  rightMenuMsgDefault: "轉為繁體"
  # the language of website (1 - Traditional Chinese/ 2 - Simplified Chinese）
  defaultEncoding: 2
  # Time delay
  translateDelay: 0
  # The text of the button when the language is Simplified Chinese
  msgToTraditionalChinese: "繁"
  # The text of the button when the language is Traditional Chinese
  msgToSimplifiedChinese: "简"
  # Right-click the menu to traditional Chinese
  rightMenuMsgToTraditionalChinese: "转为繁体"
  # Right-click menu to simplified Chinese
  rightMenuMsgToSimplifiedChinese: "转为简体"

# Read Mode (閲读模式)
readmode: true

# 中控台
centerConsole:
  enable: true
  card_tags:
    enable: true
    limit: 40 # if set 0 will show all
    color: false
    sort_order: # Don't modify the setting unless you know how it works
    highlightTags:
      # - Hexo
      # - 前端
  card_archives:
    enable: true
    type: monthly # yearly or monthly
    format: MMMM YYYY # eg: YYYY年MM月
    order: -1 # Sort of order. 1, asc for ascending; -1, desc for descending
    limit: 8 # if set 0 will show all
    sort_order: # Don't modify the setting unless you know how it works

# dark mode
darkmode:
  enable: true
  # Toggle Button to switch dark/light mode
  button: true
  # Switch dark/light mode automatically (自动切换 dark mode和 light mode)
  # autoChangeMode: 1  Following System Settings, if the system doesn't support dark mode, it will switch dark mode between 6 pm to 6 am
  # autoChangeMode: 2  Switch dark mode between 6 pm to 6 am
  # autoChangeMode: false
  autoChangeMode: 1
  start: # 8
  end: # 22

# Don't modify the following settings unless you know how they work (非必要请不要修改 )
# Choose: readmode,translate,darkmode,hideAside,toc,chat,comment
# Don't repeat 不要重複
rightside_item_order:
  enable: false
  hide: # readmode,translate,darkmode,hideAside
  show: # toc,chat,comment

# Lightbox (图片大图查看模式)
# --------------------------------------
# You can only choose one, or neither (只能选择一个 或者 两个都不选)

# medium-zoom
# https://github.com/francoischalifour/medium-zoom
medium_zoom: false

# fancybox
# http://fancyapps.com/fancybox/3/
fancybox: true

# Tag Plugins settings (标籤外挂)
# --------------------------------------

# mermaid
# see https://github.com/mermaid-js/mermaid
mermaid:
  enable: false
  # built-in themes: default/forest/dark/neutral
  theme:
    light: default
    dark: dark

# Note (Bootstrap Callout)
note:
  # Note tag style values:
  #  - simple    bs-callout old alert style. Default.
  #  - modern    bs-callout new (v2-v3) alert style.
  #  - flat      flat callout style with background, like on Mozilla or StackOverflow.
  #  - disabled  disable all CSS styles import of note tag.
  style: flat
  icons: true
  border_radius: 3
  # Offset lighter of background in % for modern and flat styles (modern: -12 | 12; flat: -18 | 6).
  # Offset also applied to label tag variables. This option can work with disabled note tag.
  light_bg_offset: 0

icons:
  ali_iconfont_js: https://at.alicdn.com/t/c/font_4729035_3m5int5al8c.js # 阿里图标symbol 引用链接，主题会进行加载 symbol 引用
  fontawesome: true #是否启用fontawesome6图标
  fontawesome_animation_css: #fontawesome_animation 如果有就会加载，示例值：https://npm.elemecdn.com/hexo-butterfly-tag-plugins-plus@1.0.17/lib/assets/font-awesome-animation.min.css

# other
# --------------------------------------

# Pjax
# It may contain bugs and unstable, give feedback when you find the bugs.
# https://github.com/MoOx/pjax
pjax:
  enable: true
  exclude:
    # - xxxx
    - /musics/

# Inject the css and script (aplayer/meting)
aplayerInject:
  enable: true
  per_page: false

# Snackbar (Toast Notification 弹窗)
# https://github.com/polonel/SnackBar
# position 弹窗位置
# 可选 top-left / top-center / top-right / bottom-left / bottom-center / bottom-right
snackbar:
  enable: true
  position: top-center
  bg_light: "#425AEF" # The background color of Toast Notification in light mode
  bg_dark: "#1f1f1f" # The background color of Toast Notification in dark mode

# https://instant.page/
# prefetch (预加载)
instantpage: true

# https://github.com/vinta/pangu.js
# Insert a space between Chinese character and English character (中英文之间添加空格)
pangu:
  enable: false
  field: site # site/post

# Lazyload (图片懒加载)
# https://github.com/verlok/vanilla-lazyload
lazyload:
  enable: true
  field: site # site/post
  placeholder:
  blur: true
  progressive: true

# PWA
# See https://github.com/JLHwung/hexo-offline
# ---------------
pwa:
  enable: false
  startup_image_enable: true
  manifest: /manifest.json
  theme_color: var(--anzhiyu-main)
  mask_icon: /img/siteicon/apple-icon-180.png
  apple_touch_icon: /img/siteicon/apple-icon-180.png
  bookmark_icon: /img/siteicon/apple-icon-180.png
  favicon_32_32: /img/siteicon/32.png
  favicon_16_16: /img/siteicon/16.png

# Open graph meta tags
# https://developers.facebook.com/docs/sharing/webmasters/
Open_Graph_meta: true

# Add the vendor prefixes to ensure compatibility
css_prefix: true

# 首页顶部相关配置
home_top:
  enable: false # 开关
  timemode: date #date/updated
  title: 生活明朗
  subTitle: 万物可爱。
  siteText: 技能树
  category:
    - name: 装备
      path: /equipment/
      shadow: var(--anzhiyu-shadow-blue)
      class: blue
      icon: anzhiyu-icon-dove
    - name: 相册
      path: /album/
      shadow: var(--anzhiyu-shadow-red)
      class: red
      icon: anzhiyu-icon-fire
    - name: 瞬间
      path: /fcircle/
      shadow: var(--anzhiyu-shadow-green)
      class: green
      icon: anzhiyu-icon-book
  default_descr: 再怎么看我也不知道怎么描述它的啦！
  swiper:
    enable: false
    swiper_css: https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css #swiper css依赖
    swiper_js: https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.js #swiper js依赖
  banner:
    tips: 新品主题
    title: Theme-AnZhiYu
    image: https://bu.dusays.com/2023/05/13/645fa3cf90d70.webp
    link: https://docs.anheyu.com/

# 朋友圈配置
friends_vue:
  enable: false
  vue_js: https://npm.elemecdn.com/anzhiyu-theme-static@1.1.1/friends/index.4f887d95.js
  apiurl: # 朋友圈后端地址
  top_tips: 使用 友链朋友圈 订阅友链最新文章
  top_background:

# 深色模式粒子效果canvas
universe:
  enable: true

# 页面卡片顶部气泡升起效果
bubble:
  enable: false

#  控制台打印信息
console:
  enable: false

# 51a统计配置
LA:
  enable: false
  ck: 3KDbRzWlIWGmBB4o
  LingQueMonitorID: 3KDbRzWlIWGmBB4o

# 标签卖萌
diytitle:
  enable: false
  leaveTitle: w(ﾟДﾟ)w 不要走！再看看嘛！
  backTitle: ♪(^∇^*)欢迎肥来！

# 留言弹幕配置
comment_barrage_config:
  enable: false
  # 同时最多显示弹幕数
  maxBarrage: 1
  # 弹幕显示间隔时间ms
  barrageTime: 4000
  # token，在控制台中获取
  accessToken: "125e054a5ee636806e90760f84be6fcd"
  # 博主邮箱md5值
  mailMd5: ""

# 左下角音乐配置项
# https://github.com/metowolf/MetingJS
nav_music:
  enable: false
  console_widescreen_music: false # 宽屏状态控制台显示音乐而不是标签 enable为true 控制台依然会显示
  id: 8152976493
  server: netease
  volume: 0.7 # 默认音量
  all_playlist: https://y.qq.com/n/ryqq/playlist/8802438608

# 路径为 /music 的音乐页面默认加载的歌单 1. nav_music 2. custom
music_page_default: nav_music

# 评论匿名邮箱
visitorMail:
  enable: true
  mail: ""

# ptool 文章底部工具
ptool:
  enable: true
  share_mobile: true
  share_weibo: true
  share_copyurl: true
  categories: false # 是否显示分类
  mode: # 运营模式与责任，不配置不显示

# 欢迎语配置
greetingBox:
  enable: false #开启后必须配置下面的list对应的时间段，不然会出现小白条
  default: 晚上好👋
  list:
    # - greeting: 晚安😴
    #   startTime: 0
    #   endTime: 5
    # - greeting: 早上好鸭👋, 祝你一天好心情！
    #   startTime: 6
    #   endTime: 9
    # - greeting: 上午好👋, 状态很好，鼓励一下～
    #   startTime: 10
    #   endTime: 10
    # - greeting: 11点多啦, 在坚持一下就吃饭啦～
    #   startTime: 11
    #   endTime: 11
    # - greeting: 午安👋, 宝贝
    #   startTime: 12
    #   endTime: 14
    # - greeting: 🌈充实的一天辛苦啦！
    #   startTime: 14
    #   endTime: 18
    # - greeting: 19点喽, 奖励一顿丰盛的大餐吧🍔。
    #   startTime: 19
    #   endTime: 19
    # - greeting: 晚上好👋, 在属于自己的时间好好放松😌~
    #   startTime: 20
    #   endTime: 24

# 文章顶部ai摘要
post_head_ai_description:
  enable: false
  gptName: AnZhiYu
  mode: local # 默认模式 可选值: tianli/local
  switchBtn: false # 可以配置是否显示切换按钮 以切换tianli/local
  btnLink: https://afdian.net/item/886a79d4db6711eda42a52540025c377
  randomNum: 3 # 按钮最大的随机次数，也就是一篇文章最大随机出来几种
  basicWordCount: 1000 # 最低获取字符数, 最小1000, 最大1999
  key: xxxx
  Referer: https://xx.xx/

# 快捷键配置
shortcutKey:
  enable: false
  delay: 100 # 所有键位延时触发而不是立即触发（包括shift，以解决和浏览器键位冲突问题）
  shiftDelay: 200 # shift按下延时多久开启

# 无障碍优化（在首页按下「shift + ?」以查看效果）
accesskey:
  enable: true

# 友情链接顶部相关配置
linkPageTop:
  enable: false
  title: 与数百名博主无限进步
  # 添加博主友链的评论自定义格式
  addFriendPlaceholder: "昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"

# 缩略图后缀 archive/tag/category 页面单独开启后缀
pageThumbnailSuffix: ""

# 隐私协议弹窗
agreementPopup:
  enable: false
  url: /privacy

# 右键菜单
rightClickMenu:
  enable: true

# 首页随便逛逛people模式 而非技能点模式，关闭后为技能点模式需要配置creativity.yml
peoplecanvas:
  enable: false
  img: https://upload-bbs.miyoushe.com/upload/2024/07/27/125766904/ba62475f396df9de3316a08ed9e65d86_5680958632268053399..png

# 动效
dynamicEffect:
  postTopWave: true # 文章顶部波浪效果
  postTopRollZoomInfo: false # 文章顶部滚动时缩放
  pageCommentsRollZoom: false # 非文章页面评论滚动时缩放显示（仅仅Twikoo生效）

# Inject
# Insert the code to head (before '</head>' tag) and the bottom (before '</body>' tag)
# 插入代码到头部 </head> 之前 和 底部 </body> 之前
inject:
  head:
    # 自定义css
    # - <link rel="stylesheet" href="/css/custom.css" media="defer" onload="this.media='all'">
    - <link rel="stylesheet" href="/css/costom_widget.css">
    - <link rel="stylesheet" href="/css/badge.css">
    - <link rel="stylesheet" href="/css/footer-animal.css">
  bottom:
    # 自定义js
    # - <script src="/js/xxx"></script>
    - <script src="/js/countdown.js"></script>
    - <script src="/js/getnowplaying.js"></script>
    - <script src="/js/badge.js"></script>
    - <script src="/js/footer-animal.js"></script>
# CDN
# Don't modify the following settings unless you know how they work
# 非必要请不要修改
CDN:
  # The CDN provider of internal scripts (主题内部 js 的 cdn 配置)
  # option: local/elemecdn/jsdelivr/unpkg/cdnjs/onmicrosoft/cbd/anheyu/custom
  # Dev version can only choose. ( dev版的主题只能设置为 local )
  internal_provider: local

  # The CDN provider of third party scripts (第三方 js 的 cdn 配置)
  # option: elemecdn/jsdelivr/unpkg/cdnjs/onmicrosoft/cbd/anheyu/custom
  third_party_provider: cbd

  # Add version number to CDN, true or false
  version: true

  # Custom format
  # For example: https://cdn.staticfile.org/${cdnjs_name}/${version}/${min_cdnjs_file}
  custom_format: # https://npm.elemecdn.com/${name}@latest/${file}

  option:
    # main_css:
    # main:
    # utils:
    # translate:
    # random_friends_post_js:
    # right_click_menu_js:
    # comment_barrage_js:
    # ai_abstract_js:
    # people_js:
    # local_search:
    # algolia_js:
    # algolia_search:
    # instantsearch:
    # docsearch_js:
    # docsearch_css:
    # pjax:
    # blueimp_md5:
    # valine:
    # twikoo:
    # waline_js:
    # waline_css:
    # sharejs:
    # sharejs_css:
    # mathjax:
    # katex:
    # katex_copytex:
    # mermaid:
    # canvas_ribbon:
    # canvas_fluttering_ribbon:
    # canvas_nest:
    # lazyload:
    # instantpage:
    # typed:
    # pangu:
    # fancybox_css:
    # fancybox:
    # medium_zoom:
    # snackbar_css:
    # snackbar:
    # activate_power_mode:
    # fireworks:
    # click_heart:
    # ClickShowText:
    # fontawesome:
    # flickr_justified_gallery_js:
    # flickr_justified_gallery_css:
    # aplayer_css:
    # aplayer_js:
    # meting_js:
    # meting_api:
    # prismjs_js:
    # prismjs_lineNumber_js:
    # prismjs_autoloader:
    # artalk_js:
    # artalk_css:
    # pace_js:
    # pace_default_css:
    # countup_js:
    # gsap_js:
    # busuanzi:
    # rightmenu:
    # waterfall:
    # ali_iconfont_css:
    # accesskey_js:

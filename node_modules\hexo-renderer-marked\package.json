{"name": "hexo-renderer-marked", "version": "6.3.0", "description": "Markdown renderer plugin for Hexo", "main": "index", "scripts": {"eslint": "eslint .", "test": "mocha test/index.js", "test-cov": "c8 --reporter=lcovonly npm run test"}, "directories": {"lib": "./lib"}, "files": ["lib", "index.js"], "repository": {"type": "git", "url": "git+https://github.com/hexojs/hexo-renderer-marked.git"}, "keywords": ["hexo", "markdown", "marked", "renderer"], "author": "<PERSON> <<EMAIL>> (https://zespia.tw)", "maintainers": ["<PERSON><PERSON> <<EMAIL>> (https://abnerchou.me)"], "license": "MIT", "dependencies": {"dompurify": "^3.0.3", "hexo-util": "^3.1.0", "jsdom": "^20.0.1", "marked": "^4.3.0"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "eslint": "^8.57.0", "eslint-config-hexo": "^5.0.0", "hexo": "^7.1.1", "mocha": "^10.4.0"}, "engines": {"node": ">=14"}}
{"name": "hexo-renderer-pug", "version": "3.0.0", "description": "Pug renderer plugin for Hexo", "main": "index.js", "scripts": {"eslint": "eslint .", "test": "mocha test/index.js", "test-cov": "nyc npm run test"}, "directories": {"lib": "./lib"}, "files": ["lib/", "index.js"], "repository": "hexojs/hexo-renderer-pug", "keywords": ["hexo", "pug", "renderer"], "author": "<PERSON> <<EMAIL>> (http://zespia.tw)", "maintainers": ["<PERSON><PERSON> <<EMAIL>> (http://abnerchou.me)", "<PERSON> <<EMAIL>>"], "license": "MIT", "dependencies": {"pug": "^3.0.2"}, "devDependencies": {"chai": "^4.3.4", "eslint": "^8.4.0", "eslint-config-hexo": "^5.0.0", "mocha": "^9.2.0", "nyc": "^15.1.0"}, "engines": {"node": ">=12.4.0"}}
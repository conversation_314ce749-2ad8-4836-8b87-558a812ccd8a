<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"><title>影视世界 | 我的博客</title><meta name="author" content="c2hapmll"><meta name="copyright" content="c2hapmll"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f7f9fe"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="影视世界"><meta name="application-name" content="影视世界"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f7f9fe"><meta property="og:type" content="website"><meta property="og:title" content="影视世界"><meta property="og:url" content="https://shijie.icu/movies/index.html"><meta property="og:site_name" content="我的博客"><meta property="og:description" content="影视世界"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://shijie.icu/img/default_cover.jpg"><meta property="article:author" content="c2hapmll"><meta property="article:tag"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://shijie.icu/img/default_cover.jpg"><meta name="description" content="影视世界"><link rel="shortcut icon" href="/images/themes/32.png"><link rel="canonical" href="https://shijie.icu/movies/"><link rel="preconnect" href="//cdn.cbd.int"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><meta name="google-site-verification" content="xxx"/><meta name="baidu-site-verification" content="code-xxx"/><meta name="msvalidate.01" content="xxx"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.cbd.int/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media='all'"><script>const GLOBAL_CONFIG = {
  linkPageTop: undefined,
  peoplecanvas: undefined,
  postHeadAiDescription: undefined,
  diytitle: undefined,
  LA51: undefined,
  greetingBox: undefined,
  twikooEnvId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
  commentBarrageConfig:undefined,
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: false,
  mainTone: undefined,
  authorStatus: {"skills":["🤖️ 数码科技爱好者","🔍 分享与热心帮助","🏠 智能家居小能手"]},
  algolia: undefined,
  localSearch: {"path":"/search.xml","preload":true,"languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: undefined,
  highlight: {"plugin":"prismjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    simplehomepage: true,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: c2hapmll","link":"链接: ","source":"来源: 我的博客","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: false,
  shortcutKey: undefined,
  autoDarkmode: true
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  configTitle: '我的博客',
  title: '影视世界',
  postAI: '',
  pageFillDescription: '影视世界',
  isPost: false,
  isHome: false,
  isHighlightShrink: false,
  isToc: false,
  postUpdate: '2024-11-08 17:53:04',
  postMainColor: '',
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#18171d')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f7f9fe')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/costom_widget.css"><link rel="stylesheet" href="/css/badge.css"><meta name="generator" content="Hexo 7.3.0"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="/images/themes/32.png"/><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"/><script async="async" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="page" id="body-wrap"><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><a id="site-name" href="/" accesskey="h"><div class="title">我的博客</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"/><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole();"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" alt="微信" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/wechat.jpg"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" alt="支付宝" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/alipay.jpg"/></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title"> 最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 1.05rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 1.05rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 1.05rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 1.05rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 1.05rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 1.05rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 1.05rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 1.05rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 1.05rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/02/"><span class="card-archive-list-date">二月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/12/"><span class="card-archive-list-date">十二月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/11/"><span class="card-archive-list-date">十一月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/10/"><span class="card-archive-list-date">十月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/09/"><span class="card-archive-list-date">九月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li></ul></div><hr/></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout hide-aside" id="content-inner"><div id="page"><h1 class="page-title">影视世界</h1><div id="movies"><div class="author-content author-content-item moviesPage single" style="background: url(/images/movies/top_movies.jpg) left 37% / cover no-repeat !important;"><div class="card-content"><div class="author-content-item-tips">电影世界</div><span class="author-content-item-title">享受视听盛宴</span><div class="content-bottom"><div class="tips">我跟自己下了赌注，如果我在这座花园的时候，那个人能够来找我，我这一辈子就会跟着他。  -- 《红猪》</div></div><div class="banner-button-group"><a class="banner-button" target="_blank" rel="noopener" href="https://www.themoviedb.org/"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right" style="font-size: 1.3rem"></i><span class="banner-button-text">TMDB</span></a></div></div></div><div class="goodmovies-item"><h2 class="goodmovies-title">想看/在看 的动画、剧集、电影</h2><div class="goodmovies-item-description"></div><div class="movies-item"><div class="movies-item-content"><div class="movies-item-content-item" data-index="0"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/60625-rick-and-morty" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/2025/202504010301.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="瑞克和莫蒂"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;瑞克和莫蒂&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【瑞克和莫蒂】&quot;);" title="瑞克和莫蒂">瑞克和莫蒂</div><div class="movies-item-content-item-specification"><span class="year">2013 / </span><span class="genre">冒险 / </span></div><div class="movies-item-content-item-description">很难想象作者的精神状态，似乎美漫都是这样，包括《辛普森一家》</div></div></div></div><!-- 新增分页按钮 box--><div class="movies-pagination-box"><div class="pagination-nav"><button class="prev-button">Prev</button><div class="page-numbers"></div><button class="next-button">Next</button></div></div></div></div><div class="goodmovies-item"><h2 class="goodmovies-title">看过的电影</h2><div class="goodmovies-item-description"></div><div class="movies-item"><div class="movies-item-content"><div class="movies-item-content-item" data-index="0"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/447743" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/202504010201.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="犬屋敷 真人版"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;犬屋敷 真人版&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【犬屋敷 真人版】&quot;);" title="犬屋敷 真人版">犬屋敷 真人版</div><div class="movies-item-content-item-specification"><span class="year">2018 / </span><span class="genre">科幻 / </span><span class="rating">⭐⭐⭐⭐</span><span class="watch-date"> / 2025.3.31</span></div><div class="movies-item-content-item-description">很早之前就看过了，既然想起来就记一下，反派塑造的有点差。</div></div></div><div class="movies-item-content-item" data-index="1"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/76726-chronicle" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/202504010101.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="超能失控"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;超能失控&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【超能失控】&quot;);" title="超能失控">超能失控</div><div class="movies-item-content-item-specification"><span class="year">2012 / </span><span class="genre">科幻 / </span><span class="rating">⭐⭐⭐⭐</span><span class="watch-date"> / 2025.3.31</span></div><div class="movies-item-content-item-description">非常好看的念力超能力电影，伪纪录片，可惜还是不够强，肉身仍是正常水平。</div></div></div><div class="movies-item-content-item" data-index="2"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/634429" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320051.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="新·奥特曼"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;新·奥特曼&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【新·奥特曼】&quot;);" title="新·奥特曼">新·奥特曼</div><div class="movies-item-content-item-specification"><span class="year">2022 / </span><span class="genre">科幻 / </span><span class="rating">⭐⭐⭐⭐</span><span class="watch-date"> / 2025.3.4</span></div><div class="movies-item-content-item-description">观感很不错，特效很棒，剧情发展的很快，如果不认真看可能会一头雾水。</div></div></div><div class="movies-item-content-item" data-index="3"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/508883" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320050.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="你想活出怎样的人生"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;你想活出怎样的人生&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【你想活出怎样的人生】&quot;);" title="你想活出怎样的人生">你想活出怎样的人生</div><div class="movies-item-content-item-specification"><span class="year">2023 / </span><span class="genre">动画 / </span><span class="rating">⭐⭐⭐⭐</span><span class="watch-date"> / 2025.1.4</span></div><div class="movies-item-content-item-description">感觉好像没有自己的脉络，太多隐喻的地方就会冲淡本身的故事性，画风还是那个画风。</div></div></div><div class="movies-item-content-item" data-index="4"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/991197" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320049.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="临时劫案"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;临时劫案&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【临时劫案】&quot;);" title="临时劫案">临时劫案</div><div class="movies-item-content-item-specification"><span class="year">2024 / </span><span class="genre">犯罪 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">tmdb的分类又有点问题了，明显是喜剧片，郭富城主演的龅牙口音也太搞笑了</div></div></div><div class="movies-item-content-item" data-index="5"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/1306144" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320048.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="喜羊羊与灰太狼之守护"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;喜羊羊与灰太狼之守护&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【喜羊羊与灰太狼之守护】&quot;);" title="喜羊羊与灰太狼之守护">喜羊羊与灰太狼之守护</div><div class="movies-item-content-item-specification"><span class="year">2024 / </span><span class="genre">动画 / </span><span class="rating">⭐⭐⭐</span></div><div class="movies-item-content-item-description">去影院看的时候都是小孩子，制作的也不错，就是偶尔莫名出现的粗糙3D动画有点难受</div></div></div><div class="movies-item-content-item" data-index="6"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/502356" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320047.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="超级马力欧兄弟大电影"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;超级马力欧兄弟大电影&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【超级马力欧兄弟大电影】&quot;);" title="超级马力欧兄弟大电影">超级马力欧兄弟大电影</div><div class="movies-item-content-item-specification"><span class="year">2023 / </span><span class="genre">动画 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">儿童向，成年人也不是不行，影院观看效果很好</div></div></div><div class="movies-item-content-item" data-index="7"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/493529" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320046.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="龙与地下城：侠盗荣耀"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;龙与地下城：侠盗荣耀&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【龙与地下城：侠盗荣耀】&quot;);" title="龙与地下城：侠盗荣耀">龙与地下城：侠盗荣耀</div><div class="movies-item-content-item-specification"><span class="year">2022 / </span><span class="genre">奇幻 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">DND系列，搞笑风格，可惜西幻的想象力还是不足，法术没什么创新</div></div></div><div class="movies-item-content-item" data-index="8"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/508935" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320045.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="八佰"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;八佰&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【八佰】&quot;);" title="八佰">八佰</div><div class="movies-item-content-item-specification"><span class="year">2020 / </span><span class="genre">战争 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">四行仓库保卫战，现实中四行仓库遗址修缮后改为纪念馆，有空可以去看看</div></div></div><div class="movies-item-content-item" data-index="9"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/620249" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320044.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="罗小黑战记"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;罗小黑战记&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【罗小黑战记】&quot;);" title="罗小黑战记">罗小黑战记</div><div class="movies-item-content-item-specification"><span class="year">2019 / </span><span class="genre">动画 冒险 奇幻 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">制作精良，保持了和动画版一致的水平，国产动画再来点</div></div></div><div class="movies-item-content-item" data-index="10"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/399579" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320043.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="阿丽塔：战斗天使"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;阿丽塔：战斗天使&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【阿丽塔：战斗天使】&quot;);" title="阿丽塔：战斗天使">阿丽塔：战斗天使</div><div class="movies-item-content-item-specification"><span class="year">2019 / </span><span class="genre">动作 冒险 科幻 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">令人惊艳的观影体验，血肉苦痛、机械飞升</div></div></div><div class="movies-item-content-item" data-index="11"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/531384" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320042.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="无双"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;无双&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【无双】&quot;);" title="无双">无双</div><div class="movies-item-content-item-specification"><span class="year">2018 / </span><span class="genre">犯罪 剧情 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">知名梗&quot;我来搞定变色油墨&quot;的出处</div></div></div><div class="movies-item-content-item" data-index="12"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/503235" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320041.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="邪不压正"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;邪不压正&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【邪不压正】&quot;);" title="邪不压正">邪不压正</div><div class="movies-item-content-item-specification"><span class="year">2018 / </span><span class="genre">动作 犯罪 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">个人感觉像是喜剧，姜文的电影总是那么抽象</div></div></div><div class="movies-item-content-item" data-index="13"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/532753" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320040.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="我不是药神"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;我不是药神&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【我不是药神】&quot;);" title="我不是药神">我不是药神</div><div class="movies-item-content-item-specification"><span class="year">2018 / </span><span class="genre">剧情 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">现实事件改编，对现实中的司法进步有一定的推动作用</div></div></div><div class="movies-item-content-item" data-index="14"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/400535" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320039.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="边境杀手2：边境战士"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;边境杀手2：边境战士&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【边境杀手2：边境战士】&quot;);" title="边境杀手2：边境战士">边境杀手2：边境战士</div><div class="movies-item-content-item-specification"><span class="year">2018 / </span><span class="genre">动作 犯罪 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">剧情是忘得差不多了，烂番茄82，应该有看头</div></div></div><div class="movies-item-content-item" data-index="15"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/766476" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320038.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="星游记之风暴法米拉"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;星游记之风暴法米拉&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【星游记之风暴法米拉】&quot;);" title="星游记之风暴法米拉">星游记之风暴法米拉</div><div class="movies-item-content-item-specification"><span class="year">2017 / </span><span class="genre">动画 / </span><span class="rating">⭐⭐⭐</span></div><div class="movies-item-content-item-description">看来星游记只能成为又一个遗憾了</div></div></div><div class="movies-item-content-item" data-index="16"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/281957" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320037.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="荒野猎人"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;荒野猎人&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【荒野猎人】&quot;);" title="荒野猎人">荒野猎人</div><div class="movies-item-content-item-specification"><span class="year">2015 / </span><span class="genre">西部 冒险 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">小李子电影，同样刷了很多次，除了主角不似人类，其他都完美</div></div></div><div class="movies-item-content-item" data-index="17"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/273481" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320036.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="边境杀手"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;边境杀手&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【边境杀手】&quot;);" title="边境杀手">边境杀手</div><div class="movies-item-content-item-specification"><span class="year">2015 / </span><span class="genre">动作 犯罪 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">特种部队宣传片，南美应该是最混乱的地方了，无论现实还是电影</div></div></div><div class="movies-item-content-item" data-index="18"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/211672" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320035.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="小黄人大眼萌"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;小黄人大眼萌&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【小黄人大眼萌】&quot;);" title="小黄人大眼萌">小黄人大眼萌</div><div class="movies-item-content-item-specification"><span class="year">2015 / </span><span class="genre">动画 冒险 喜剧 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">小黄人系列作品，主要是动画喜剧</div></div></div><div class="movies-item-content-item" data-index="19"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/618113" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320034.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="江湖论剑实录"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;江湖论剑实录&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【江湖论剑实录】&quot;);" title="江湖论剑实录">江湖论剑实录</div><div class="movies-item-content-item-specification"><span class="year">2014 / </span><span class="genre">喜剧 / </span><span class="rating">⭐⭐⭐</span></div><div class="movies-item-content-item-description">这个应该算作网剧，硬伤太多，笑点也少，给个及格吧</div></div></div><div class="movies-item-content-item" data-index="20"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/240832" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320033.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="超体"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;超体&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【超体】&quot;);" title="超体">超体</div><div class="movies-item-content-item-specification"><span class="year">2014 / </span><span class="genre">动作 科幻 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">寡姐作品，超能力系列，能力表现不错</div></div></div><div class="movies-item-content-item" data-index="21"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/82693" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320032.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="暴力街区"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;暴力街区&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【暴力街区】&quot;);" title="暴力街区">暴力街区</div><div class="movies-item-content-item-specification"><span class="year">2014 / </span><span class="genre">动作 犯罪 剧情 / </span><span class="rating">⭐⭐⭐</span></div><div class="movies-item-content-item-description">黑帮、跑酷，现在看估计不太行</div></div></div><div class="movies-item-content-item" data-index="22"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/80274" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320031.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="安德的游戏"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;安德的游戏&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【安德的游戏】&quot;);" title="安德的游戏">安德的游戏</div><div class="movies-item-content-item-specification"><span class="year">2013 / </span><span class="genre">动作 科幻 冒险 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">科幻电影启蒙，在当时的时间段，是一部好电影</div></div></div><div class="movies-item-content-item" data-index="23"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/49521" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320030.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="超人：钢铁之躯"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;超人：钢铁之躯&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【超人：钢铁之躯】&quot;);" title="超人：钢铁之躯">超人：钢铁之躯</div><div class="movies-item-content-item-specification"><span class="year">2013 / </span><span class="genre">动作 科幻 冒险 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">很喜欢的一部超能力电影，记得当时看了很久的超人类型的小说，可惜写的都很差劲，不知为何TMDB评分不高</div></div></div><div class="movies-item-content-item" data-index="24"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/165213" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320029.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="新世界"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;新世界&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【新世界】&quot;);" title="新世界">新世界</div><div class="movies-item-content-item-specification"><span class="year">2013 / </span><span class="genre">惊悚 犯罪 剧情 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">韩版无间道，电影拍的李政宰是真帅啊</div></div></div><div class="movies-item-content-item" data-index="25"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/72190" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320028.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="僵尸世界大战"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;僵尸世界大战&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【僵尸世界大战】&quot;);" title="僵尸世界大战">僵尸世界大战</div><div class="movies-item-content-item-specification"><span class="year">2013 / </span><span class="genre">动作 恐怖 科幻 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">僵尸题材电影，智商、逻辑在线，已看10次+，不可多得的好片</div></div></div><div class="movies-item-content-item" data-index="26"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/634528" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320027.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="神枪手"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;神枪手&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【神枪手】&quot;);" title="神枪手">神枪手</div><div class="movies-item-content-item-specification"><span class="year">2021 / </span><span class="genre">动作 剧情 惊悚 / </span><span class="rating">⭐⭐</span></div><div class="movies-item-content-item-description">B站推荐的，非常差，剧情设计不合理，强行降智煽情，不推荐</div></div></div><div class="movies-item-content-item" data-index="27"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/8247" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320026.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="心灵传输者"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;心灵传输者&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【心灵传输者】&quot;);" title="心灵传输者">心灵传输者</div><div class="movies-item-content-item-specification"><span class="year">2008 / </span><span class="genre">动作 冒险 科幻 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">这能力送外卖真不错，小了、格局小了</div></div></div><div class="movies-item-content-item" data-index="28"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/278" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320025.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="肖申克的救赎"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;肖申克的救赎&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【肖申克的救赎】&quot;);" title="肖申克的救赎">肖申克的救赎</div><div class="movies-item-content-item-specification"><span class="year">1994 / </span><span class="genre">剧情 犯罪 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">TOP500电影，经典永不过时</div></div></div><div class="movies-item-content-item" data-index="29"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/335977" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320024.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="夺宝奇兵5：神殿奇兵"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;夺宝奇兵5：神殿奇兵&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【夺宝奇兵5：神殿奇兵】&quot;);" title="夺宝奇兵5：神殿奇兵">夺宝奇兵5：神殿奇兵</div><div class="movies-item-content-item-specification"><span class="year">2023 / </span><span class="genre">冒险 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">时隔多年的续作，水平依旧在线，院线爆米花</div></div></div><div class="movies-item-content-item" data-index="30"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/217" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320023.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="夺宝奇兵4：水晶头骨王国"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;夺宝奇兵4：水晶头骨王国&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【夺宝奇兵4：水晶头骨王国】&quot;);" title="夺宝奇兵4：水晶头骨王国">夺宝奇兵4：水晶头骨王国</div><div class="movies-item-content-item-specification"><span class="year">2008 / </span><span class="genre">冒险 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">系列电影水平都在线，不错</div></div></div><div class="movies-item-content-item" data-index="31"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/89" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320022.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="夺宝奇兵3：圣战奇兵"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;夺宝奇兵3：圣战奇兵&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【夺宝奇兵3：圣战奇兵】&quot;);" title="夺宝奇兵3：圣战奇兵">夺宝奇兵3：圣战奇兵</div><div class="movies-item-content-item-specification"><span class="year">1989 / </span><span class="genre">冒险 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">系列电影水平都在线，不错</div></div></div><div class="movies-item-content-item" data-index="32"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/87" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320021.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="夺宝奇兵2：魔域奇兵"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;夺宝奇兵2：魔域奇兵&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【夺宝奇兵2：魔域奇兵】&quot;);" title="夺宝奇兵2：魔域奇兵">夺宝奇兵2：魔域奇兵</div><div class="movies-item-content-item-specification"><span class="year">1984 / </span><span class="genre">冒险 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">系列电影水平都在线，不错</div></div></div><div class="movies-item-content-item" data-index="33"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/85" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320020.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="夺宝奇兵：法柜奇兵"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;夺宝奇兵：法柜奇兵&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【夺宝奇兵：法柜奇兵】&quot;);" title="夺宝奇兵：法柜奇兵">夺宝奇兵：法柜奇兵</div><div class="movies-item-content-item-specification"><span class="year">1981 / </span><span class="genre">冒险 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">印第安纳琼斯系列第一部，经典开山鼻祖</div></div></div><div class="movies-item-content-item" data-index="34"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/122917" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320019.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="霍比特人3：五军之战"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;霍比特人3：五军之战&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【霍比特人3：五军之战】&quot;);" title="霍比特人3：五军之战">霍比特人3：五军之战</div><div class="movies-item-content-item-specification"><span class="year">2014 / </span><span class="genre">冒险 奇幻 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">霍比特人系列第三部，战争场面宏大热血，好看</div></div></div><div class="movies-item-content-item" data-index="35"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/57158" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320018.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="霍比特人2：史矛革之战"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;霍比特人2：史矛革之战&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【霍比特人2：史矛革之战】&quot;);" title="霍比特人2：史矛革之战">霍比特人2：史矛革之战</div><div class="movies-item-content-item-specification"><span class="year">2013 / </span><span class="genre">冒险 奇幻 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">霍比特人系列第二部，继续之前的冒险，水平依然在线</div></div></div><div class="movies-item-content-item" data-index="36"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/49051" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320017.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="霍比特人1：意外之旅"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;霍比特人1：意外之旅&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【霍比特人1：意外之旅】&quot;);" title="霍比特人1：意外之旅">霍比特人1：意外之旅</div><div class="movies-item-content-item-specification"><span class="year">2012 / </span><span class="genre">冒险 奇幻 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">西幻著名文学作品改编，是指环王系列的前传，非常好看</div></div></div><div class="movies-item-content-item" data-index="37"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/603692" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320016.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="疾速追杀4"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;疾速追杀4&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【疾速追杀4】&quot;);" title="疾速追杀4">疾速追杀4</div><div class="movies-item-content-item-specification"><span class="year">2023 / </span><span class="genre">动作 惊悚 犯罪 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">疾速追杀系列第四部，不知道结尾威克是不是真的死了，期待续作</div></div></div><div class="movies-item-content-item" data-index="38"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/458156" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320015.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="疾速追杀3"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;疾速追杀3&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【疾速追杀3】&quot;);" title="疾速追杀3">疾速追杀3</div><div class="movies-item-content-item-specification"><span class="year">2019 / </span><span class="genre">动作 惊悚 犯罪 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">疾速追杀系列第三部，爆米花电影，好看</div></div></div><div class="movies-item-content-item" data-index="39"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/324552" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320014.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="疾速追杀2"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;疾速追杀2&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【疾速追杀2】&quot;);" title="疾速追杀2">疾速追杀2</div><div class="movies-item-content-item-specification"><span class="year">2017 / </span><span class="genre">动作 惊悚 犯罪 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">疾速追杀续作，爆米花电影，好看</div></div></div><div class="movies-item-content-item" data-index="40"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/245891" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320013.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="疾速追杀"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;疾速追杀&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【疾速追杀】&quot;);" title="疾速追杀">疾速追杀</div><div class="movies-item-content-item-specification"><span class="year">2014 / </span><span class="genre">动作 惊悚 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">动作戏比较考究，但还是摆脱不了一人上场，其他人看戏的局面</div></div></div><div class="movies-item-content-item" data-index="41"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/11621" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320012.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="红猪"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;红猪&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【红猪】&quot;);" title="红猪">红猪</div><div class="movies-item-content-item-specification"><span class="year">1992 / </span><span class="genre">动画 奇幻 冒险 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">宫崎骏作品，本页顶图文字引用自此动画电影</div></div></div><div class="movies-item-content-item" data-index="42"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/8392" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320011.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="龙猫"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;龙猫&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【龙猫】&quot;);" title="龙猫">龙猫</div><div class="movies-item-content-item-specification"><span class="year">1988 / </span><span class="genre">动画 奇幻 家庭 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">宫崎骏作品，龙猫这种生物真是可爱</div></div></div><div class="movies-item-content-item" data-index="43"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/128" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320010.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="幽灵公主"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;幽灵公主&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【幽灵公主】&quot;);" title="幽灵公主">幽灵公主</div><div class="movies-item-content-item-specification"><span class="year">1997 / </span><span class="genre">动画 奇幻 冒险 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">宫崎骏作品，经典之作，力荐</div></div></div><div class="movies-item-content-item" data-index="44"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/268896" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320009.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="环太平洋：雷霆再起"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;环太平洋：雷霆再起&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【环太平洋：雷霆再起】&quot;);" title="环太平洋：雷霆再起">环太平洋：雷霆再起</div><div class="movies-item-content-item-specification"><span class="year">2018 / </span><span class="genre">动作 科幻 冒险 / </span><span class="rating">⭐⭐⭐</span></div><div class="movies-item-content-item-description">中规中矩的续作，机甲已经没有第一部的厚重感，感官上会有差异</div></div></div><div class="movies-item-content-item" data-index="45"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/68726" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320008.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="环太平洋"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;环太平洋&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【环太平洋】&quot;);" title="环太平洋">环太平洋</div><div class="movies-item-content-item-specification"><span class="year">2013 / </span><span class="genre">动作 科幻 冒险 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">机甲、怪兽、科幻，拳拳到肉，观感极佳</div></div></div><div class="movies-item-content-item" data-index="46"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/166426" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320007.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="加勒比海盗5：死无对证"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;加勒比海盗5：死无对证&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【加勒比海盗5：死无对证】&quot;);" title="加勒比海盗5：死无对证">加勒比海盗5：死无对证</div><div class="movies-item-content-item-specification"><span class="year">2017 / </span><span class="genre">冒险 动作 奇幻 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">他带着他的船扬帆起航，从那之后消失在了海的尽头。也许他还在进行着他的冒险之旅，也许他已经葬身于海底，或许有一天我们还能看到黑珍珠号的风帆在海平面上飘荡</div></div></div><div class="movies-item-content-item" data-index="47"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/1865" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320006.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="加勒比海盗4：惊涛怪浪"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;加勒比海盗4：惊涛怪浪&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【加勒比海盗4：惊涛怪浪】&quot;);" title="加勒比海盗4：惊涛怪浪">加勒比海盗4：惊涛怪浪</div><div class="movies-item-content-item-specification"><span class="year">2011 / </span><span class="genre">冒险 动作 奇幻 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">加勒比海盗系列之第四部，经典作品，力荐</div></div></div><div class="movies-item-content-item" data-index="48"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/285" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320005.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="加勒比海盗3：世界的尽头"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;加勒比海盗3：世界的尽头&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【加勒比海盗3：世界的尽头】&quot;);" title="加勒比海盗3：世界的尽头">加勒比海盗3：世界的尽头</div><div class="movies-item-content-item-specification"><span class="year">2007 / </span><span class="genre">冒险 奇幻 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">加勒比海盗系列之第三部，经典作品，力荐</div></div></div><div class="movies-item-content-item" data-index="49"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/58" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320004.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="加勒比海盗2：聚魂棺"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;加勒比海盗2：聚魂棺&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【加勒比海盗2：聚魂棺】&quot;);" title="加勒比海盗2：聚魂棺">加勒比海盗2：聚魂棺</div><div class="movies-item-content-item-specification"><span class="year">2006 / </span><span class="genre">冒险 奇幻 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">加勒比海盗系列之第二部，经典作品，力荐</div></div></div><div class="movies-item-content-item" data-index="50"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/22" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320003.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="加勒比海盗：黑珍珠号的诅咒"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;加勒比海盗：黑珍珠号的诅咒&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【加勒比海盗：黑珍珠号的诅咒】&quot;);" title="加勒比海盗：黑珍珠号的诅咒">加勒比海盗：黑珍珠号的诅咒</div><div class="movies-item-content-item-specification"><span class="year">2003 / </span><span class="genre">冒险 奇幻 动作 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">加勒比海盗系列首部，经典作品，力荐</div></div></div><div class="movies-item-content-item" data-index="51"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/4935" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320002.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="哈尔的移动城堡"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;哈尔的移动城堡&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【哈尔的移动城堡】&quot;);" title="哈尔的移动城堡">哈尔的移动城堡</div><div class="movies-item-content-item-specification"><span class="year">2004 / </span><span class="genre">奇幻 动画 冒险 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">爱与勇气能够打破所有魔咒</div></div></div><div class="movies-item-content-item" data-index="52"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/movie/129" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/movie/2320001.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="千与千寻"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;千与千寻&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【千与千寻】&quot;);" title="千与千寻">千与千寻</div><div class="movies-item-content-item-specification"><span class="year">2001 / </span><span class="genre">动画 家庭 奇幻 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">看过的第一部宫崎骏动画电影</div></div></div></div><!-- 新增分页按钮 box--><div class="movies-pagination-box"><div class="pagination-nav"><button class="prev-button">Prev</button><div class="page-numbers"></div><button class="next-button">Next</button></div></div></div></div><div class="goodmovies-item"><h2 class="goodmovies-title">看过的剧集（7）</h2><div class="goodmovies-item-description"></div><div class="movies-item"><div class="movies-item-content"><div class="movies-item-content-item" data-index="0"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/93405" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/tv/2330007.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="鱿鱼游戏"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;鱿鱼游戏&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【鱿鱼游戏】&quot;);" title="鱿鱼游戏">鱿鱼游戏</div><div class="movies-item-content-item-specification"><span class="year">2021 / </span><span class="genre">动作冒险 悬疑 剧情 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">赏金逃杀，人性考验，让我想起来了《饥饿游戏》</div></div></div><div class="movies-item-content-item" data-index="1"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/40008" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/tv/2330006.webp" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="汉尼拔（第二季）"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;汉尼拔（第二季）&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【汉尼拔（第二季）】&quot;);" title="汉尼拔（第二季）">汉尼拔（第二季）</div><div class="movies-item-content-item-specification"><span class="year">2014 / </span><span class="genre">剧情 犯罪 惊悚 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">续集水平依旧在线，我个人觉得过渡场景太多了，冲突不够明显，有点像为了拉长剧集时间而设计的</div></div></div><div class="movies-item-content-item" data-index="2"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/40008" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/tv/2330005.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="汉尼拔（第一季）"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;汉尼拔（第一季）&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【汉尼拔（第一季）】&quot;);" title="汉尼拔（第一季）">汉尼拔（第一季）</div><div class="movies-item-content-item-specification"><span class="year">2013 / </span><span class="genre">剧情 犯罪 惊悚 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">拔叔的演技真是一流，完美表现了一个疯狂、冷血又聪明的罪犯</div></div></div><div class="movies-item-content-item" data-index="3"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/76479" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/tv/2330004.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="黑袍纠察队（第一季）"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;黑袍纠察队（第一季）&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【黑袍纠察队（第一季）】&quot;);" title="黑袍纠察队（第一季）">黑袍纠察队（第一季）</div><div class="movies-item-content-item-specification"><span class="year">2019 / </span><span class="genre">剧情 动作 冒险 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">黑暗风超级英雄，尺度很大</div></div></div><div class="movies-item-content-item" data-index="4"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/71100" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/tv/2330003.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="人民的名义"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;人民的名义&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【人民的名义】&quot;);" title="人民的名义">人民的名义</div><div class="movies-item-content-item-specification"><span class="year">2017 / </span><span class="genre">剧情 犯罪 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">国产反腐剧，政治尺度较大，演员演技在线</div></div></div><div class="movies-item-content-item" data-index="5"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/55925" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/tv/2330002.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="半泽直树"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;半泽直树&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【半泽直树】&quot;);" title="半泽直树">半泽直树</div><div class="movies-item-content-item-specification"><span class="year">2013 / </span><span class="genre">剧情 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">日剧，职场复仇主题，还不错</div></div></div><div class="movies-item-content-item" data-index="6"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/100088" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/tv/2330001.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="最后生还者"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;最后生还者&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【最后生还者】&quot;);" title="最后生还者">最后生还者</div><div class="movies-item-content-item-specification"><span class="year">2023 / </span><span class="genre">冒险 末日 生存 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">印象深刻的游戏改编作品，难得的精品</div></div></div></div><!-- 新增分页按钮 box--><div class="movies-pagination-box"><div class="pagination-nav"><button class="prev-button">Prev</button><div class="page-numbers"></div><button class="next-button">Next</button></div></div></div></div><div class="goodmovies-item"><h2 class="goodmovies-title">看过的动画（6）</h2><div class="goodmovies-item-description"></div><div class="movies-item"><div class="movies-item-content"><div class="movies-item-content-item" data-index="0"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/240411" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/anime/2340007.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="胆大党（第一季）"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;胆大党（第一季）&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【胆大党（第一季）】&quot;);" title="胆大党（第一季）">胆大党（第一季）</div><div class="movies-item-content-item-specification"><span class="year">2024 / </span><span class="genre">科幻 / </span><span class="rating">⭐⭐⭐⭐⭐</span><span class="watch-date"> / 2025.3.4</span></div><div class="movies-item-content-item-description">第一季已全部看完，但是结尾断的也太离谱了点吧，非常期待第二季。</div></div></div><div class="movies-item-content-item" data-index="1"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/100049" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/anime/2340006.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="总之就是非常可爱（第一季）"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;总之就是非常可爱（第一季）&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【总之就是非常可爱（第一季）】&quot;);" title="总之就是非常可爱（第一季）">总之就是非常可爱（第一季）</div><div class="movies-item-content-item-specification"><span class="year">2023 / </span><span class="genre">动画 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">如果你愿意和我结婚，那我就跟你交往</div></div></div><div class="movies-item-content-item" data-index="2"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/82739" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/anime/2340005.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="青春猪头少年不会梦到兔女郎学姐"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;青春猪头少年不会梦到兔女郎学姐&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【青春猪头少年不会梦到兔女郎学姐】&quot;);" title="青春猪头少年不会梦到兔女郎学姐">青春猪头少年不会梦到兔女郎学姐</div><div class="movies-item-content-item-specification"><span class="year">2018 / </span><span class="genre">动画 剧情 爱情 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">很有意思的剧情设定，我要是有主角的口才何愁至今单身😮</div></div></div><div class="movies-item-content-item" data-index="3"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/86831-love-death-robots/season/2" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/anime/2340004.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="Love, Death &amp; Robots（第二季）"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;Love, Death &amp; Robots（第二季）&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【Love, Death &amp; Robots（第二季）】&quot;);" title="Love, Death &amp; Robots（第二季）">Love, Death &amp; Robots（第二季）</div><div class="movies-item-content-item-specification"><span class="year">2021 / </span><span class="genre">动画 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">第二季，仍是不同的小故事，水平依旧在线</div></div></div><div class="movies-item-content-item" data-index="4"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/86831" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/anime/2340003.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="Love, Death &amp; Robots（第一季）"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;Love, Death &amp; Robots（第一季）&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【Love, Death &amp; Robots（第一季）】&quot;);" title="Love, Death &amp; Robots（第一季）">Love, Death &amp; Robots（第一季）</div><div class="movies-item-content-item-specification"><span class="year">2019 / </span><span class="genre">动画 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">多部短篇动画组成的系列，每一部都是一个小故事，风格各异，制作水准很高</div></div></div><div class="movies-item-content-item" data-index="5"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/118797" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/anime/2340002.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="星游记"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;星游记&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【星游记】&quot;);" title="星游记">星游记</div><div class="movies-item-content-item-specification"><span class="year">2011 / </span><span class="genre">动作 冒险 动画 / </span><span class="rating">⭐⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">很早的时候看的动漫，很热血很好看，可惜剧情只到麦当中了蔓影术，后面就没有了</div></div></div><div class="movies-item-content-item" data-index="6"><div class="movies-item-content-item-cover"><a class="movie-link" href="https://www.themoviedb.org/tv/105248" target="_blank"><img class="movies-item-content-item-image movie-image" data-lazy-src="/images/movies/anime/2340001.jpg" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="赛博朋克：边缘行者"></a></div><div class="movies-item-content-item-info"><div class="movies-item-content-item-name" onclick="rm.rightmenuCopyText(&quot;赛博朋克：边缘行者&quot;);anzhiyu.snackbarShow(&quot;已复制电影名： 【赛博朋克：边缘行者】&quot;);" title="赛博朋克：边缘行者">赛博朋克：边缘行者</div><div class="movies-item-content-item-specification"><span class="year">2022 / </span><span class="genre">动作冒险 动画 / </span><span class="rating">⭐⭐⭐⭐</span></div><div class="movies-item-content-item-description">动画的播出给游戏赛博朋克2077增加了不少热度，虽然比较喜欢赛博朋克风格，但我完全不喜欢BD结局</div></div></div></div><!-- 新增分页按钮 box--><div class="movies-pagination-box"><div class="pagination-nav"><button class="prev-button">Prev</button><div class="page-numbers"></div><button class="next-button">Next</button></div></div></div></div></div><!-- 在Pug模板底部嵌入JavaScript代码--><script>var columns = document.querySelectorAll('.goodmovies-item');

columns.forEach(column => {
  const moviesItems = column.querySelectorAll('.movies-item-content-item');
  const prevButton = column.querySelector('.prev-button');
  const nextButton = column.querySelector('.next-button');
  const pageNumbers = column.querySelector('.page-numbers');
  const paginationBox = column.querySelector('.movies-pagination-box');
  const itemsPerPage = 9;  
  let currentPage = 1;

  const totalItems = moviesItems.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // 如果电影数量小于9，则隐藏分页按钮
  if (totalItems <= itemsPerPage) {
    paginationBox.style.display = 'none';
  } else {
    paginationBox.style.display = 'flex';
  }

  // 显示当前页的电影项目
  function renderPage(page) {
    const start = (page - 1) * itemsPerPage;
    const end = start + itemsPerPage;

    // 显示当前页的项目，其他项目隐藏
    moviesItems.forEach((item, index) => {
      item.style.display = index >= start && index < end ? 'flex' : 'none';
    });

    // 更新上一页按钮的文字
    if (page === 1) {
      prevButton.textContent = '首页';
    } else {
      prevButton.textContent = '上页';
    }

    // 更新下一页按钮的文字
    if (page === totalPages) {
      nextButton.textContent = '尾页';
    } else if (page === 1) {
      nextButton.textContent = '下页';
    } else {
      nextButton.textContent = '下页';
    }

    // 更新页码按钮
    pageNumbers.innerHTML = '';
    for (let i = 1; i <= totalPages; i++) {
      const pageButton = document.createElement('button');
      pageButton.textContent = i;
      pageButton.classList.add('page-button');
      if (i === page) {
        pageButton.classList.add('active');
      }
      pageButton.addEventListener('click', () => {
        currentPage = i;
        renderPage(currentPage);
      });
      pageNumbers.appendChild(pageButton);
    }
  }

  // 上一页按钮点击事件
  prevButton.addEventListener('click', () => {
    if (currentPage > 1) {
      currentPage--;
      renderPage(currentPage);
    }
  });

  // 下一页按钮点击事件
  nextButton.addEventListener('click', () => {
    if (currentPage < totalPages) {
      currentPage++;
      renderPage(currentPage);
    }
  });

  // 初始化第一页
  renderPage(currentPage);
});

                  //- .movies-item-content-item-toolbar
                  //-   if iten.link.includes('https://') || iten.link.includes('http://')
                  //-     a.movies-item-content-item-link(href= iten.link, target='_blank') 详情
                  //-     .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                  //-       i.anzhiyufont.anzhiyu-icon-message
                  //-   else  
                  //-     a.movies-item-content-item-link(href= iten.link, target='_blank') 查看文章
                  //-     .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                  //-       i.anzhiyufont.anzhiyu-icon-message</script><hr/><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)">匿名评论</a><a href="/privacy" style="margin-left: 4px">隐私政策</a></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div></div></div><div class="comment-barrage"></div></div></div></main><footer id="footer"><div id="footer-wrap"></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">&copy;2024 - 2025 By <a class="footer-bar-link" href="/" title="c2hapmll" target="_blank">c2hapmll</a></div></div><div id="footer-type-tips"></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index" title="皖ICP备20000482号-2">皖ICP备20000482号-2</a><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.mps.gov.cn/#/query/webSearch" title="皖公网安备 34120402000423号">皖公网安备 34120402000423号</a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">6</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">9</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">3</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" target="_blank" rel="noopener" href="https://home.shijie.icu/" title="我的主页"><img class="back-menu-item-icon" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/32.png" alt="我的主页"/><span class="back-menu-item-text">我的主页</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 0.88rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 0.88rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 0.88rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 0.88rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 0.88rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 0.88rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 0.88rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 0.88rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 0.88rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button></div><div id="rightside-config-show"><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><a id="to_comment" href="#post-comment" title="直达评论"><i class="anzhiyufont anzhiyu-icon-comments"></i></a><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><span id="loading-status"></span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="is-center" id="loading-database"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-pulse-icon"></i><span>  数据库加载中</span></div><div class="search-wrap"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div><hr/><div id="local-search-results"></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size: 1rem;"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://y.qq.com/n/ryqq/playlist/8802438608&quot;, &quot;_blank&quot;);" style="display: none;"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://cdn.cbd.int/instant.page@5.2.0/instantpage.js" type="module"></script><script src="https://cdn.cbd.int/vanilla-lazyload@17.8.5/dist/lazyload.iife.min.js"></script><script src="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.js"></script><canvas id="universe"></canvas><script async src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script async src="/anzhiyu/random.js"></script><script src="/js/search/local-search.js"></script><div class="js-pjax"><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="movies"></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
        region: '',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script>var visitorMail = "";
</script><script async data-pjax src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script async src="https://at.alicdn.com/t/c/font_4729035_3m5int5al8c.js"></script><script src="/js/countdown.js"></script><script src="/js/getnowplaying.js"></script><script src="/js/badge.js"></script><script src="/js/update.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/aplayer/APlayer.min.css" media="print" onload="this.media='all'"><script src="https://cdn.cbd.int/anzhiyu-blog-static@1.0.1/js/APlayer.min.js"></script><script src="https://cdn.cbd.int/hexo-anzhiyu-music@1.0.1/assets/js/Meting2.min.js"></script><script src="https://cdn.cbd.int/pjax@0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: false,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script><script charset="UTF-8" src="https://cdn.cbd.int/anzhiyu-theme-static@1.1.5/accesskey/accesskey.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div></body></html>
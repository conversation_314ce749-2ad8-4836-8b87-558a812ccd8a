.comment-barrage {
  position: fixed;
  bottom: 0;
  right: 60px;
  padding: 0 0 20px 10px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: flex-end;
  z-index: 999;
  transition: 0.3s;
}
@media screen and (max-width: 768px) {
  .comment-barrage {
    display: none !important;
  }
}
.comment-barrage-item {
  min-width: 300px;
  max-width: 300px;
  width: fit-content;
  min-height: 80px;
  max-height: 150px;
  margin: 4px;
  padding: 8px 14px;
  background: var(--anzhiyu-maskbgdeep);
  border-radius: 8px;
  color: var(--anzhiyu-fontcolor);
  animation: barrageIn 0.6s cubic-bezier(0.42, 0, 0.3, 1.11);
  transition: 0.3s;
  display: flex;
  flex-direction: column;
  border: var(--style-border);
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transform: translateZ(0);
  position: fixed;
  box-shadow: var(--anzhiyu-shadow-border);
}
.comment-barrage-item:hover {
  border: var(--style-border-hover);
  box-shadow: var(--anzhiyu-shadow-main);
}
.comment-barrage-item.out {
  opacity: 0;
  animation: barrageOut 0.6s cubic-bezier(0.42, 0, 0.3, 1.11);
}
.comment-barrage-item a.barrageContent:hover {
  color: var(--anzhiyu-main);
}
.comment-barrage-item.hovered {
  opacity: 0;
}
.comment-barrage-item .comment-barrage-close {
  color: var(--anzhiyu-secondtext);
  cursor: pointer;
  line-height: 1;
  padding: 4px;
}
.comment-barrage-item .comment-barrage-close:hover {
  color: var(--anzhiyu-main);
}
.comment-barrage-item pre {
  display: none;
}
.comment-barrage-item p img:not(.tk-owo-emotion) {
  display: none;
}

.comment-barrage-item .barrageHead {
  height: 30px;
  padding: 0;
  line-height: 30px;
  font-size: 12px;
  border-bottom: var(--style-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 700;
  padding-bottom: 6px;
}
.comment-barrage-item .barrageHead .barrageTitle {
  color: var(--anzhiyu-card-bg);
  margin-right: 8px;
  background: var(--anzhiyu-fontcolor);
  line-height: 1;
  padding: 4px;
  border-radius: 4px;
}
.comment-barrage-item .barrageHead .barrageTitle.barrageBloggerTitle {
  background: var(--anzhiyu-orange);
}
.comment-barrage-item .barrageAvatar {
  width: 16px;
  height: 16px;
  margin: 0;
  margin-left: auto;
  margin-right: 8px;
  border-radius: 50%;
  background: var(--anzhiyu-secondbg);
}
.comment-barrage-item .barrageContent {
  font-size: 14px !important;
  font-weight: 400 !important;
  height: calc(100% - 30px);
  overflow: scroll;
  cursor: pointer;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar {
  height: 0;
  width: 4px;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar-button {
  display: none;
}
.comment-barrage-item .barrageContent p {
  margin: 8px 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.comment-barrage-item .barrageContent blockquote p {
  margin: 0;
}
.comment-barrage-item .barrageContent h1,
.comment-barrage-item .barrageContent h2,
.comment-barrage-item .barrageContent h3,
.comment-barrage-item .barrageContent h4 {
  font-size: 14px !important;
  font-weight: 400 !important;
  margin: 8px 0 !important;
}

<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"><title>2025年02月15日-记一次去医院看牙的经历 | 我的博客</title><meta name="keywords" content="医院,牙科"><meta name="author" content="c2hapmll"><meta name="copyright" content="c2hapmll"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f7f9fe"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="2025年02月15日-记一次去医院看牙的经历"><meta name="application-name" content="2025年02月15日-记一次去医院看牙的经历"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f7f9fe"><meta property="og:type" content="article"><meta property="og:title" content="2025年02月15日-记一次去医院看牙的经历"><meta property="og:url" content="https://shijie.icu/post/20250215190000.html"><meta property="og:site_name" content="我的博客"><meta property="og:description" content="如果你是真的能扛顶着牙疼过日子，那恭喜你，最终结果只有一个，那就是根管治疗。 “牙医恐惧”，首先一定是疼。除了生理上的疼之外，也有心理上的，心疼钱。   前因24年年中就感觉牙齿不适，偶尔吃饭时会掉落牙齿碎片，当时没觉得是自己的牙齿损坏了，而是以为饭里有些骨头啥的东西咬碎了，后来刷牙时发现右边牙"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://shijie.icu/images/posts/2025/021501/top.jpg"><meta property="article:author" content="c2hapmll"><meta property="article:tag"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://shijie.icu/images/posts/2025/021501/top.jpg"><meta name="description" content="如果你是真的能扛顶着牙疼过日子，那恭喜你，最终结果只有一个，那就是根管治疗。 “牙医恐惧”，首先一定是疼。除了生理上的疼之外，也有心理上的，心疼钱。   前因24年年中就感觉牙齿不适，偶尔吃饭时会掉落牙齿碎片，当时没觉得是自己的牙齿损坏了，而是以为饭里有些骨头啥的东西咬碎了，后来刷牙时发现右边牙"><link rel="shortcut icon" href="/images/themes/32.png"><link rel="canonical" href="https://shijie.icu/post/20250215190000.html"><link rel="preconnect" href="//cdn.cbd.int"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><meta name="google-site-verification" content="xxx"/><meta name="baidu-site-verification" content="code-xxx"/><meta name="msvalidate.01" content="xxx"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.cbd.int/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media='all'"><script>const GLOBAL_CONFIG = {
  linkPageTop: undefined,
  peoplecanvas: undefined,
  postHeadAiDescription: undefined,
  diytitle: undefined,
  LA51: undefined,
  greetingBox: undefined,
  twikooEnvId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
  commentBarrageConfig:undefined,
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: false,
  mainTone: undefined,
  authorStatus: {"skills":["🤖️ 数码科技爱好者","🔍 分享与热心帮助","🏠 智能家居小能手"]},
  algolia: undefined,
  localSearch: {"path":"/search.xml","preload":true,"languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: undefined,
  highlight: {"plugin":"prismjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    simplehomepage: true,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: c2hapmll","link":"链接: ","source":"来源: 我的博客","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: false,
  shortcutKey: undefined,
  autoDarkmode: true
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  configTitle: '我的博客',
  title: '2025年02月15日-记一次去医院看牙的经历',
  postAI: '',
  pageFillDescription: '前因, 初诊, 治疗, 价格amp术后, 复诊, 价格amp术后, 其他, 参考如果你是真的能扛顶着牙疼过日子那恭喜你最终结果只有一个那就是根管治疗牙医恐惧首先一定是疼除了生理上的疼之外也有心理上的心疼钱前因年年中就感觉牙齿不适偶尔吃饭时会掉落牙齿碎片当时没觉得是自己的牙齿损坏了而是以为饭里有些骨头啥的东西咬碎了后来刷牙时发现右边牙齿根部似乎出了个洞但是也不痒也不疼加上本人对于医院有点惧怕于是就没有太过在意年春节情况变得更加严重由于过年期间大吃大喝可能刺激到了损坏的牙齿根部嚼着嚼着一下子咬到硬物时会发生剧烈疼痛脸都能疼变形的那种无法忍受的疼经历了几次疼痛后右侧牙齿几乎无法正常咬碎食物只能依靠左侧正常牙齿吃饭但当时想着回苏州可以刷医保能省不少钱于是硬生生忍受了下来我真是神人终于过完了年来到苏州的第一件事就是挂牙科的号经历一顿波折才挂上号周六上午的号这期间吃饭时是真的折磨加上去之前号的时候牙齿好像还发炎了隐隐作痛只能吃布洛芬止痛夜里也无法好好休息还好去医院的时候已经消炎了初诊专家号是别想了打工人想在周末挂主流科室的专家号还是很困难的在苏州上挂的苏州华夏口腔医院牙体牙髓病二科普通门诊的号时间为号周六上午去的时候晚了一点才在自主取号机取到号去导引台签到分配医生由于我第一次来没有熟悉的医生分配的随机医生然后找地方坐着等叫号去的比较晚没过几分钟就叫到我了走到门诊室后医生问我有什么问题我说下右侧的牙疼医生让我躺在专门的牙科椅子上检查牙齿上方的灯光还晃了我一下短暂的检查过后说最里面的牙蛀的很严重已经影响到牙神经了可能需要做根管治疗倒数第二颗也有点损坏需要补一下开的诊断单写的慢性牙周炎和龋齿治疗方案经过我同意后让我去拍个牙片给我开了个牙片单子去放射区签到排队拍了片后不用取直接回牙医处就行医生在电脑上看过片后说确定要做根管治疗的问我要不要做我同意后方案后医生拿出根管治疗知情同意书让我签名签名后就到了最折磨人的治疗阶段了治疗医生会提前说明如有不适吐口水有专门的医疗器具吸剧烈疼痛需要举左手示意且会询问你来之前是否吃过饭我是提前吃了瓶八宝粥和面包根管治疗的时间比较长所以先做的是补牙补牙需要将棉花垫在需要补的那颗牙的两侧用于吸收唾液防止补牙材料被污染这个阶段我就出问题了我的呕吐反应非常严重舌头不习惯旁边有异物垫了两次后一直出现干呕无法补牙于是就让我左侧头控制唾液流向才可以进行下一步补牙前需要先把被蛀成黑色的部分磨除再填充材料这个过程非常离谱因为我右侧牙神经已经损坏有一点轻微的震动就会非常疼磨得过程得声音也很恐怖比较反人类不过过程没什么问题忍忍就行了接下来时间比较长的就是根管治疗了在需要根管治疗的牙齿旁边围上一块有弹性的手术布因为我也看不见就只能感觉到又是磨又是喷一种冰凉的喷剂根管的过程基本没什么感觉偶尔有些疼痛是震动带来的但因为冰凉的关系感觉也不太痛我一直以为需要打麻醉针原来不需要期间没什么问题但时间很长直到点半左右才完成价格术后首先就是漱口漱完一阵清爽起来后医生说小时内不要吃东西补的那颗牙暂时不要用舌头去舔根管的那颗牙要想完全恢复还需要做个牙冠右侧牙齿近期不要咀嚼等号复诊再看看情况于是我又提前挂了号上午点的号本次开销刷的是医保消费总费用元统筹支付元个人账户支付元目前的感觉非常好就是吃饭的时候只能用左侧牙吃饭有些不习惯吃着吃着就不自觉的左侧头有人术后还有牙疼的情况我这里几乎感觉不到等牙冠一做就又能正常吃饭了就等着复诊了复诊依旧自行取号但是这次去导诊台签到的同时需要指定复诊医生签完到坐等叫号轮到我时医生让我先去拍之前做根管牙的牙片是真快啊大约秒就出片拍完后回到医生处就开始做检查根管情况中途也没什么意外做完牙冠后又让我去拍个片子拍完回来后问我是否牙冠过高影响咀嚼然后开始修正一下牙冠高度让牙冠和周围牙齿齐平然后就完事了不过仍然要在天后看根管的恢复情况价格术后本次开销刷医保卡消费总费用统筹支付元个人支付元这里我注意到有个根管填充术的收费项目难道复诊时还需要清理根管內部已经过去两天了现在能够正常吃饭简直是太爽了其他复诊完成后一直在听医生说的注意事项忘记让医生写张病假单了只能用一天年假血亏医生说左边的牙齿情况也要注意但是如果要进一步检查和治疗的话得多等一段时间后当天下午预约了苏州博物馆本馆我以为牙冠做的很快实际消耗了不少时间我又没带相机只能又做地铁回家拿浪费了不少时间血亏年假参考内有大矿超近距离拍龋齿修复隔着屏幕看痛了影视飓风牙医恐惧调查报告克服恐惧我终于也成了有假牙的人少数派',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2025-02-27 15:37:49',
  postMainColor: '',
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#18171d')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f7f9fe')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/costom_widget.css"><link rel="stylesheet" href="/css/badge.css"><meta name="generator" content="Hexo 7.3.0"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="/images/themes/32.png"/><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"/><script async="async" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><a id="site-name" href="/" accesskey="h"><div class="title">我的博客</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"/><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole();"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" alt="微信" src="/images/themes/reward/wechat.jpg"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="/images/themes/reward/alipay.jpg"/></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title"> 最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 1.05rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 1.05rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 1.05rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 1.05rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 1.05rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 1.05rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 1.05rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 1.05rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 1.05rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/02/"><span class="card-archive-list-date">二月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/12/"><span class="card-archive-list-date">十二月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/11/"><span class="card-archive-list-date">十一月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/10/"><span class="card-archive-list-date">十月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/09/"><span class="card-archive-list-date">九月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li></ul></div><hr/></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%97%A5%E5%B8%B8/" itemprop="url">日常</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%8C%BB%E9%99%A2/" tabindex="-1" itemprop="url"> <span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>医院</span></a><a class="article-meta__tags" href="/tags/%E7%89%99%E7%A7%91/" tabindex="-1" itemprop="url"> <span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>牙科</span></a></span></div></div><h1 class="post-title" itemprop="name headline">2025年02月15日-记一次去医院看牙的经历</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-02-15T11:00:00.000Z" title="发表于 2025-02-15 19:00:00">2025-02-15</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-02-27T07:37:49.000Z" title="更新于 2025-02-27 15:37:49">2025-02-27</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">1.9k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>5分钟</span></span><span class="post-meta-separator"></span><span class="post-meta-pv-cv" id="" data-flag-title="2025年02月15日-记一次去医院看牙的经历"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="busuanzi_value_page_pv"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator">       </span><span class="post-meta-position" title="作者IP属地为苏州"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>苏州</span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="/images/posts/2025/021501/top.jpg"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container" itemscope itemtype="https://shijie.icu/post/20250215190000.html"><header><a class="post-meta-categories" href="/categories/%E6%97%A5%E5%B8%B8/" itemprop="url">日常</a><a href="/tags/%E5%8C%BB%E9%99%A2/" tabindex="-1" itemprop="url">医院</a><a href="/tags/%E7%89%99%E7%A7%91/" tabindex="-1" itemprop="url">牙科</a><h1 id="CrawlerTitle" itemprop="name headline">2025年02月15日-记一次去医院看牙的经历</h1><span itemprop="author" itemscope itemtype="http://schema.org/Person">c2hapmll</span><time itemprop="dateCreated datePublished" datetime="2025-02-15T11:00:00.000Z" title="发表于 2025-02-15 19:00:00">2025-02-15</time><time itemprop="dateCreated datePublished" datetime="2025-02-27T07:37:49.000Z" title="更新于 2025-02-27 15:37:49">2025-02-27</time></header><blockquote>
<ol>
<li>如果你是真的能扛顶着牙疼过日子，那恭喜你，最终结果只有一个，那就是根管治疗。</li>
<li>“牙医恐惧”，首先一定是疼。除了生理上的疼之外，也有心理上的，心疼钱。</li>
</ol>
</blockquote>
<h1 id="前因"><a href="#前因" class="headerlink" title="前因"></a>前因</h1><p>24年年中就感觉牙齿不适，偶尔吃饭时会掉落牙齿碎片，当时没觉得是自己的牙齿损坏了，而是以为饭里有些骨头啥的东西咬碎了，后来刷牙时发现右边牙齿根部似乎出了个洞，但是也不痒也不疼，加上本人对于医院有点惧怕，于是就没有太过在意。</p>
<p>25年春节情况变得更加严重，由于过年期间大吃大喝，可能刺激到了损坏的牙齿根部，嚼着嚼着一下子咬到硬物时会发生剧烈疼痛，脸都能疼变形的那种，无法忍受的疼，经历了几次疼痛后右侧牙齿几乎无法正常咬碎食物，只能依靠左侧正常牙齿吃饭，但当时想着回苏州可以刷医保，能省不少钱，于是硬生生忍受了下来（我真是神人）。</p>
<p>终于过完了年，来到苏州的第一件事就是挂牙科的号，经历一顿波折才挂上15号周六上午的号。这期间吃饭时是真的折磨，加上去之前12号的时候牙齿好像还发炎了，隐隐作痛，只能吃布洛芬止痛，夜里也无法好好休息，还好去医院的时候已经消炎了。</p>
<h1 id="初诊"><a href="#初诊" class="headerlink" title="初诊"></a>初诊</h1><p>专家号是别想了，打工人想在周末挂主流科室的专家号还是很困难的。在苏州12320上挂的苏州华夏口腔医院-牙体牙髓病二科普通门诊的号，时间为15号周六上午10:00-10:30。</p>
<p>去的时候晚了一点，10:05才在自主取号机取到号，去导引台签到分配医生，由于我第一次来没有熟悉的医生，分配的随机医生，然后找地方坐着等叫号。去的比较晚，没过几分钟就叫到我了，走到门诊室后医生问我有什么问题，我说下右侧的牙疼，医生让我躺在专门的牙科椅子上检查牙齿，上方的灯光还晃了我一下。</p>
<p>短暂的检查过后说，最里面的牙蛀的很严重，已经影响到牙神经了，可能需要做根管治疗，倒数第二颗也有点损坏，需要补一下，开的诊断单写的慢性牙周炎和龋齿(qǔ chǐ)，治疗方案经过我同意后让我去拍个牙片，给我开了个牙片单子去放射区签到排队，拍了片后不用取，直接回牙医处就行，医生在电脑上看过片后说确定要做根管治疗的，问我要不要做，我同意后方案后医生拿出“根管治疗知情同意书”让我签名，签名后就到了最折磨人的治疗阶段了。</p>
<h2 id="治疗"><a href="#治疗" class="headerlink" title="治疗"></a>治疗</h2><p>医生会提前说明如有不适、吐口水（有专门的医疗器具吸）、剧烈疼痛需要举左手示意，且会询问你来之前是否吃过饭，我是提前吃了瓶八宝粥和面包。</p>
<p>根管治疗的时间比较长，所以先做的是补牙，补牙需要将棉花垫在需要补的那颗牙的两侧，用于吸收唾液，防止补牙材料被污染。这个阶段我就出问题了，我的呕吐反应非常严重，舌头不习惯旁边有异物，垫了两次后一直出现干呕无法补牙，于是就让我左侧头控制唾液流向，才可以进行下一步。补牙前需要先把被蛀成黑色的部分磨除，再填充材料，这个过程非常离谱，因为我右侧牙神经已经损坏，有一点轻微的震动就会非常疼，磨得过程得声音也很恐怖，比较反人类。不过过程没什么问题，忍忍就行了。</p>
<p>接下来时间比较长的就是根管治疗了，在需要根管治疗的牙齿旁边围上一块有弹性的手术布，因为我也看不见，就只能感觉到又是磨又是喷一种冰凉的喷剂，根管的过程基本没什么感觉，偶尔有些疼痛是震动带来的，但因为冰凉的关系感觉也不太痛。我一直以为需要打麻醉针，原来不需要。期间没什么问题，但时间很长，直到11点半左右才完成。</p>
<h2 id="价格-术后"><a href="#价格-术后" class="headerlink" title="价格&amp;术后"></a>价格&amp;术后</h2><p>首先就是漱口，漱完一阵清爽，起来后医生说2小时内不要吃东西，补的那颗牙暂时不要用舌头去舔，根管的那颗牙要想完全恢复还需要做个牙冠，右侧牙齿近期不要咀嚼，等25号复诊再看看情况。于是我又提前挂了25号上午10点的号。</p>
<p>本次开销刷的是医保，消费总费用1208.03元，统筹支付186.75元，个人账户支付1021.28元。</p>
<p>目前的感觉非常好，就是吃饭的时候只能用左侧牙吃饭有些不习惯，吃着吃着就不自觉的左侧头。有人术后还有牙疼的情况，我这里几乎感觉不到，等牙冠一做就又能正常吃饭了，就等着复诊了…</p>
<h1 id="复诊"><a href="#复诊" class="headerlink" title="复诊"></a>复诊</h1><p>依旧自行取号，但是这次去导诊台签到的同时需要指定复诊医生，签完到坐等叫号，轮到我时医生让我先去拍之前做根管牙的牙片（是真快啊，大约10秒就出片），拍完后回到医生处就开始做检查根管情况。中途也没什么意外，做完牙冠后又让我去拍个片子，拍完回来后问我是否牙冠过高影响咀嚼，然后开始修正一下牙冠高度，让牙冠和周围牙齿齐平，然后就完事了，不过仍然要在90天后看根管的恢复情况。</p>
<h2 id="价格-术后-1"><a href="#价格-术后-1" class="headerlink" title="价格&amp;术后"></a>价格&amp;术后</h2><p>本次开销刷医保卡，消费总费用815.03，统筹支付312.3元，个人支付502.73元<br><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/021501/2025021501001.png" alt="复诊开销"></p>
<p>这里我注意到有个根管填充术的收费项目，难道复诊时还需要清理根管內部？</p>
<p>2025&#x2F;02&#x2F;27，已经过去两天了，现在能够正常吃饭简直是太爽了~</p>
<h1 id="其他"><a href="#其他" class="headerlink" title="其他"></a>其他</h1><ol>
<li>复诊完成后一直在听医生说的注意事项，忘记让医生写张病假单了，只能用一天年假血亏。</li>
<li>医生说左边的牙齿情况也要注意，但是如果要进一步检查和治疗的话得多等一段时间后。</li>
<li>当天下午预约了 苏州博物馆本馆 ，我以为牙冠做的很快，实际消耗了不少时间，我又没带相机，只能又做地铁回家拿，浪费了不少时间（血亏年假）。</li>
</ol>
<h1 id="参考"><a href="#参考" class="headerlink" title="参考"></a>参考</h1><ol>
<li><a target="_blank" rel="noopener" href="https://www.bilibili.com/video/BV13j411V7J6">内有大矿！超近距离拍龋齿修复，隔着屏幕看痛了？！  – 影视飓风</a> </li>
<li><a target="_blank" rel="noopener" href="https://www.upi.com/Health_News/2009/03/27/Survey-Most-fear-the-dentist-root-canals/85781238210145/">Survey: Most fear the dentist, root canals.   – 牙医恐惧调查报告</a></li>
<li><a target="_blank" rel="noopener" href="https://sspai.com/post/54300">克服恐惧，我终于也成了有假牙的人  – 少数派</a></li>
</ol>
</article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" title="头像" alt="头像"></a><div class="post-copyright__author_name">c2hapmll</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://shijie.icu/post/20250215190000.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl('https://shijie.icu/post/20250215190000.html')">2025年02月15日-记一次去医院看牙的经历</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/wechat.jpg" alt="微信"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/alipay.jpg" alt="支付宝"/></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display: none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://shijie.icu/post/20250215190000.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=2025年02月15日-记一次去医院看牙的经历&amp;url=https://shijie.icu/post/20250215190000.html&amp;pic=/images/posts/2025/021501/top.jpg" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl() {
  var currentPageUrl = window.location.href;
  var input = document.createElement("input");
  input.setAttribute("value", currentPageUrl);
  document.body.appendChild(input);
  input.select();
  input.setSelectionRange(0, 99999);
  document.execCommand("copy");
  document.body.removeChild(input);
}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://shijie.icu" target="_blank">我的博客</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E5%8C%BB%E9%99%A2/"><span class="tags-punctuation"> <i class="anzhiyufont anzhiyu-icon-tag"></i></span>医院<span class="tagsPageCount">1</span></a><a class="post-meta__box__tags" href="/tags/%E7%89%99%E7%A7%91/"><span class="tags-punctuation"> <i class="anzhiyufont anzhiyu-icon-tag"></i></span>牙科<span class="tagsPageCount">1</span></a></div></div></div><div class="post_share"><div class="social-share" data-image="/images/posts/2025/042201/202504220200_top.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media='all'"/><script src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/js/social-share.min.js" defer="defer"></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/post/20241203090000.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024120301/top.webp" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">2024年12月03日-Hexo安知鱼主题魔改记录</div></div></a></div><div class="next-post pull-right"><a href="/post/20250422110000.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/042201/202504220200_top.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">2025年04月22日 - RetroArch模拟器 - 联机篇</div></div></a></div></nav><hr/><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)">匿名评论</a><a href="/privacy" style="margin-left: 4px">隐私政策</a></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div></div></div><div class="comment-barrage"></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="card-content"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div><div class="author-info-avatar"><img class="avatar-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/><div class="author-status"><img class="g-status" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="https://bu.dusays.com/2021/03/03/c50ad1cd452e8.png" alt="status"/></div></div><div class="author-info__description"><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">欢迎访问我的博客，这里是我分享<b style="color:#fff">生活</b>的地方，另外还有一些技能和经验的<b style="color:#fff">记录</b>。</div><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">希望可以帮助到你。</div></div><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">c2hapmll</h1><div class="author-info__desc"></div></a><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://steamcommunity.com/profiles/76561199172600200/" target="_blank" title="Steam"><svg class="icon faa-tada" aria-hidden="true"><use xlink:href="#icon-steam"></use></svg></a></div></div></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bullhorn anzhiyu-shake"></i><span>公告</span></div><div class="announcement_content">刚从Halo迁移至Hexo，还在调整中，欢迎访问~ <br/> 本站开放时间为：<b>8:30-23:00</b>，可能会提前关站哦~ <br/> 历史更新请点击<a href="https://shijie.icu/update/"><b>博客更新日志</b></a> <br/></div></div><div class="card-widget card-countdown"><div class="item-headline"><i></i><span></span></div><div class="item-content"><div class="cd-count-left">
  <span class="cd-text">距离</span>
  <span class="cd-name" id="eventName"></span>
  <span class="cd-time" id="daysUntil"></span>
  <span class="cd-date" id="eventDate"></span>
</div>
<div id="countRight" class="cd-count-right"></div>
</div></div><div class="card-widget music-widget"><div class="item-headline"><i></i><span></span></div><div class="item-content"><div class="music-widget">
  <div id="musicStatus" class="music-status"></div>
  <img id="musicCover" class="music-cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="" alt="专辑封面">
</div>
<div class="music-info">
  <div id="musicTitle" class="music-title"></div>
  <div id="musicArtist" class="music-artist"></div>
  <div id="musicAlbum" class="music-album"></div>
</div>
</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E5%89%8D%E5%9B%A0"><span class="toc-number">1.</span> <span class="toc-text">前因</span></a></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E5%88%9D%E8%AF%8A"><span class="toc-number">2.</span> <span class="toc-text">初诊</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%B2%BB%E7%96%97"><span class="toc-number">2.1.</span> <span class="toc-text">治疗</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%BB%B7%E6%A0%BC-%E6%9C%AF%E5%90%8E"><span class="toc-number">2.2.</span> <span class="toc-text">价格&amp;术后</span></a></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E5%A4%8D%E8%AF%8A"><span class="toc-number">3.</span> <span class="toc-text">复诊</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%BB%B7%E6%A0%BC-%E6%9C%AF%E5%90%8E-1"><span class="toc-number">3.1.</span> <span class="toc-text">价格&amp;术后</span></a></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E5%85%B6%E4%BB%96"><span class="toc-number">4.</span> <span class="toc-text">其他</span></a></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E5%8F%82%E8%80%83"><span class="toc-number">5.</span> <span class="toc-text">参考</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/042201/202504220200_top.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2025年04月22日 - RetroArch模拟器 - 联机篇"/></a><div class="content"><a class="title" href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇">2025年04月22日 - RetroArch模拟器 - 联机篇</a><time datetime="2025-04-22T03:00:00.000Z" title="发表于 2025-04-22 11:00:00">2025-04-22</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20250215190000.html" title="2025年02月15日-记一次去医院看牙的经历"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/021501/top.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2025年02月15日-记一次去医院看牙的经历"/></a><div class="content"><a class="title" href="/post/20250215190000.html" title="2025年02月15日-记一次去医院看牙的经历">2025年02月15日-记一次去医院看牙的经历</a><time datetime="2025-02-15T11:00:00.000Z" title="发表于 2025-02-15 19:00:00">2025-02-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20241203090000.html" title="2024年12月03日-Hexo安知鱼主题魔改记录"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024120301/top.webp" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年12月03日-Hexo安知鱼主题魔改记录"/></a><div class="content"><a class="title" href="/post/20241203090000.html" title="2024年12月03日-Hexo安知鱼主题魔改记录">2024年12月03日-Hexo安知鱼主题魔改记录</a><time datetime="2024-12-03T01:00:00.000Z" title="发表于 2024-12-03 09:00:00">2024-12-03</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024112001/top.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年11月20日-RetroArch全能游戏模拟器"/></a><div class="content"><a class="title" href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器">2024年11月20日-RetroArch全能游戏模拟器</a><time datetime="2024-11-20T01:26:54.000Z" title="发表于 2024-11-20 09:26:54">2024-11-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024101001/top.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年10月10日-流量卡选购指南"/></a><div class="content"><a class="title" href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南">2024年10月10日-流量卡选购指南</a><time datetime="2024-10-10T06:44:21.000Z" title="发表于 2024-10-10 14:44:21">2024-10-10</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">&copy;2024 - 2025 By <a class="footer-bar-link" href="/" title="c2hapmll" target="_blank">c2hapmll</a></div></div><div id="footer-type-tips"></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index" title="皖ICP备20000482号-2">皖ICP备20000482号-2</a><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.mps.gov.cn/#/query/webSearch" title="皖公网安备 34120402000423号">皖公网安备 34120402000423号</a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">6</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">9</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">3</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" target="_blank" rel="noopener" href="https://home.shijie.icu/" title="我的主页"><img class="back-menu-item-icon" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/32.png" alt="我的主页"/><span class="back-menu-item-text">我的主页</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 0.88rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 0.88rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 0.88rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 0.88rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 0.88rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 0.88rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 0.88rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 0.88rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 0.88rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div id="rightside"><div id="rightside-config-hide"><button id="readmode" type="button" title="阅读模式"><i class="anzhiyufont anzhiyu-icon-book-open"></i></button><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><a id="to_comment" href="#post-comment" title="直达评论"><i class="anzhiyufont anzhiyu-icon-comments"></i></a><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><span id="loading-status"></span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="is-center" id="loading-database"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-pulse-icon"></i><span>  数据库加载中</span></div><div class="search-wrap"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div><hr/><div id="local-search-results"></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size: 1rem;"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://y.qq.com/n/ryqq/playlist/8802438608&quot;, &quot;_blank&quot;);" style="display: none;"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://cdn.cbd.int/instant.page@5.2.0/instantpage.js" type="module"></script><script src="https://cdn.cbd.int/vanilla-lazyload@17.8.5/dist/lazyload.iife.min.js"></script><script src="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.js"></script><canvas id="universe"></canvas><script async src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script async src="/anzhiyu/random.js"></script><script src="/js/search/local-search.js"></script><div class="js-pjax"><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
        region: '',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script>var visitorMail = "";
</script><script async data-pjax src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script async src="https://at.alicdn.com/t/c/font_4729035_3m5int5al8c.js"></script><script src="/js/countdown.js"></script><script src="/js/getnowplaying.js"></script><script src="/js/badge.js"></script><script src="/js/update.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/aplayer/APlayer.min.css" media="print" onload="this.media='all'"><script src="https://cdn.cbd.int/anzhiyu-blog-static@1.0.1/js/APlayer.min.js"></script><script src="https://cdn.cbd.int/hexo-anzhiyu-music@1.0.1/assets/js/Meting2.min.js"></script><script src="https://cdn.cbd.int/pjax@0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: false,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script><script charset="UTF-8" src="https://cdn.cbd.int/anzhiyu-theme-static@1.1.5/accesskey/accesskey.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div></body></html>
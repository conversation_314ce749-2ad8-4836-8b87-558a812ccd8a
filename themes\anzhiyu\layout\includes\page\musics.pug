#musics
  if site.data.musics
    each i in site.data.musics
      .author-content.author-content-item.musicsPage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          .banner-button-group
            a.banner-button(href=i.buttonLink)
              i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
              span.banner-button-text=i.buttonText
      each item in i.good_musics
        .goodmusics-item
          h2.goodmusics-title= item.title
          .goodmusics-item-description= item.description
          .musics-item
            .musics-item-content
              each iten, indey in item.musics_list
                .musics-item-content-item
                  .musics-item-content-item-cover
                      a(href=iten.link target="_blank" class="music-link")
                        img.musics-item-content-item-image(
                          data-lazy-src=url_for(iten.image)
                          onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'`
                          alt=iten.name
                          class="music-image"
                        )
                  .musics-item-content-item-info
                    .musics-item-content-item-name(onclick=`rm.rightmenuCopyText("${iten.name}");anzhiyu.snackbarShow("${_p('已复制音乐名：') + " 【" + iten.name + "】"}");` title=iten.name)= iten.name
                    .musics-item-content-item-specification= iten.specification
                    .musics-item-content-item-description= iten.description
                    //- .musics-item-content-item-toolbar
                    //-   if iten.link.includes('https://') || iten.link.includes('http://')
                    //-     a.musics-item-content-item-link(href= iten.link, target='_blank') 详情
                    //-     .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                    //-       i.anzhiyufont.anzhiyu-icon-message
                    //-   else  
                    //-     a.musics-item-content-item-link(href= iten.link, target='_blank') 查看文章
                    //-     .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                    //-       i.anzhiyufont.anzhiyu-icon-message
---
title: 2025年04月18日 - RetroArch模拟器 - 街机篇
date: 2025-04-18 11:00:00
updated: 2025-04-18 15:00:00
categories:
  - 游戏
tags:
  - 日常
  - 指南
  - 游戏
  - 模拟器
cover: /images/posts/2024112001/top.png
---

街机游戏的设置比较复杂，我们先认识一些前提条件，然后再来设置RetroArch模拟器。

# 1. 核心选择

RetroArch 模拟器支持 MAME 系列街机核心和 FinalBurn 系列街机核心，这里我推荐下载 FinalBurn Neo 核心，理论上来说所有核心的区别不大，各种配置文件的存放方式也是一致的，下文都以 FinalBurn Neo 核心为例。

核心下载：菜单 -> 在线更新 -> 核心下载 -> Arcade (FinalBurn Neo) ，回车下载

问题：如果点击 **在线更新** 时无法加载核心列表，就是你的网络问题。

# 2. 街机ROM文件

街机游戏的rom是以.zip为结尾的，以"恐龙快打"为例，"恐龙快打"的rom对应的文件名为**dino.zip**，那么这个文件名就不要去动它，更不要解压，否则会无法正确运行。你可以打开这个压缩包查看其中的内容，简单看下里面的文件，防止后面有些报错信息你不认识。

## 2.1 Full Non-Merged 和 Merged

街机游戏的运行大都需要bios文件，也就是驱动，没有bios文件，游戏是无法正确运行的。那就有人问了，我下载的dino.zip直接导入到RetroArch，使用fbneo核心（街机核心）就可以直接运行游玩了，也没什么问题啊。其实不然，能够直接运行说明你下载的 dino.zip 是 **Full Non-Merged** 版本，什么意思呢？就是你下载的rom文件 dino.zip 中包含了支持游戏运行的bios文件，无需额外加载bios即可运行。

否则运行时会出现提示：缺少xxx.pl、缺少spxx.sp1 等等错误代码，错误代码中的文件就是bios文件中包含的内容，如果你不太理解也没关系，你完全不需要关注bios中有哪些东西，只需认识到 这些bios文件是被一些街机rom所依赖的，没有这些依赖文件，游戏有可能不能正常运行。一般来说，bios文件也是以.zip为结尾的压缩文件，有些合集会把bios文件放在rom同级目录下（感觉很不好，容易误会文件作用），比如 neogeo.zip 是一些街机游戏的 bios 文件，不要将其认为是某个游戏rom本体。

理所当然，由于包含 bios 文件，这样下载的 dino.zip 文件大小就会大上不少，确实很省事。但麻烦的是，很多街机合集都是 **Merged** 版本，也就是不完全版本，仍需要另外加载bios文件才能正常运行。很多时候你只有一个dino.zip文件，你也不知道自己下载的是不是完整的rom怎么办？很简单，直接加载rom运行看看报不报错就行(当你准备制作自己的rom库时，一定要提前验证)。RetroArch 模拟器提供了部分 bios 文件的在线更新能力，虽然还是缺了很多文件。你完全可以自行在网络上搜索较新的bios文件下载并载入。

## 2.2 父 rom 和 子 rom

除了上面提到的完全版本和不完全版本，另外还有一种情况，有些街机rom文件是需要依赖父rom文件运行的，它本身不包含能够完整支持它运行的游戏文件（并非bios），举个例子，"西游释厄传 特别版 版本100 第1套" 对应的文件名为 olds100.zip，这个rom文件如果是 Merged (不完全)版本，就依赖于父 ROM 西游释厄传 olds.zip 运行，你需要将这两个zip放在同一目录下才能正常运行特别版游戏。再再进一步，olds100.zip 其实还需要 bios 文件 pgm.zip 的支持，你还需要把 pgm.zip 放在模拟器的正确的 bios 目录下才能正常运行特别版游戏。

总而言之，如果你想省空间，那么 Merged 版本很适合你，如果你想简单便捷，那么一定要选择 Full Non-Merged 版本。然而从实际出发来说，你很难找到所有游戏的 Full Non-Merged 版本，所以该准备bios文件还是要准备的。

## 2.3 原版rom 和 非原版rom（汉化版、修改版）

待撰写。。。

# 3. 游戏库扫描

# 4. 街机rom封面、缩略图

谁不希望自己的rom有漂亮的封面呢？RetroArch提供了在线更新缩略图的功能，就在 主菜单 -> 在线更新 -> 列表缩略图更新，或者可以单独选择已经扫描完成的游戏，选中后选择 缩略图更新，如果能够检索到缩略图，下载完毕后就能展示出来。
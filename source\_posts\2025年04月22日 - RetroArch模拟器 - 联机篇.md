---
title: 2025年04月22日 - RetroArch模拟器 - 联机篇
date: 2025-04-22 11:00:00
updated: 2025-04-21 15:00:00
categories:
  - 游戏
tags:
  - 日常
  - 指南
  - 游戏
  - 模拟器
cover: /images/posts/2025/042201/202504220200_top.png
---

RetroArch 对于联机的要求比较严格，需要RetroArch 版本一致、核心版本一致、游戏rom一致才能进行联机，此为前提条件需要注意。

# 1. 设置用户名

为防止意外和找不到房间的情况发生，Retroarch需要设置一个用户名用于辨认，房主和玩家尽量都设置，步骤如下

1. 主菜单 -> 设置 -> 用户 -> 用户名，设置为你想要的名称即可

# 2. 局域网联机

过于简单，不再详解。

# 3. 公网联机

## 3.1 UPnP和端口转发（内网穿透）

RetroArch 在不设置代理服务器的情况下默认使用此种方式进行公网联机。

原理是房主主机作为服务端，进行内网穿透映射到外网才可以正常联机，如果你不理解该节的一些名词，请勿参照该节内容，防止路由器暴露公网端口。

### 3.1.1 路由器

进入路由器管理页面，找到**UPnP功能**并开启，步骤如下

1. 访问192.168.1.1（小米默认是192.168.31.1）进入路由管理页面，开启UPnP功能。

RetroArch 启动后会自动增加UPnP记录，如果没有自动记录请使用**端口转发服务**

1. 设置端口转发，TCP，主机端局域网地址，端口为55435


### 3.1.2 主机端设置（房主）

在联机菜单下选择主机并运行游戏，步骤如下：

1. 主菜单 -> 联机 -> 主机 -> 作为游戏主机

如若一切就绪，回车后将在左下角提示"联机游戏将在内容加载后开始"，此时选择核心、选择游戏启动，会在左下角出现提示"您已作为玩家1进入"、"正在等待客户端连接"，此时客户端（其他玩家）连进主机即可正常联机游玩游戏。

如果弹出的不是以上提示，或者为类似下面的提示时将无法进行联机操作，请使用其他方式进行联机。

1. **您的网络有前置，推荐使用中继服务**
2. **您的网络无法在公网访问**
3. **联机服务开启失败**

### 3.1.3 客户端设置（其他玩家）

无需任何设置，只要你能正常联网即可，具体步骤如下

1. 主菜单 -> 联机 -> 刷新联机主机列表 -> 往下翻找到对应的房间名称回车即可，名称为主机端的用户名。

如果你没有玩过房主开的房间选择的游戏，那么会提示"未找到匹配rom，将在你启动对应rom时进行联机"，此时选择相同的核心、相同的游戏启动即可加入联机房间。

## 3.2 公网直连

我没有ipv4公网，无法尝试，如果有的话理论上和局域网联机一样的方式，只需路由器开放55435端口进出即可。

## 3.3 官方联机服务器（中继）

不推荐，官方的git仓库中pr的只有国外的服务器，我尝试了所有的官方服务器，要么连接失败，要么延迟逆天。

## 3.4 自建联机服务器（中继）

需要自行准备公网服务器（具备ipv4公网地址，目前没有试验ipv6的情况），如果没有请参照其他节进行联机。

### 3.4.1 配置联机服务

官方服务器源码地址：https://github.com/libretro/netplay-tunnel-server

下载其中内容上传至服务器 或 在服务器安装git直接clone上面的地址。

环境需求非常简单，需要python3.7版本及以上，安装过程自行百度不再赘述。

运行也非常简单，readme.md中直接使用下面的命令即可
```python
python3 -OO retroarch_tunnel_server.py
或 自定义路径的配置文件
python3 -OO retroarch_tunnel_server.py /path/retroarch_tunnel_server.ini
```

python执行成功返回如下图所示：

![Python运行脚本](/images/posts/2025/042201/202504220201.png)

防火墙放行端口 55435，如果没有启用防火墙就不需要此步骤，如果是iptables自行搜索端口放行命令（我忘了，懒得搜）
```shell
# Centos firewalld
sudo firewall-cmd --permanent --add-port=55435/tcp
sudo firewall-cmd --reload

# Ubuntu ufw
sudo ufw allow 55435/tcp
```

别忘记放行服务商的防火墙，如果你是国内服务器，例如阿里云的服务器，还需要登录网页控制台，

1. 控制台 -> 服务器 -> 防火墙 -> 放行55435端口

两套防火墙都要放行，一套是服务器本身的防火墙，一套是服务商提供的防火墙

### 3.4.2 主机端设置（房主）

需要启用**代理服务器功能**，并设置为自定义和填写服务器的公网地址，步骤如下

1. 主菜单 -> 联机 -> 网络 -> 启用代理服务器
2. 设置**代理服务器位置**，选择最下面的 "自定义"
3. 设置**自定义代理服务器地址**，填写你的服务器地址即可
4. （可选）设置**服务器密码**，可以避免无关人员加入捣乱

随后在联机菜单下选择主机并运行游戏，步骤如下：

1. 主菜单 -> 联机 -> 主机 -> 作为游戏主机

如若一切就绪，回车后将在左下角提示"联机游戏将在内容加载后开始"，此时选择核心、选择游戏启动，会在左下角出现提示"您已作为玩家1进入"、"正在等待客户端连接"，此时客户端（其他玩家）连进主机即可正常联机游玩游戏。

如果左下角提示其他内容，请参照本文目录QA节中的内容进行排查。

### 3.4.3 客户端设置（其他玩家）

无需任何设置，只要你能正常联网即可，具体步骤如下

1. 主菜单 -> 联机 -> 刷新联机主机列表 -> 往下翻找到对应的房间名称回车即可，名称为主机端的用户名。

如果你没有玩过房主开的房间选择的游戏，那么会提示"未找到匹配rom，将在你启动对应rom时进行联机"，此时选择相同的核心、相同的游戏启动即可加入联机房间。

## 3.5 软件异地组网联机（异地组网）

很多软件提供多台外网机器组局域网的能力，这样就可以参照**局域网联机**一节进行局域网联机操作。但比较麻烦的是，和你玩的朋友可能不愿意下载一些软件，尤其当这些软件设置起来还需要一定的网络知识的情况。

因为几乎所有的异地组网软件都是通过内网穿透打洞的方式进行的，网络质量会非常影响打洞的成功率，而且延迟问题也不能保证。由于软件众多不再进行详解，异地组网方式请自行搜索皎月连、贝瑞蒲公英、zerotier、Netbird等异地组网教程。

当所有机器连接到同一网段时就可以使用局域网联机的方式进行游玩了。

# 4. 问题排查

1. 联机支持哪些游戏？哪些核心?
不清楚，我目前只使用了Fbneo核心进行街机游戏的联机功能，具体的支持范围你可以参考官方教程或各核心的github库查询。

2. 能够实现哪些功能？
宝可梦交换、通信进化，同网络不同屏幕联机游戏，不同网络不同屏幕联机游戏。

3. 延迟问题？
自建服务器取决于你的服务器，国内的服务器大约在30ms-150ms波动，对于一些对战类比较勉强，对于闯关类还不错。
官方服务器本身就没有国内cn的服务器，延迟大很正常
理论上最好的为直连，少了一个中继服务器的延迟，但直连要公网地址，目前运营商都不给了。

4. 可以直接使用**连接到联机主机**输入ip地址吗？
可以的，但只有局域网和直连可用，中继的话填写不了主机ip（存疑，70%把握）

5. 直接使用**连接到联机主机**功能输入服务器地址提示"服务器版本较低"怎么办？
请注意，**连接到联机主机**功能输入的是主机（房主）机器的IP地址，不是你搭建的服务器地址。
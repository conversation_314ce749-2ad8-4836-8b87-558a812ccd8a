<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"><title>2025年04月22日 - RetroArch模拟器 - 联机篇 | 我的博客</title><meta name="keywords" content="日常,指南,游戏,模拟器"><meta name="author" content="c2hapmll"><meta name="copyright" content="c2hapmll"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f7f9fe"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="2025年04月22日 - RetroArch模拟器 - 联机篇"><meta name="application-name" content="2025年04月22日 - RetroArch模拟器 - 联机篇"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f7f9fe"><meta property="og:type" content="article"><meta property="og:title" content="2025年04月22日 - RetroArch模拟器 - 联机篇"><meta property="og:url" content="https://shijie.icu/post/20250422110000.html"><meta property="og:site_name" content="我的博客"><meta property="og:description" content="RetroArch 对于联机的要求比较严格，需要RetroArch 版本一致、核心版本一致、游戏rom一致才能进行联机，此为前提条件需要注意。 1. 设置用户名为防止意外和找不到房间的情况发生，Retroarch需要设置一个用户名用于辨认，房主和玩家尽量都设置，步骤如下  主菜单 -&amp;gt; 设置"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://shijie.icu/images/posts/2025/042201/202504220200_top.png"><meta property="article:author" content="c2hapmll"><meta property="article:tag"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://shijie.icu/images/posts/2025/042201/202504220200_top.png"><meta name="description" content="RetroArch 对于联机的要求比较严格，需要RetroArch 版本一致、核心版本一致、游戏rom一致才能进行联机，此为前提条件需要注意。 1. 设置用户名为防止意外和找不到房间的情况发生，Retroarch需要设置一个用户名用于辨认，房主和玩家尽量都设置，步骤如下  主菜单 -&amp;gt; 设置"><link rel="shortcut icon" href="/images/themes/32.png"><link rel="canonical" href="https://shijie.icu/post/20250422110000.html"><link rel="preconnect" href="//cdn.cbd.int"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><meta name="google-site-verification" content="xxx"/><meta name="baidu-site-verification" content="code-xxx"/><meta name="msvalidate.01" content="xxx"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.cbd.int/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media='all'"><script>const GLOBAL_CONFIG = {
  linkPageTop: undefined,
  peoplecanvas: undefined,
  postHeadAiDescription: undefined,
  diytitle: undefined,
  LA51: undefined,
  greetingBox: undefined,
  twikooEnvId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
  commentBarrageConfig:undefined,
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: false,
  mainTone: undefined,
  authorStatus: {"skills":["🤖️ 数码科技爱好者","🔍 分享与热心帮助","🏠 智能家居小能手"]},
  algolia: undefined,
  localSearch: {"path":"/search.xml","preload":true,"languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: undefined,
  highlight: {"plugin":"prismjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    simplehomepage: true,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: c2hapmll","link":"链接: ","source":"来源: 我的博客","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: false,
  shortcutKey: undefined,
  autoDarkmode: true
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  configTitle: '我的博客',
  title: '2025年04月22日 - RetroArch模拟器 - 联机篇',
  postAI: '',
  pageFillDescription: '1. 设置用户名, 2. 局域网联机, 3. 公网联机, 3.1 UPnP和端口转发（内网穿透）, 3.1.1 路由器, 3.1.2 主机端设置（房主）, 3.1.3 客户端设置（其他玩家）, 3.2 公网直连, 3.3 官方联机服务器（中继）, 3.4 自建联机服务器（中继）, 3.4.1 配置联机服务, 3.4.2 主机端设置（房主）, 3.4.3 客户端设置（其他玩家）, 3.5 软件异地组网联机（异地组网）, 4. 问题排查对于联机的要求比较严格需要版本一致核心版本一致游戏一致才能进行联机此为前提条件需要注意设置用户名为防止意外和找不到房间的情况发生需要设置一个用户名用于辨认房主和玩家尽量都设置步骤如下主菜单设置用户用户名设置为你想要的名称即可局域网联机过于简单不再详解公网联机和端口转发内网穿透在不设置代理服务器的情况下默认使用此种方式进行公网联机原理是房主主机作为服务端进行内网穿透映射到外网才可以正常联机如果你不理解该节的一些名词请勿参照该节内容防止路由器暴露公网端口路由器进入路由器管理页面找到功能并开启步骤如下访问小米默认是进入路由管理页面开启功能启动后会自动增加记录如果没有自动记录请使用端口转发服务设置端口转发主机端局域网地址端口为主机端设置房主在联机菜单下选择主机并运行游戏步骤如下主菜单联机主机作为游戏主机如若一切就绪回车后将在左下角提示联机游戏将在内容加载后开始此时选择核心选择游戏启动会在左下角出现提示您已作为玩家进入正在等待客户端连接此时客户端其他玩家连进主机即可正常联机游玩游戏如果弹出的不是以上提示或者为类似下面的提示时将无法进行联机操作请使用其他方式进行联机您的网络有前置推荐使用中继服务您的网络无法在公网访问联机服务开启失败客户端设置其他玩家无需任何设置只要你能正常联网即可具体步骤如下主菜单联机刷新联机主机列表往下翻找到对应的房间名称回车即可名称为主机端的用户名如果你没有玩过房主开的房间选择的游戏那么会提示未找到匹配将在你启动对应时进行联机此时选择相同的核心相同的游戏启动即可加入联机房间公网直连我没有公网无法尝试如果有的话理论上和局域网联机一样的方式只需路由器开放端口进出即可官方联机服务器中继不推荐官方的仓库中的只有国外的服务器我尝试了所有的官方服务器要么连接失败要么延迟逆天自建联机服务器中继需要自行准备公网服务器具备公网地址目前没有试验的情况如果没有请参照其他节进行联机配置联机服务官方服务器源码地址下载其中内容上传至服务器或在服务器安装直接上面的地址环境需求非常简单需要版本及以上安装过程自行百度不再赘述运行也非常简单中直接使用下面的命令即可或自定义路径的配置文件执行成功返回如下图所示防火墙放行端口如果没有启用防火墙就不需要此步骤如果是自行搜索端口放行命令我忘了懒得搜别忘记放行服务商的防火墙如果你是国内服务器例如阿里云的服务器还需要登录网页控制台控制台服务器防火墙放行端口两套防火墙都要放行一套是服务器本身的防火墙一套是服务商提供的防火墙主机端设置房主需要启用代理服务器功能并设置为自定义和填写服务器的公网地址步骤如下主菜单联机网络启用代理服务器设置代理服务器位置选择最下面的自定义设置自定义代理服务器地址填写你的服务器地址即可可选设置服务器密码可以避免无关人员加入捣乱随后在联机菜单下选择主机并运行游戏步骤如下主菜单联机主机作为游戏主机如若一切就绪回车后将在左下角提示联机游戏将在内容加载后开始此时选择核心选择游戏启动会在左下角出现提示您已作为玩家进入正在等待客户端连接此时客户端其他玩家连进主机即可正常联机游玩游戏如果左下角提示其他内容请参照本文目录节中的内容进行排查客户端设置其他玩家无需任何设置只要你能正常联网即可具体步骤如下主菜单联机刷新联机主机列表往下翻找到对应的房间名称回车即可名称为主机端的用户名如果你没有玩过房主开的房间选择的游戏那么会提示未找到匹配将在你启动对应时进行联机此时选择相同的核心相同的游戏启动即可加入联机房间软件异地组网联机异地组网很多软件提供多台外网机器组局域网的能力这样就可以参照局域网联机一节进行局域网联机操作但比较麻烦的是和你玩的朋友可能不愿意下载一些软件尤其当这些软件设置起来还需要一定的网络知识的情况因为几乎所有的异地组网软件都是通过内网穿透打洞的方式进行的网络质量会非常影响打洞的成功率而且延迟问题也不能保证由于软件众多不再进行详解异地组网方式请自行搜索皎月连贝瑞蒲公英等异地组网教程当所有机器连接到同一网段时就可以使用局域网联机的方式进行游玩了问题排查联机支持哪些游戏哪些核心不清楚我目前只使用了核心进行街机游戏的联机功能具体的支持范围你可以参考官方教程或各核心的库查询能够实现哪些功能宝可梦交换通信进化同网络不同屏幕联机游戏不同网络不同屏幕联机游戏延迟问题自建服务器取决于你的服务器国内的服务器大约在波动对于一些对战类比较勉强对于闯关类还不错官方服务器本身就没有国内的服务器延迟大很正常理论上最好的为直连少了一个中继服务器的延迟但直连要公网地址目前运营商都不给了可以直接使用连接到联机主机输入地址吗可以的但只有局域网和直连可用中继的话填写不了主机存疑把握直接使用连接到联机主机功能输入服务器地址提示服务器版本较低怎么办请注意连接到联机主机功能输入的是主机房主机器的地址不是你搭建的服务器地址',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2025-04-21 15:00:00',
  postMainColor: '',
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#18171d')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f7f9fe')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/costom_widget.css"><link rel="stylesheet" href="/css/badge.css"><meta name="generator" content="Hexo 7.3.0"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="/images/themes/32.png"/><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"/><script async="async" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><a id="site-name" href="/" accesskey="h"><div class="title">我的博客</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"/><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole();"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" alt="微信" src="/images/themes/reward/wechat.jpg"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="/images/themes/reward/alipay.jpg"/></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title"> 最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 1.05rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 1.05rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 1.05rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 1.05rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 1.05rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 1.05rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 1.05rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 1.05rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 1.05rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/02/"><span class="card-archive-list-date">二月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/12/"><span class="card-archive-list-date">十二月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/11/"><span class="card-archive-list-date">十一月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/10/"><span class="card-archive-list-date">十月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/09/"><span class="card-archive-list-date">九月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li></ul></div><hr/></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%B8%B8%E6%88%8F/" itemprop="url">游戏</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E6%97%A5%E5%B8%B8/" tabindex="-1" itemprop="url"> <span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>日常</span></a><a class="article-meta__tags" href="/tags/%E6%8C%87%E5%8D%97/" tabindex="-1" itemprop="url"> <span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>指南</span></a><a class="article-meta__tags" href="/tags/%E6%B8%B8%E6%88%8F/" tabindex="-1" itemprop="url"> <span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>游戏</span></a><a class="article-meta__tags" href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" tabindex="-1" itemprop="url"> <span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>模拟器</span></a></span></div></div><h1 class="post-title" itemprop="name headline">2025年04月22日 - RetroArch模拟器 - 联机篇</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-22T03:00:00.000Z" title="发表于 2025-04-22 11:00:00">2025-04-22</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-04-21T07:00:00.000Z" title="更新于 2025-04-21 15:00:00">2025-04-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">2.1k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>6分钟</span></span><span class="post-meta-separator"></span><span class="post-meta-pv-cv" id="" data-flag-title="2025年04月22日 - RetroArch模拟器 - 联机篇"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="busuanzi_value_page_pv"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator">       </span><span class="post-meta-position" title="作者IP属地为苏州"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>苏州</span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="/images/posts/2025/042201/202504220200_top.png"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container" itemscope itemtype="https://shijie.icu/post/20250422110000.html"><header><a class="post-meta-categories" href="/categories/%E6%B8%B8%E6%88%8F/" itemprop="url">游戏</a><a href="/tags/%E6%97%A5%E5%B8%B8/" tabindex="-1" itemprop="url">日常</a><a href="/tags/%E6%8C%87%E5%8D%97/" tabindex="-1" itemprop="url">指南</a><a href="/tags/%E6%B8%B8%E6%88%8F/" tabindex="-1" itemprop="url">游戏</a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" tabindex="-1" itemprop="url">模拟器</a><h1 id="CrawlerTitle" itemprop="name headline">2025年04月22日 - RetroArch模拟器 - 联机篇</h1><span itemprop="author" itemscope itemtype="http://schema.org/Person">c2hapmll</span><time itemprop="dateCreated datePublished" datetime="2025-04-22T03:00:00.000Z" title="发表于 2025-04-22 11:00:00">2025-04-22</time><time itemprop="dateCreated datePublished" datetime="2025-04-21T07:00:00.000Z" title="更新于 2025-04-21 15:00:00">2025-04-21</time></header><p>RetroArch 对于联机的要求比较严格，需要RetroArch 版本一致、核心版本一致、游戏rom一致才能进行联机，此为前提条件需要注意。</p>
<h1 id="1-设置用户名"><a href="#1-设置用户名" class="headerlink" title="1. 设置用户名"></a>1. 设置用户名</h1><p>为防止意外和找不到房间的情况发生，Retroarch需要设置一个用户名用于辨认，房主和玩家尽量都设置，步骤如下</p>
<ol>
<li>主菜单 -&gt; 设置 -&gt; 用户 -&gt; 用户名，设置为你想要的名称即可</li>
</ol>
<h1 id="2-局域网联机"><a href="#2-局域网联机" class="headerlink" title="2. 局域网联机"></a>2. 局域网联机</h1><p>过于简单，不再详解。</p>
<h1 id="3-公网联机"><a href="#3-公网联机" class="headerlink" title="3. 公网联机"></a>3. 公网联机</h1><h2 id="3-1-UPnP和端口转发（内网穿透）"><a href="#3-1-UPnP和端口转发（内网穿透）" class="headerlink" title="3.1 UPnP和端口转发（内网穿透）"></a>3.1 UPnP和端口转发（内网穿透）</h2><p>RetroArch 在不设置代理服务器的情况下默认使用此种方式进行公网联机。</p>
<p>原理是房主主机作为服务端，进行内网穿透映射到外网才可以正常联机，如果你不理解该节的一些名词，请勿参照该节内容，防止路由器暴露公网端口。</p>
<h3 id="3-1-1-路由器"><a href="#3-1-1-路由器" class="headerlink" title="3.1.1 路由器"></a>3.1.1 路由器</h3><p>进入路由器管理页面，找到<strong>UPnP功能</strong>并开启，步骤如下</p>
<ol>
<li>访问192.168.1.1（小米默认是192.168.31.1）进入路由管理页面，开启UPnP功能。</li>
</ol>
<p>RetroArch 启动后会自动增加UPnP记录，如果没有自动记录请使用<strong>端口转发服务</strong></p>
<ol>
<li>设置端口转发，TCP，主机端局域网地址，端口为55435</li>
</ol>
<h3 id="3-1-2-主机端设置（房主）"><a href="#3-1-2-主机端设置（房主）" class="headerlink" title="3.1.2 主机端设置（房主）"></a>3.1.2 主机端设置（房主）</h3><p>在联机菜单下选择主机并运行游戏，步骤如下：</p>
<ol>
<li>主菜单 -&gt; 联机 -&gt; 主机 -&gt; 作为游戏主机</li>
</ol>
<p>如若一切就绪，回车后将在左下角提示”联机游戏将在内容加载后开始”，此时选择核心、选择游戏启动，会在左下角出现提示”您已作为玩家1进入”、”正在等待客户端连接”，此时客户端（其他玩家）连进主机即可正常联机游玩游戏。</p>
<p>如果弹出的不是以上提示，或者为类似下面的提示时将无法进行联机操作，请使用其他方式进行联机。</p>
<ol>
<li><strong>您的网络有前置，推荐使用中继服务</strong></li>
<li><strong>您的网络无法在公网访问</strong></li>
<li><strong>联机服务开启失败</strong></li>
</ol>
<h3 id="3-1-3-客户端设置（其他玩家）"><a href="#3-1-3-客户端设置（其他玩家）" class="headerlink" title="3.1.3 客户端设置（其他玩家）"></a>3.1.3 客户端设置（其他玩家）</h3><p>无需任何设置，只要你能正常联网即可，具体步骤如下</p>
<ol>
<li>主菜单 -&gt; 联机 -&gt; 刷新联机主机列表 -&gt; 往下翻找到对应的房间名称回车即可，名称为主机端的用户名。</li>
</ol>
<p>如果你没有玩过房主开的房间选择的游戏，那么会提示”未找到匹配rom，将在你启动对应rom时进行联机”，此时选择相同的核心、相同的游戏启动即可加入联机房间。</p>
<h2 id="3-2-公网直连"><a href="#3-2-公网直连" class="headerlink" title="3.2 公网直连"></a>3.2 公网直连</h2><p>我没有ipv4公网，无法尝试，如果有的话理论上和局域网联机一样的方式，只需路由器开放55435端口进出即可。</p>
<h2 id="3-3-官方联机服务器（中继）"><a href="#3-3-官方联机服务器（中继）" class="headerlink" title="3.3 官方联机服务器（中继）"></a>3.3 官方联机服务器（中继）</h2><p>不推荐，官方的git仓库中pr的只有国外的服务器，我尝试了所有的官方服务器，要么连接失败，要么延迟逆天。</p>
<h2 id="3-4-自建联机服务器（中继）"><a href="#3-4-自建联机服务器（中继）" class="headerlink" title="3.4 自建联机服务器（中继）"></a>3.4 自建联机服务器（中继）</h2><p>需要自行准备公网服务器（具备ipv4公网地址，目前没有试验ipv6的情况），如果没有请参照其他节进行联机。</p>
<h3 id="3-4-1-配置联机服务"><a href="#3-4-1-配置联机服务" class="headerlink" title="3.4.1 配置联机服务"></a>3.4.1 配置联机服务</h3><p>官方服务器源码地址：<a target="_blank" rel="noopener" href="https://github.com/libretro/netplay-tunnel-server">https://github.com/libretro/netplay-tunnel-server</a></p>
<p>下载其中内容上传至服务器 或 在服务器安装git直接clone上面的地址。</p>
<p>环境需求非常简单，需要python3.7版本及以上，安装过程自行百度不再赘述。</p>
<p>运行也非常简单，readme.md中直接使用下面的命令即可</p>
<pre class="line-numbers language-python" data-language="python"><code class="language-python">python3 <span class="token operator">-</span>OO retroarch_tunnel_server<span class="token punctuation">.</span>py
或 自定义路径的配置文件
python3 <span class="token operator">-</span>OO retroarch_tunnel_server<span class="token punctuation">.</span>py <span class="token operator">/</span>path<span class="token operator">/</span>retroarch_tunnel_server<span class="token punctuation">.</span>ini<span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span></span></code></pre>

<p>python执行成功返回如下图所示：</p>
<p><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/042201/202504220201.png" alt="Python运行脚本"></p>
<p>防火墙放行端口 55435，如果没有启用防火墙就不需要此步骤，如果是iptables自行搜索端口放行命令（我忘了，懒得搜）</p>
<pre class="line-numbers language-bash" data-language="bash"><code class="language-bash"><span class="token comment"># Centos firewalld</span>
<span class="token function">sudo</span> firewall-cmd <span class="token parameter variable">--permanent</span> --add-port<span class="token operator">=</span><span class="token number">55435</span>/tcp
<span class="token function">sudo</span> firewall-cmd <span class="token parameter variable">--reload</span>

<span class="token comment"># Ubuntu ufw</span>
<span class="token function">sudo</span> ufw allow <span class="token number">55435</span>/tcp<span aria-hidden="true" class="line-numbers-rows"><span></span><span></span><span></span><span></span><span></span><span></span></span></code></pre>

<p>别忘记放行服务商的防火墙，如果你是国内服务器，例如阿里云的服务器，还需要登录网页控制台，</p>
<ol>
<li>控制台 -&gt; 服务器 -&gt; 防火墙 -&gt; 放行55435端口</li>
</ol>
<p>两套防火墙都要放行，一套是服务器本身的防火墙，一套是服务商提供的防火墙</p>
<h3 id="3-4-2-主机端设置（房主）"><a href="#3-4-2-主机端设置（房主）" class="headerlink" title="3.4.2 主机端设置（房主）"></a>3.4.2 主机端设置（房主）</h3><p>需要启用<strong>代理服务器功能</strong>，并设置为自定义和填写服务器的公网地址，步骤如下</p>
<ol>
<li>主菜单 -&gt; 联机 -&gt; 网络 -&gt; 启用代理服务器</li>
<li>设置<strong>代理服务器位置</strong>，选择最下面的 “自定义”</li>
<li>设置<strong>自定义代理服务器地址</strong>，填写你的服务器地址即可</li>
<li>（可选）设置<strong>服务器密码</strong>，可以避免无关人员加入捣乱</li>
</ol>
<p>随后在联机菜单下选择主机并运行游戏，步骤如下：</p>
<ol>
<li>主菜单 -&gt; 联机 -&gt; 主机 -&gt; 作为游戏主机</li>
</ol>
<p>如若一切就绪，回车后将在左下角提示”联机游戏将在内容加载后开始”，此时选择核心、选择游戏启动，会在左下角出现提示”您已作为玩家1进入”、”正在等待客户端连接”，此时客户端（其他玩家）连进主机即可正常联机游玩游戏。</p>
<p>如果左下角提示其他内容，请参照本文目录QA节中的内容进行排查。</p>
<h3 id="3-4-3-客户端设置（其他玩家）"><a href="#3-4-3-客户端设置（其他玩家）" class="headerlink" title="3.4.3 客户端设置（其他玩家）"></a>3.4.3 客户端设置（其他玩家）</h3><p>无需任何设置，只要你能正常联网即可，具体步骤如下</p>
<ol>
<li>主菜单 -&gt; 联机 -&gt; 刷新联机主机列表 -&gt; 往下翻找到对应的房间名称回车即可，名称为主机端的用户名。</li>
</ol>
<p>如果你没有玩过房主开的房间选择的游戏，那么会提示”未找到匹配rom，将在你启动对应rom时进行联机”，此时选择相同的核心、相同的游戏启动即可加入联机房间。</p>
<h2 id="3-5-软件异地组网联机（异地组网）"><a href="#3-5-软件异地组网联机（异地组网）" class="headerlink" title="3.5 软件异地组网联机（异地组网）"></a>3.5 软件异地组网联机（异地组网）</h2><p>很多软件提供多台外网机器组局域网的能力，这样就可以参照<strong>局域网联机</strong>一节进行局域网联机操作。但比较麻烦的是，和你玩的朋友可能不愿意下载一些软件，尤其当这些软件设置起来还需要一定的网络知识的情况。</p>
<p>因为几乎所有的异地组网软件都是通过内网穿透打洞的方式进行的，网络质量会非常影响打洞的成功率，而且延迟问题也不能保证。由于软件众多不再进行详解，异地组网方式请自行搜索皎月连、贝瑞蒲公英、zerotier、Netbird等异地组网教程。</p>
<p>当所有机器连接到同一网段时就可以使用局域网联机的方式进行游玩了。</p>
<h1 id="4-问题排查"><a href="#4-问题排查" class="headerlink" title="4. 问题排查"></a>4. 问题排查</h1><ol>
<li><p>联机支持哪些游戏？哪些核心?<br>不清楚，我目前只使用了Fbneo核心进行街机游戏的联机功能，具体的支持范围你可以参考官方教程或各核心的github库查询。</p>
</li>
<li><p>能够实现哪些功能？<br>宝可梦交换、通信进化，同网络不同屏幕联机游戏，不同网络不同屏幕联机游戏。</p>
</li>
<li><p>延迟问题？<br>自建服务器取决于你的服务器，国内的服务器大约在30ms-150ms波动，对于一些对战类比较勉强，对于闯关类还不错。<br>官方服务器本身就没有国内cn的服务器，延迟大很正常<br>理论上最好的为直连，少了一个中继服务器的延迟，但直连要公网地址，目前运营商都不给了。</p>
</li>
<li><p>可以直接使用<strong>连接到联机主机</strong>输入ip地址吗？<br>可以的，但只有局域网和直连可用，中继的话填写不了主机ip（存疑，70%把握）</p>
</li>
<li><p>直接使用<strong>连接到联机主机</strong>功能输入服务器地址提示”服务器版本较低”怎么办？<br>请注意，<strong>连接到联机主机</strong>功能输入的是主机（房主）机器的IP地址，不是你搭建的服务器地址。</p>
</li>
</ol>
</article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" title="头像" alt="头像"></a><div class="post-copyright__author_name">c2hapmll</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://shijie.icu/post/20250422110000.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl('https://shijie.icu/post/20250422110000.html')">2025年04月22日 - RetroArch模拟器 - 联机篇</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/wechat.jpg" alt="微信"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/alipay.jpg" alt="支付宝"/></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display: none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://shijie.icu/post/20250422110000.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=2025年04月22日 - RetroArch模拟器 - 联机篇&amp;url=https://shijie.icu/post/20250422110000.html&amp;pic=/images/posts/2025/042201/202504220200_top.png" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl() {
  var currentPageUrl = window.location.href;
  var input = document.createElement("input");
  input.setAttribute("value", currentPageUrl);
  document.body.appendChild(input);
  input.select();
  input.setSelectionRange(0, 99999);
  document.execCommand("copy");
  document.body.removeChild(input);
}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://shijie.icu" target="_blank">我的博客</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E6%97%A5%E5%B8%B8/"><span class="tags-punctuation"> <i class="anzhiyufont anzhiyu-icon-tag"></i></span>日常<span class="tagsPageCount">3</span></a><a class="post-meta__box__tags" href="/tags/%E6%8C%87%E5%8D%97/"><span class="tags-punctuation"> <i class="anzhiyufont anzhiyu-icon-tag"></i></span>指南<span class="tagsPageCount">3</span></a><a class="post-meta__box__tags" href="/tags/%E6%B8%B8%E6%88%8F/"><span class="tags-punctuation"> <i class="anzhiyufont anzhiyu-icon-tag"></i></span>游戏<span class="tagsPageCount">2</span></a><a class="post-meta__box__tags" href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/"><span class="tags-punctuation"> <i class="anzhiyufont anzhiyu-icon-tag"></i></span>模拟器<span class="tagsPageCount">2</span></a></div></div></div><div class="post_share"><div class="social-share" data-image="/images/posts/2025/042201/202504220200_top.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media='all'"/><script src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/js/social-share.min.js" defer="defer"></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-full"><a href="/post/20250215190000.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/021501/top.jpg" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">2025年02月15日-记一次去医院看牙的经历</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size: 1.5rem; margin-right: 4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器"><img class="cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024112001/top.png" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2024-11-20</div><div class="title">2024年11月20日-RetroArch全能游戏模拟器</div></div></a></div><div><a href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南"><img class="cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024101001/top.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2024-10-10</div><div class="title">2024年10月10日-流量卡选购指南</div></div></a></div></div></div><hr/><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)">匿名评论</a><a href="/privacy" style="margin-left: 4px">隐私政策</a></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div></div></div><div class="comment-barrage"></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="card-content"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div><div class="author-info-avatar"><img class="avatar-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/><div class="author-status"><img class="g-status" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="https://bu.dusays.com/2021/03/03/c50ad1cd452e8.png" alt="status"/></div></div><div class="author-info__description"><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">欢迎访问我的博客，这里是我分享<b style="color:#fff">生活</b>的地方，另外还有一些技能和经验的<b style="color:#fff">记录</b>。</div><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">希望可以帮助到你。</div></div><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">c2hapmll</h1><div class="author-info__desc"></div></a><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://steamcommunity.com/profiles/76561199172600200/" target="_blank" title="Steam"><svg class="icon faa-tada" aria-hidden="true"><use xlink:href="#icon-steam"></use></svg></a></div></div></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bullhorn anzhiyu-shake"></i><span>公告</span></div><div class="announcement_content">刚从Halo迁移至Hexo，还在调整中，欢迎访问~ <br/> 本站开放时间为：<b>8:30-23:00</b>，可能会提前关站哦~ <br/> 历史更新请点击<a href="https://shijie.icu/update/"><b>博客更新日志</b></a> <br/></div></div><div class="card-widget card-countdown"><div class="item-headline"><i></i><span></span></div><div class="item-content"><div class="cd-count-left">
  <span class="cd-text">距离</span>
  <span class="cd-name" id="eventName"></span>
  <span class="cd-time" id="daysUntil"></span>
  <span class="cd-date" id="eventDate"></span>
</div>
<div id="countRight" class="cd-count-right"></div>
</div></div><div class="card-widget music-widget"><div class="item-headline"><i></i><span></span></div><div class="item-content"><div class="music-widget">
  <div id="musicStatus" class="music-status"></div>
  <img id="musicCover" class="music-cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="" alt="专辑封面">
</div>
<div class="music-info">
  <div id="musicTitle" class="music-title"></div>
  <div id="musicArtist" class="music-artist"></div>
  <div id="musicAlbum" class="music-album"></div>
</div>
</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#1-%E8%AE%BE%E7%BD%AE%E7%94%A8%E6%88%B7%E5%90%8D"><span class="toc-number">1.</span> <span class="toc-text">1. 设置用户名</span></a></li><li class="toc-item toc-level-1"><a class="toc-link" href="#2-%E5%B1%80%E5%9F%9F%E7%BD%91%E8%81%94%E6%9C%BA"><span class="toc-number">2.</span> <span class="toc-text">2. 局域网联机</span></a></li><li class="toc-item toc-level-1"><a class="toc-link" href="#3-%E5%85%AC%E7%BD%91%E8%81%94%E6%9C%BA"><span class="toc-number">3.</span> <span class="toc-text">3. 公网联机</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#3-1-UPnP%E5%92%8C%E7%AB%AF%E5%8F%A3%E8%BD%AC%E5%8F%91%EF%BC%88%E5%86%85%E7%BD%91%E7%A9%BF%E9%80%8F%EF%BC%89"><span class="toc-number">3.1.</span> <span class="toc-text">3.1 UPnP和端口转发（内网穿透）</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-1-%E8%B7%AF%E7%94%B1%E5%99%A8"><span class="toc-number">3.1.1.</span> <span class="toc-text">3.1.1 路由器</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-2-%E4%B8%BB%E6%9C%BA%E7%AB%AF%E8%AE%BE%E7%BD%AE%EF%BC%88%E6%88%BF%E4%B8%BB%EF%BC%89"><span class="toc-number">3.1.2.</span> <span class="toc-text">3.1.2 主机端设置（房主）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-3-%E5%AE%A2%E6%88%B7%E7%AB%AF%E8%AE%BE%E7%BD%AE%EF%BC%88%E5%85%B6%E4%BB%96%E7%8E%A9%E5%AE%B6%EF%BC%89"><span class="toc-number">3.1.3.</span> <span class="toc-text">3.1.3 客户端设置（其他玩家）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-2-%E5%85%AC%E7%BD%91%E7%9B%B4%E8%BF%9E"><span class="toc-number">3.2.</span> <span class="toc-text">3.2 公网直连</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-3-%E5%AE%98%E6%96%B9%E8%81%94%E6%9C%BA%E6%9C%8D%E5%8A%A1%E5%99%A8%EF%BC%88%E4%B8%AD%E7%BB%A7%EF%BC%89"><span class="toc-number">3.3.</span> <span class="toc-text">3.3 官方联机服务器（中继）</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-4-%E8%87%AA%E5%BB%BA%E8%81%94%E6%9C%BA%E6%9C%8D%E5%8A%A1%E5%99%A8%EF%BC%88%E4%B8%AD%E7%BB%A7%EF%BC%89"><span class="toc-number">3.4.</span> <span class="toc-text">3.4 自建联机服务器（中继）</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-4-1-%E9%85%8D%E7%BD%AE%E8%81%94%E6%9C%BA%E6%9C%8D%E5%8A%A1"><span class="toc-number">3.4.1.</span> <span class="toc-text">3.4.1 配置联机服务</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-4-2-%E4%B8%BB%E6%9C%BA%E7%AB%AF%E8%AE%BE%E7%BD%AE%EF%BC%88%E6%88%BF%E4%B8%BB%EF%BC%89"><span class="toc-number">3.4.2.</span> <span class="toc-text">3.4.2 主机端设置（房主）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-4-3-%E5%AE%A2%E6%88%B7%E7%AB%AF%E8%AE%BE%E7%BD%AE%EF%BC%88%E5%85%B6%E4%BB%96%E7%8E%A9%E5%AE%B6%EF%BC%89"><span class="toc-number">3.4.3.</span> <span class="toc-text">3.4.3 客户端设置（其他玩家）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-5-%E8%BD%AF%E4%BB%B6%E5%BC%82%E5%9C%B0%E7%BB%84%E7%BD%91%E8%81%94%E6%9C%BA%EF%BC%88%E5%BC%82%E5%9C%B0%E7%BB%84%E7%BD%91%EF%BC%89"><span class="toc-number">3.5.</span> <span class="toc-text">3.5 软件异地组网联机（异地组网）</span></a></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#4-%E9%97%AE%E9%A2%98%E6%8E%92%E6%9F%A5"><span class="toc-number">4.</span> <span class="toc-text">4. 问题排查</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/042201/202504220200_top.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2025年04月22日 - RetroArch模拟器 - 联机篇"/></a><div class="content"><a class="title" href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇">2025年04月22日 - RetroArch模拟器 - 联机篇</a><time datetime="2025-04-22T03:00:00.000Z" title="发表于 2025-04-22 11:00:00">2025-04-22</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20250215190000.html" title="2025年02月15日-记一次去医院看牙的经历"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/021501/top.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2025年02月15日-记一次去医院看牙的经历"/></a><div class="content"><a class="title" href="/post/20250215190000.html" title="2025年02月15日-记一次去医院看牙的经历">2025年02月15日-记一次去医院看牙的经历</a><time datetime="2025-02-15T11:00:00.000Z" title="发表于 2025-02-15 19:00:00">2025-02-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20241203090000.html" title="2024年12月03日-Hexo安知鱼主题魔改记录"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024120301/top.webp" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年12月03日-Hexo安知鱼主题魔改记录"/></a><div class="content"><a class="title" href="/post/20241203090000.html" title="2024年12月03日-Hexo安知鱼主题魔改记录">2024年12月03日-Hexo安知鱼主题魔改记录</a><time datetime="2024-12-03T01:00:00.000Z" title="发表于 2024-12-03 09:00:00">2024-12-03</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024112001/top.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年11月20日-RetroArch全能游戏模拟器"/></a><div class="content"><a class="title" href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器">2024年11月20日-RetroArch全能游戏模拟器</a><time datetime="2024-11-20T01:26:54.000Z" title="发表于 2024-11-20 09:26:54">2024-11-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024101001/top.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年10月10日-流量卡选购指南"/></a><div class="content"><a class="title" href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南">2024年10月10日-流量卡选购指南</a><time datetime="2024-10-10T06:44:21.000Z" title="发表于 2024-10-10 14:44:21">2024-10-10</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">&copy;2024 - 2025 By <a class="footer-bar-link" href="/" title="c2hapmll" target="_blank">c2hapmll</a></div></div><div id="footer-type-tips"></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index" title="皖ICP备20000482号-2">皖ICP备20000482号-2</a><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.mps.gov.cn/#/query/webSearch" title="皖公网安备 34120402000423号">皖公网安备 34120402000423号</a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">6</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">9</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">3</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" target="_blank" rel="noopener" href="https://home.shijie.icu/" title="我的主页"><img class="back-menu-item-icon" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/32.png" alt="我的主页"/><span class="back-menu-item-text">我的主页</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 0.88rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 0.88rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 0.88rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 0.88rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 0.88rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 0.88rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 0.88rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 0.88rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 0.88rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div id="rightside"><div id="rightside-config-hide"><button id="readmode" type="button" title="阅读模式"><i class="anzhiyufont anzhiyu-icon-book-open"></i></button><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><a id="to_comment" href="#post-comment" title="直达评论"><i class="anzhiyufont anzhiyu-icon-comments"></i></a><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><span id="loading-status"></span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="is-center" id="loading-database"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-pulse-icon"></i><span>  数据库加载中</span></div><div class="search-wrap"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div><hr/><div id="local-search-results"></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size: 1rem;"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://y.qq.com/n/ryqq/playlist/8802438608&quot;, &quot;_blank&quot;);" style="display: none;"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://cdn.cbd.int/instant.page@5.2.0/instantpage.js" type="module"></script><script src="https://cdn.cbd.int/vanilla-lazyload@17.8.5/dist/lazyload.iife.min.js"></script><script src="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.js"></script><canvas id="universe"></canvas><script async src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script async src="/anzhiyu/random.js"></script><script src="/js/search/local-search.js"></script><div class="js-pjax"><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
        region: '',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script>var visitorMail = "";
</script><script async data-pjax src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script async src="https://at.alicdn.com/t/c/font_4729035_3m5int5al8c.js"></script><script src="/js/countdown.js"></script><script src="/js/getnowplaying.js"></script><script src="/js/badge.js"></script><script src="/js/update.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/aplayer/APlayer.min.css" media="print" onload="this.media='all'"><script src="https://cdn.cbd.int/anzhiyu-blog-static@1.0.1/js/APlayer.min.js"></script><script src="https://cdn.cbd.int/hexo-anzhiyu-music@1.0.1/assets/js/Meting2.min.js"></script><script src="https://cdn.cbd.int/pjax@0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: false,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script><script charset="UTF-8" src="https://cdn.cbd.int/anzhiyu-theme-static@1.1.5/accesskey/accesskey.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div></body></html>
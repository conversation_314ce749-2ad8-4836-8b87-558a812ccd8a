{"name": "warehouse", "version": "5.0.1", "description": "Simple JSON-based database", "main": "dist/database", "directories": {"lib": "./dist"}, "files": ["dist/"], "scripts": {"prepublish ": "npm install && npm run clean && npm run build", "build": "tsc -b", "clean": "tsc -b --clean", "preeslint": "npm run clean && npm run build", "eslint": "eslint src test", "pretest": "npm run clean && npm run build", "test": "mocha -r ts-node/register 'test/scripts/**/*.ts'", "test-cov": "c8 --reporter=lcovonly --reporter=text-summary npm test", "typedoc": "typedoc --entryPointStrategy expand ./src"}, "repository": "hexojs/warehouse", "keywords": ["database", "json", "db"], "author": "<PERSON> <<EMAIL>> (https://zespia.tw)", "license": "MIT", "dependencies": {"bluebird": "^3.7.2", "cuid": "^2.1.8", "graceful-fs": "^4.2.10", "hexo-log": "^4.0.1", "is-plain-object": "^5.0.0", "jsonparse": "^1.3.1", "rfdc": "^1.3.0", "through2": "^4.0.2"}, "devDependencies": {"@types/bluebird": "^3.5.37", "@types/chai": "^4.3.3", "@types/graceful-fs": "^4.1.5", "@types/jsonstream": "^0.8.30", "@types/mocha": "^10.0.0", "@types/node": "^18.11.7", "@types/sinon": "^17.0.3", "@types/through2": "^2.0.36", "c8": "^9.0.0", "chai": "^4.3.6", "chai-as-promised": "^7.1.1", "eslint": "^8.26.0", "eslint-config-hexo": "^5.0.0", "lodash": "^4.17.21", "mocha": "^10.1.0", "sinon": "^17.0.1", "ts-node": "^10.9.1", "typedoc": "^0.25.0", "typedoc-plugin-rename-defaults": "^0.7.0", "typescript": "^5.0.3"}, "engines": {"node": ">=14"}}
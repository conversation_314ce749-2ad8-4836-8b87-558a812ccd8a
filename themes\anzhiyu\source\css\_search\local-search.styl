#local-search
  .search-dialog
    .local-search-box
      margin: 0 auto
      max-width: 100%
      width: 100%

      input
        padding: 5px 14px
        width: 100%
        outline: none
        border: 2px solid $search-color
        border-radius: 40px
        background: var(--search-bg)
        color: var(--search-input-color)
        -webkit-appearance: none

    .search-wrap
      display: none

    .local-search__hit-item
      position: relative
      padding-left: 24px
      line-height: 1.7

      &:hover
        &:before
          border-color: var(--pseudo-hover)

      &:before
        $w = .5em
        position: absolute
        top: .45em
        left: 0
        width: w = $w
        height: h = w
        border: 3px solid $search-color
        border-radius: w
        background: transparent
        content: ''
        line-height: h
        transition: all .2s ease-in-out

      a
        display: block
        color: var(--search-result-title)
        font-weight: 600
        cursor: pointer

        &:hover
          color: $search-color

      .search-result
        margin: 0 8px 8px 0
        word-break: break-all

      .search-keyword
        color: $search-keyword-highlight
        font-weight: bold

    .search-result-list
      overflow-y: auto
      max-height: calc(80vh - 130px)

      +maxWidth768()
        padding-bottom: 40px
        max-height: 75vh !important

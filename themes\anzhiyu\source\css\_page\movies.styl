.movies-title
  margin: 1rem 0
  line-height: 1;

.movies-item
  .movies-item-content
    display: flex
    flex-direction: row
    flex-wrap: wrap
    gap: 12px // 控制项目间距
    .movies-item-content-item
      display: flex
      flex-direction: row // 保持横向布局
      width: calc(33.33% - 12px) // 每行放置3个
      height: 180px // 统一高度
      border-radius: 12px
      border: var(--style-border-always)
      overflow: hidden
      background: var(--anzhiyu-card-bg)
      box-shadow: var(--anzhiyu-shadow-border)
      position: relative
      margin-bottom: 16px

      // 中等屏幕布局调整
      +maxWidth1200()
        width: calc(50% - 16px) // 中屏幕时每行2个

      // 小屏幕布局调整
      +maxWidth768()
        width: 100% // 小屏幕时每行1个，保留横向布局
        flex-direction: row // 强制保持图片在左、文字在右
        height: 180px // 设置小屏幕时盒子的高度为200px

      // 图片部分
      .movies-item-content-item-cover
        flex: 0 0 120px // 小屏幕时固定图片宽度为120px
        height: 100%
        background: var(--anzhiyu-card-bg) // 修改为与文字背景一致
        display: flex
        justify-content: center
        align-items: center
        padding: 10px // 添加内边距，使图片不紧贴边缘
        position: relative

        // 修改为填满整个 a 标签容器
        a
          display: block
          width: 100%
          height: 100%

        img.movies-item-content-item-image
          object-fit: cover // 填充整个容器，不失真
          width: 100%
          height: 100%
          border-radius: 12px // 可根据需求调整圆角

      // 文字部分
      .movies-item-content-item-info
        flex: 1
        display: flex
        flex-direction: column
        padding: 0 12px // 添加左右内边距

        .movies-item-content-item-name
          font-size: 20px // 加大字体
          font-weight: 700 // 加粗文字
          line-height: 1.1
          margin-bottom: 12px
          margin-top: 20px
          white-space: nowrap
          overflow: hidden
          text-overflow: ellipsis
          cursor: pointer
          &:hover
            color: var(--anzhiyu-main)

        .movies-item-content-item-specification
          font-size: 14px
          color: var(--anzhiyu-secondtext)
          line-height: 16px
          margin-bottom: 12px
          white-space: nowrap
          overflow: hidden
          text-overflow: ellipsis

          // 修改年份样式
          .year
            color: var(--anzhiyu-secondtext)
            font-weight: normal

          // 新增样式：类型
          .genre
            color: var(--anzhiyu-secondtext)

          // 新增样式：评分
          .rating
            color: #FFB800
            font-weight: 600

          // 新增样式：观看日期
          .watch-date
            color: var(--anzhiyu-secondtext)
            font-style: italic

        .movies-item-content-item-description
          font-size: 14px
          font-weight: 700
          line-height: 20px
          color: var(--anzhiyu-secondtext)
          display: -webkit-box
          -webkit-line-clamp: 3 // 限制为三行
          -webkit-box-orient: vertical
          overflow: hidden

body[data-type="movies"] #web_bg
  background: var(--anzhiyu-background)

body[data-type="movies"] #page
  border: 0;
  box-shadow: none !important;
  padding: 0 !important;
  background: 0 0 !important;

body[data-type="movies"] #page .page-title
  display: none;

if $highlight_theme == 'light' || ($highlight_theme == 'mac light')
  // prism-base16-ateliersulphurpool.light
  pre[class*='language-']
    .token.function
      color: #ffb62c

    .token.comment,
    .token.prolog,
    .token.doctype,
    .token.cdata
      color: rgba(149, 165, 166, .8)

    .token.punctuation
      color: #5e6687

    .token.namespace
      opacity: .7

    .token.operator,
    .token.boolean,
    .token.number
      color: #c76b29

    .token.property
      color: #c08b30

    .token.tag
      color: #3d8fd1

    .token.string
      color: #22a2c9

    .token.selector
      color: #6679cc

    .token.attr-name
      color: #c76b29

    .token.entity,
    .token.url,
    .language-css .token.string,
    .style .token.string
      color: #22a2c9

    .token.attr-value,
    .token.keyword,
    .token.control,
    .token.directive,
    .token.unit
      color: #ac9739

    .token.statement,
    .token.regex,
    .token.atrule
      color: #22a2c9

    .token.placeholder,
    .token.variable
      color: #3d8fd1

    .token.deleted
      text-decoration: line-through

    .token.inserted
      border-bottom: 1px dotted #202746
      text-decoration: none

    .token.italic
      font-style: italic

    .token.important,
    .token.bold
      font-weight: bold

    .token.important
      color: #c94922

    .token.entity
      cursor: help

    pre > code.highlight
      outline: .4em solid #c94922
      outline-offset: .4em

if $highlight_theme == 'darker' || ($highlight_theme == 'mac')
  // prism-atom-dark
  pre[class*='language-']
    .token.comment,
    .token.prolog,
    .token.doctype,
    .token.cdata
      color: #7C7C7C

    .token.punctuation
      color: #c5c8c6

    .namespace
      opacity: .7

    .token.property,
    .token.keyword,
    .token.tag
      color: #96CBFE

    .token.class-name
      color: #FFFFB6

    .token.boolean,
    .token.constant
      color: #99CC99

    .token.symbol,
    .token.deleted
      color: #f92672

    .token.number
      color: #FF73FD

    .token.selector,
    .token.attr-name,
    .token.string,
    .token.char,
    .token.builtin,
    .token.inserted
      color: #A8FF60

    .token.variable
      color: #C6C5FE

    .token.operator
      color: #EDEDED

    .token.entity
      color: #FFFFB6
      cursor: help

    .token.url
      color: #96CBFE

    .language-css .token.string,
    .style .token.string
      color: #87C38A

    .token.atrule,
    .token.attr-value
      color: #F9EE98

    .token.function
      color: #DAD085

    .token.regex
      color: #E9C062

    .token.important
      color: #fd971f

    .token.important,
    .token.bold
      font-weight: bold

    .token.italic
      font-style: italic

if $highlight_theme == 'pale night'
  // prism-dracula
  pre[class*='language-']
    .token.comment,
    .token.prolog,
    .token.doctype,
    .token.cdata
      color: #6272a4

    .token.punctuation
      color: #f8f8f2

    .namespace
      opacity: .7

    .token.property,
    .token.tag,
    .token.constant,
    .token.symbol,
    .token.deleted
      color: #ff79c6

    .token.boolean,
    .token.number
      color: #bd93f9

    .token.selector,
    .token.attr-name,
    .token.string,
    .token.char,
    .token.builtin,
    .token.inserted
      color: #50fa7b

    .token.operator,
    .token.entity,
    .token.url,
    .language-css .token.string,
    .style .token.string,
    .token.variable
      color: #f8f8f2

    .token.atrule,
    .token.attr-value,
    .token.function,
    .token.class-name
      color: #f1fa8c

    .token.keyword
      color: #8be9fd

    .token.regex,
    .token.important
      color: #ffb86c

    .token.important,
    .token.bold
      font-weight: bold

    .token.italic
      font-style: italic

    .token.entity
      cursor: help

if $highlight_theme == 'ocean'
  // prism-material-oceanic
  pre[class*='language-']
    &.language-css > code,
    &.language-sass > code,
    &.language-scss > code
      color: #fd9170 !important

    .namespace
      opacity: .7

    .token.atrule,
    .token.symbol,
    .token.constant,
    .token.boolean,
    .token.function
      color: #c792ea

    .token.attr-name,
    .token.builtin,
    .token.class
      color: #ffcb6b

    .token.attr-value,
    .token.attribute,
    .token.pseudo-class,
    .token.pseudo-element,
    .token.string
      color: #c3e88d

    .token.cdata,
    .token.property,
    .token.char,
    .token.inserted
      color: #80cbc4

    .token.class-name,
    .token.color,
    .token.hexcode,
    .token.regex
      color: #f2ff00

    .token.comment,
    .token.prolog,
    .token.doctype
      color: #546e7a

    .token.deleted,
    .token.variable,
    .token.entity,
    .token.selector,
    .token.tag,
    .token.unit
      color: #f07178

    .token.id
      color: #c792ea
      font-weight: bold

    .token.important
      color: #c792ea
      font-weight: bold

    .token.keyword
      color: #c792ea
      font-style: italic

    .token.number,
    .token.url
      color: #fd9170

    .token.operator,
    .token.punctuation
      color: #89ddff
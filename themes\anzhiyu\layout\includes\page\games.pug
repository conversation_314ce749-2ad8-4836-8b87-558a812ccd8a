#games
  if site.data.games
    each i in site.data.games
      .author-content.author-content-item.GamesPage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          .banner-button-group
            a.banner-button(href=i.buttonLink)
              i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
              span.banner-button-text=i.buttonText
      each item in i.good_games
        .goodgames-item
          h2.goodgames-title= item.title
          .goodgames-item-description= item.description
          .games-item
            .games-item-content
              each iten, indey in item.games_list
                .games-item-content-item
                  .games-item-content-item-cover
                    if iten.back_image
                      .flip-card-inner
                        .flip-card-front
                          img.games-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                        .flip-card-back
                          img.games-item-back-image(data-lazy-src=url_for(iten.back_image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                    else
                      img.games-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                  .games-item-content-item-info
                    .games-item-content-item-name(onclick=`rm.rightmenuCopyText("${iten.name}");anzhiyu.snackbarShow("${_p('已复制游戏名：') + " 【" + iten.name + "】"}");` title=iten.name)= iten.name
                    .games-item-content-item-specification
                      if iten.platform
                        span.games-item-content-item-specification-platform= iten.platform + " / "
                      if iten.rating
                        span.games-item-content-item-specification-rating= iten.rating
                      if iten.play_date
                        span.games-item-content-item-specification-play-date= " / " + iten.play_date
                      if iten.achievements
                        span.games-item-content-item-specification-achievements= " / " + iten.achievements
                    .games-item-content-item-description= iten.description
                    .games-item-content-item-toolbar
                      if iten.link.includes('https://') || iten.link.includes('http://')
                        a.games-item-content-item-link(href= iten.link, target='_blank') 详情
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message
                      else  
                        a.games-item-content-item-link(href= iten.link, target='_blank') 查看文章
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message
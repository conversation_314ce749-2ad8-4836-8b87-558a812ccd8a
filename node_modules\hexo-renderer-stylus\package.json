{"name": "hexo-renderer-stylus", "version": "3.0.1", "description": "Stylus renderer plugin for Hexo", "main": "index", "scripts": {"eslint": "eslint .", "test": "mocha test/index.js", "test-cov": "c8 --reporter=lcovonly --reporter=text npm run test"}, "directories": {"lib": "./lib"}, "files": ["lib", "index.js"], "repository": "hexojs/hexo-renderer-stylus", "keywords": ["hexo", "stylus", "css", "style", "stylesheet", "styl", "renderer"], "author": "<PERSON> <<EMAIL>> (https://zespia.tw)", "license": "MIT", "dependencies": {"nib": "^1.2.0", "stylus": "^0.62.0"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "eslint": "^8.40.0", "eslint-config-hexo": "^5.0.0", "hexo": "^7.0.0", "mocha": "^10.2.0"}, "engines": {"node": ">=14"}}
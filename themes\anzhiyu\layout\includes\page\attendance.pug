#attendance
  if site.data.attendance
    each i in site.data.attendance
      .author-content.author-content-item.attendancePage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          .banner-button-group
            a.banner-button(href=i.buttonLink)
              i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
              span.banner-button-text=i.buttonText
      each item in i.good_attendance
        .goodattendance-item
          h2.goodattendance-title= item.title
          .goodattendance-item-description= item.description
          .attendance-item
            .attendance-item-content
              each iten, indey in item.attendance_list
                .attendance-item-content-item
                  .attendance-item-content-item-cover
                    if iten.back_image
                      .flip-card-inner
                        .flip-card-front
                          img.attendance-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                        .flip-card-back
                          img.attendance-item-back-image(data-lazy-src=url_for(iten.back_image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                    else
                      img.attendance-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                  .attendance-item-content-item-info
                    .attendance-item-content-item-name(onclick=`rm.rightmenuCopyText("${iten.name}");anzhiyu.snackbarShow("${_p('已复制地名：') + " 【" + iten.name + "】"}");` title=iten.name)= iten.name
                    .attendance-item-content-item-specification= iten.type + ' / ' + iten.rating + ' / ' + iten.cost + ' / ' + iten.checkin_date
                    .attendance-item-content-item-description= iten.description
                    .attendance-item-content-item-toolbar
                      if iten.link.includes('https://') || iten.link.includes('http://')
                        a.attendance-item-content-item-link(href= iten.link, target='_blank') 详情
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.type + " " + iten.rating + " " + iten.cost + " " + iten.checkin_date + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message
                      else  
                        a.attendance-item-content-item-link(href= iten.link, target='_blank') 查看文章
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.type + " " + iten.rating + " " + iten.cost + " " + iten.checkin_date + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message
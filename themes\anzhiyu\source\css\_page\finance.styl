// 财务页面样式
.finance_all-title
  margin: 1rem 0
  line-height: 1;
.finance-item
  .finance-item-content
    display: flex
    flex-direction: row
    flex-wrap: wrap
    margin: 0 -8px
    .finance-item-content-item
      width: calc(25% - 12px)
      border-radius: 12px
      border: var(--style-border-always)
      overflow: hidden
      margin: 8px 6px
      background: var(--anzhiyu-card-bg)
      box-shadow: var(--anzhiyu-shadow-border)
      min-height: 400px
      position: relative
      +maxWidth1200()
        width: calc(50% - 12px)
      +maxWidth768()
        width: 100%
      
      .finance-item-content-item-info
        padding: 8px 16px 16px 16px
        margin-top: 12px

      .finance-item-content-item-name
        display: flex
        justify-content: space-between
        font-size: 18px
        font-weight: bold
        line-height: 1
        margin-bottom: 8px
        white-space: nowrap
        overflow: visible
        text-overflow: ellipsis
        cursor: pointer
        &:hover
            color: var(--anzhiyu-main)

      .finance-item-month
        // 其他样式

      .finance-item-expense
        // 其他样式

      .finance-item-content-item-specification
        font-size: 12px
        color: var(--anzhiyu-secondtext)
        line-height: 16px
        margin-bottom: 5px
        white-space: nowrap
        overflow: hidden
        text-overflow: ellipsis

      .finance-item-content-item-description
        margin-top: 10px;
        line-height: 20px
        color: var(--anzhiyu-secondtext)
        height: 125px
        display: -webkit-box
        overflow: hidden
        -webkit-line-clamp: 5
        -webkit-box-orient: vertical
        font-size: 14px

      a.finance-item-content-item-link
        font-size: 12px
        background: var(--anzhiyu-gray-op)
        padding: 4px 8px
        border-radius: 8px
        cursor: pointer

        &:hover
          background: var(--anzhiyu-main)
          color: var(--anzhiyu-white)

      // 翻转卡片效果
      .finance-item-content-item-cover
        position: relative
        width: 100%
        height: 200px
        perspective: 1000px // 创建3D效果

        // 使用外层容器的 `:hover`，避免翻转过程中动画重复触发
        &:hover .flip-card-inner
          transform: rotateY(-180deg)

        .flip-card-inner
          position: relative
          width: 100%
          height: 100%
          transition: transform 0.6s
          transform-style: preserve-3d
          transform: rotateY(0deg) // 初始状态为 0 度

          .flip-card-front, .flip-card-back
            position: absolute
            width: 100%
            height: 100%
            backface-visibility: hidden
            display: flex
            justify-content: center
            align-items: center

          .flip-card-front
            z-index: 2
            img.finance-item-content-item-image
              object-fit: cover
              height: 100%
              width: 100%

          .flip-card-back
            transform: rotateY(-180deg)
            z-index: 1
            img.finance-item-back-image
              object-fit: cover
              height: 100%
              width: 100%

      img.finance-item-content-item-image
        object-fit: cover
        height: 100%
        width: 100%
       // border-radius: 0 
       // 若需要去除图片圆角可以将这里的注释去掉

      .finance-item-content-item-toolbar
        display: flex
        justify-content: space-between
        position: absolute
        bottom: 12px
        left: 0
        width: 100%
        padding: 0 16px

body[data-type="finance"] #web_bg
  background: var(--anzhiyu-background);
body[data-type="finance"] #page
  border: 0;
  box-shadow: none !important;
  padding: 0 !important;
  background: 0 0 !important;
body[data-type="finance"] #page .page-title
  display: none;
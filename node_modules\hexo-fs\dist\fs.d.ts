/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import type { WriteFileOptions } from 'fs';
import chokidar, { WatchOptions } from 'chokidar';
import BlueBirdPromise from 'bluebird';
import fs from 'graceful-fs';
import type { Stream } from 'stream';
export declare function exists(path: string): BlueBirdPromise<boolean>;
export declare function existsSync(path: string): boolean;
export declare function mkdirs(path: string): BlueBirdPromise<string>;
export declare function mkdirsSync(path: string): void;
export declare function writeFile(path: string, data?: string | NodeJS.ArrayBufferView | Iterable<string | NodeJS.ArrayBufferView> | AsyncIterable<string | NodeJS.ArrayBufferView> | Stream, options?: WriteFileOptions): BlueBirdPromise<void>;
export declare function writeFileSync(path: string, data: string | NodeJS.ArrayBufferView, options?: WriteFileOptions): void;
export declare function appendFile(path: string, data: string | Uint8Array, options?: WriteFileOptions): BlueBirdPromise<void>;
export declare function appendFileSync(path: string, data: string | Uint8Array, options?: WriteFileOptions): void;
export declare function copyFile(src: string, dest: string, flags?: number): BlueBirdPromise<void>;
export type ReadDirOptions = {
    encoding?: BufferEncoding | null;
    withFileTypes?: false;
    ignoreHidden?: boolean;
    ignorePattern?: RegExp;
};
export declare function copyDir(src: string, dest: string, options?: ReadDirOptions): BlueBirdPromise<string[]>;
export declare function listDir(path: string, options?: ReadDirOptions): BlueBirdPromise<string[]>;
export declare function listDirSync(path: string, options?: ReadDirOptions): string[];
export declare function escapeEOL(str: string): string;
export declare function escapeBOM(str: string): string;
export declare function escapeFileContent(content: string): string;
export type ReadFileOptions = {
    encoding?: BufferEncoding | null;
    flag?: string;
    escape?: string;
};
export declare function readFile(path: string): BlueBirdPromise<string>;
export declare function readFile(path: string, options?: ReadFileOptions | null): BlueBirdPromise<string | Buffer>;
export declare function readFileSync(path: string): string;
export declare function readFileSync(path: string, options?: ReadFileOptions): string | Buffer;
export declare function emptyDir(path: string, options?: ReadDirOptions & {
    exclude?: string[];
}): BlueBirdPromise<string[]>;
export declare function emptyDirSync(path: string, options?: ReadDirOptions & {
    exclude?: string[];
}): string[];
export declare function rmdir(path: string): BlueBirdPromise<void>;
export declare function rmdirSync(path: string): void;
export declare function watch(path: string | ReadonlyArray<string>, options?: WatchOptions): BlueBirdPromise<chokidar.FSWatcher>;
export declare function ensurePath(path: string): BlueBirdPromise<string>;
export declare function ensurePathSync(path: string): string;
export declare function ensureWriteStream(path: string, options?: BufferEncoding | {
    flags?: string;
    encoding?: BufferEncoding;
    fd?: number;
    mode?: number;
    autoClose?: boolean;
    emitClose?: boolean;
    start?: number;
    highWaterMark?: number;
}): BlueBirdPromise<fs.WriteStream>;
export declare function ensureWriteStreamSync(path: string, options?: BufferEncoding | {
    flags?: string;
    encoding?: BufferEncoding;
    fd?: number;
    mode?: number;
    autoClose?: boolean;
    emitClose?: boolean;
    start?: number;
    highWaterMark?: number;
}): fs.WriteStream;
export declare const access: (arg1: fs.PathLike) => BlueBirdPromise<unknown>;
export declare const accessSync: typeof fs.accessSync;
export declare const chmod: (arg1: fs.PathLike, arg2: fs.Mode) => BlueBirdPromise<unknown>;
export declare const chmodSync: typeof fs.chmodSync;
export declare const fchmod: (arg1: number, arg2: fs.Mode) => BlueBirdPromise<unknown>;
export declare const fchmodSync: typeof fs.fchmodSync;
export declare const lchmod: (arg1: fs.PathLike, arg2: fs.Mode) => BlueBirdPromise<unknown>;
export declare const lchmodSync: typeof fs.lchmodSync;
export declare const chown: (arg1: fs.PathLike, arg2: number, arg3: number) => BlueBirdPromise<unknown>;
export declare const chownSync: typeof fs.chownSync;
export declare const fchown: (arg1: number, arg2: number, arg3: number) => BlueBirdPromise<unknown>;
export declare const fchownSync: typeof fs.fchownSync;
export declare const lchown: (arg1: fs.PathLike, arg2: number, arg3: number) => BlueBirdPromise<unknown>;
export declare const lchownSync: typeof fs.lchownSync;
export declare const close: (arg1: number) => BlueBirdPromise<unknown>;
export declare const closeSync: typeof fs.closeSync;
export declare const createReadStream: typeof fs.createReadStream;
export declare const createWriteStream: typeof fs.createWriteStream;
export declare const fsync: (arg1: number) => BlueBirdPromise<unknown>;
export declare const fsyncSync: typeof fs.fsyncSync;
export declare const link: (arg1: fs.PathLike, arg2: fs.PathLike) => BlueBirdPromise<unknown>;
export declare const linkSync: typeof fs.linkSync;
export declare const mkdir: (arg1: fs.PathLike) => BlueBirdPromise<unknown>;
export declare const mkdirSync: typeof fs.mkdirSync;
export declare const open: (arg1: fs.PathLike) => BlueBirdPromise<number>;
export declare const openSync: typeof fs.openSync;
export declare const symlink: (arg1: fs.PathLike, arg2: fs.PathLike) => BlueBirdPromise<unknown>;
export declare const symlinkSync: typeof fs.symlinkSync;
export declare const read: (arg1: number) => BlueBirdPromise<number>;
export declare const readSync: typeof fs.readSync;
export declare const readdir: (arg1: fs.PathLike) => BlueBirdPromise<string[]>;
export declare const readdirSync: typeof fs.readdirSync;
export declare const readlink: (arg1: fs.PathLike) => BlueBirdPromise<string>;
export declare const readlinkSync: typeof fs.readlinkSync;
export declare const realpath: (arg1: fs.PathLike) => BlueBirdPromise<string>;
export declare const realpathSync: typeof fs.realpathSync;
export declare const rename: (arg1: fs.PathLike, arg2: fs.PathLike) => BlueBirdPromise<void>;
export declare const renameSync: typeof fs.renameSync;
export declare const stat: (arg1: fs.PathLike) => BlueBirdPromise<fs.Stats>;
export declare const statSync: fs.StatSyncFn;
export declare const fstat: (arg1: number) => BlueBirdPromise<unknown>;
export declare const fstatSync: typeof fs.fstatSync;
export declare const lstat: (arg1: fs.PathLike) => BlueBirdPromise<unknown>;
export declare const lstatSync: fs.StatSyncFn;
export declare const truncate: (arg1: fs.PathLike) => BlueBirdPromise<unknown>;
export declare const truncateSync: typeof fs.truncateSync;
export declare const ftruncate: (arg1: number) => BlueBirdPromise<unknown>;
export declare const ftruncateSync: typeof fs.ftruncateSync;
export declare const unlink: (arg1: fs.PathLike) => BlueBirdPromise<void>;
export declare const unlinkSync: typeof fs.unlinkSync;
export declare const utimes: (arg1: fs.PathLike, arg2: fs.TimeLike, arg3: fs.TimeLike) => BlueBirdPromise<unknown>;
export declare const utimesSync: typeof fs.utimesSync;
export declare const futimes: (arg1: number, arg2: fs.TimeLike, arg3: fs.TimeLike) => BlueBirdPromise<unknown>;
export declare const futimesSync: typeof fs.futimesSync;
export declare const watchFile: typeof fs.watchFile;
export declare const unwatchFile: typeof fs.unwatchFile;
export declare const write: (arg1: number, arg2: string) => BlueBirdPromise<number>;
export declare const writeSync: typeof fs.writeSync;
export declare const Stats: typeof fs.Stats;
export declare const ReadStream: typeof fs.ReadStream;
export declare const WriteStream: typeof fs.WriteStream;

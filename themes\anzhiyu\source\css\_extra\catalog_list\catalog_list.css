/* 分类目录条、标签目录条 */
#catalog-bar {
  border-radius: var(--anzhiyu-border-radius);
  display: flex;
  border: var(--anzhiyu-border-radius);
  margin-bottom: 1rem;
  justify-content: flex-start;
  align-items: center;
}

#catalog-bar:hover {
  border-color: var(--anzhiyu-theme);
}

#catalog-bar i {
  line-height: inherit;
}

#catalog-list {
  /* 分类/标签较少时，可以选择不设置 width，居中显示 catalog-list-item */
  display: flex;
  white-space: nowrap;
  overflow-x: hidden;
}

#catalog-list::-webkit-scrollbar {
  display: none;
}

.catalog-list-item a {
  margin-right: 0.3rem;
  font-weight: bold;
  color: var(--font-color);
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  padding: 0.1rem 0.5rem;
  border-radius: 8px;
  height: 30px;
  line-height: 30px;
}
.catalog-list-item {
  display: flex;
  align-items: center;
}

.catalog-list-item.selected a {
  background: var(--anzhiyu-lighttext);
  color: var(--anzhiyu-white);
}

a.catalog-more {
  min-width: fit-content;
  font-weight: bold;
  color: var(--anzhiyu-fontcolor);
  margin-left: 16px;
}

a.catalog-more:hover {
  color: var(--anzhiyu-theme);
}

#swiper_container_blog {
  width: 49%;
  transition: all 0.3s, width 0s;
  border-radius: 12px;
}
.swiper-wrapper {
  align-items: center;
}
div#swiper_container {
  transition: 0.3s;
}
/* 防止轮播图css未加载完时的超度横行滚动条 */
#swiper_container {
  overflow: hidden !important;
}
div.swiper_container_card {
  margin: 1rem auto 0;
}
@media screen and (max-width: 768px) {
  div.swiper_container_card {
    margin: 0.2rem auto 0;
  }
}
div#swiper_container {
  background: rgba(255, 255, 255, 0);
  width: 100%;
  padding: 0;
  overflow: initial;
  background: var(--card-bg);
  border-radius: 12px;
  border: var(--style-border);
}
div#swiper_container:hover {
  border: var(--style-border-hover);
  box-shadow: var(--anzhiyu-shadow-main);
}
.blog-slider {
  width: 100%;
  position: relative;
  border-radius: 12px 8px 8px 12px;
  margin: 0 auto;
}
.blog-slider__item {
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: box;
  display: flex;
  -webkit-box-align: center;
  -moz-box-align: center;
  -o-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.blog-slider__item.swiper-slide-active .blog-slider__img img {
  opacity: 1;
  -ms-filter: none;
  filter: none;
  -webkit-transition-delay: 0.3s;
  -moz-transition-delay: 0.3s;
  -o-transition-delay: 0.3s;
  -ms-transition-delay: 0.3s;
  transition-delay: 0.3s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > * {
  opacity: 1;
  -ms-filter: none;
  filter: none;
  -webkit-transform: none;
  -moz-transform: none;
  -o-transform: none;
  -ms-transform: none;
  transform: none;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(1) {
  -webkit-transition-delay: 0.3s;
  -moz-transition-delay: 0.3s;
  -o-transition-delay: 0.3s;
  -ms-transition-delay: 0.3s;
  transition-delay: 0.3s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(2) {
  -webkit-transition-delay: 0.4s;
  -moz-transition-delay: 0.4s;
  -o-transition-delay: 0.4s;
  -ms-transition-delay: 0.4s;
  transition-delay: 0.4s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(3) {
  -webkit-transition-delay: 0.5s;
  -moz-transition-delay: 0.5s;
  -o-transition-delay: 0.5s;
  -ms-transition-delay: 0.5s;
  transition-delay: 0.5s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(4) {
  -webkit-transition-delay: 0.6s;
  -moz-transition-delay: 0.6s;
  -o-transition-delay: 0.6s;
  -ms-transition-delay: 0.6s;
  transition-delay: 0.6s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(5) {
  -webkit-transition-delay: 0.7s;
  -moz-transition-delay: 0.7s;
  -o-transition-delay: 0.7s;
  -ms-transition-delay: 0.7s;
  transition-delay: 0.7s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(6) {
  -webkit-transition-delay: 0.8s;
  -moz-transition-delay: 0.8s;
  -o-transition-delay: 0.8s;
  -ms-transition-delay: 0.8s;
  transition-delay: 0.8s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(7) {
  -webkit-transition-delay: 0.9s;
  -moz-transition-delay: 0.9s;
  -o-transition-delay: 0.9s;
  -ms-transition-delay: 0.9s;
  transition-delay: 0.9s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(8) {
  -webkit-transition-delay: 1s;
  -moz-transition-delay: 1s;
  -o-transition-delay: 1s;
  -ms-transition-delay: 1s;
  transition-delay: 1s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(9) {
  -webkit-transition-delay: 1.1s;
  -moz-transition-delay: 1.1s;
  -o-transition-delay: 1.1s;
  -ms-transition-delay: 1.1s;
  transition-delay: 1.1s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(10) {
  -webkit-transition-delay: 1.2s;
  -moz-transition-delay: 1.2s;
  -o-transition-delay: 1.2s;
  -ms-transition-delay: 1.2s;
  transition-delay: 1.2s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(11) {
  -webkit-transition-delay: 1.3s;
  -moz-transition-delay: 1.3s;
  -o-transition-delay: 1.3s;
  -ms-transition-delay: 1.3s;
  transition-delay: 1.3s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(12) {
  -webkit-transition-delay: 1.4s;
  -moz-transition-delay: 1.4s;
  -o-transition-delay: 1.4s;
  -ms-transition-delay: 1.4s;
  transition-delay: 1.4s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(13) {
  -webkit-transition-delay: 1.5s;
  -moz-transition-delay: 1.5s;
  -o-transition-delay: 1.5s;
  -ms-transition-delay: 1.5s;
  transition-delay: 1.5s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(14) {
  -webkit-transition-delay: 1.6s;
  -moz-transition-delay: 1.6s;
  -o-transition-delay: 1.6s;
  -ms-transition-delay: 1.6s;
  transition-delay: 1.6s;
}
.blog-slider__item.swiper-slide-active .blog-slider__content > :nth-child(15) {
  -webkit-transition-delay: 1.7s;
  -moz-transition-delay: 1.7s;
  -o-transition-delay: 1.7s;
  -ms-transition-delay: 1.7s;
  transition-delay: 1.7s;
}
.blog-slider__img {
  width: 290px;
  -webkit-flex-shrink: 0;
  flex-shrink: 0;
  height: 200px;
  padding: 10px;
  border-radius: 5px;
  transform: translateX(0);
  overflow: hidden;
}
.blog-slider__img:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  opacity: 0.8;
}
.blog-slider__img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  opacity: 0;
  border-radius: 5px;
  transition: all 0.3s;
}
.blog-slider__content {
  padding-right: 50px;
  padding-left: 50px;
}
.blog-slider__content > * {
  opacity: 0;
  -webkit-transform: translateY(25px);
  -moz-transform: translateY(25px);
  -o-transform: translateY(25px);
  -ms-transform: translateY(25px);
  transform: translateY(25px);
  -webkit-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -o-transition: all 0.4s;
  -ms-transition: all 0.4s;
  transition: all 0.4s;
}
.blog-slider__code {
  color: var(--font-color);
  margin-bottom: 0;
  display: block;
  font-weight: 500;
}
.blog-slider__title {
  font-size: 18px;
  font-weight: 700;
  color: var(--font-color);
  margin-bottom: 15px;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
}
.blog-slider__text {
  color: var(--font-color);
  margin-bottom: 15px;
  line-height: 1.5em;
  width: 100%;
  height: 50px;
  word-break: break-all;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.blog-slider__button {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-box;
  display: inline-flex;
  background-color: var(--btn-bg);
  padding: 4px 14px;
  border-radius: 8px;
  color: var(--btn-color);
  text-decoration: none;
  font-weight: 500;
  -webkit-box-pack: center;
  -moz-box-pack: center;
  -o-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
  letter-spacing: 1px;
  display: none;
}
.blog-slider__button:hover {
  background-color: var(--btn-hover-color);
  color: var(--btn-color);
}
.blog-slider .swiper-container-horizontal > .swiper-pagination-bullets,
.blog-slider .swiper-pagination-custom,
.blog-slider .swiper-pagination-fraction {
  bottom: 10px;
  left: 0;
  width: 100%;
}
.blog-slider__pagination {
  position: absolute;
  z-index: 21;
  right: 20px;
  width: 11px !important;
  text-align: center;
  left: auto !important;
  top: 50%;
  bottom: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.blog-slider .blog-slider__pagination.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 8px 0;
}
.blog-slider__pagination .swiper-pagination-bullet {
  width: 11px;
  height: 11px;
  display: block;
  border-radius: 10px;
  background: #858585;
  opacity: 0.2;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -ms-transition: all 0.3s;
  transition: all 0.3s;
}
.blog-slider__pagination .swiper-pagination-bullet-active {
  opacity: 1;
  -ms-filter: none;
  filter: none;
  background: var(--btn-bg);
  height: 30px;
}
@media screen and (max-width: 768px) {
  .blog-slider__pagination {
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    left: 50% !important;
    top: 320px;
    width: 100% !important;
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: box;
    display: flex;
    -webkit-box-pack: center;
    -moz-box-pack: center;
    -o-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -moz-box-align: center;
    -o-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
  }
  .blog-slider .blog-slider__pagination.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 5px;
  }
  .blog-slider__pagination .swiper-pagination-bullet-active {
    height: 11px;
    width: 30px;
  }
  .blog-slider__button {
    display: -webkit-inline-box;
    display: -moz-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-box;
    display: inline-flex;
    width: 100%;
  }
  .blog-slider {
    min-height: 350px;
    height: auto;
    margin-bottom: 10px;
  }
  .blog-slider__content {
    text-align: center;
    padding: 0 30px;
  }
  .blog-slider__item {
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    -o-box-orient: vertical;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
  .blog-slider__img {
    width: 100%;
    padding-top: 0;
  }
  .blog-slider__content {
    padding-left: 10px;
    padding-right: 10px;
    margin-top: 15px;
  }
  .blog-slider__pagination.swiper-pagination-clickable.swiper-pagination-bullets {
    top: 218px;
  }

  .swiper_container_card #swiper_container_blog div#swiper_container.blog-slider {
    padding: 10px;
    overflow: hidden;
  }
  .blog-slider__item .blog-slider__content .blog-slider__code {
    margin-top: 10px;
  }
  .blog-slider__title {
    margin-bottom: 0;
  }
}
@media screen and (min-width: 768px) {
  .blog-slider {
    height: 200px;
  }
  .blog-slider__img {
    height: 200px;
  }
}

@media screen and (max-width: 1200px) {
  div#swiper_container {
    width: 100%;
  }
  #swiper_container_blog {
    width: 99%;
  }
}

.swiper_container_card {
  display: flex !important;
  justify-content: space-around;
  flex-direction: row !important;
  user-select: none;
}

/* rightMenu右键菜单 */
#rightMenu {
  display: none;
  position: fixed;
  padding: 0 0.25rem;
  width: 11rem;
  height: fit-content;
  top: 10%;
  left: 10%;
  background-color: var(--anzhiyu-card-bg);
  color: var(--anzhiyu-fontcolor);
  border-radius: 12px;
  z-index: 10002;
  border: var(--style-border);
  user-select: none;
}
#rightMenu a {
  color: var(--anzhiyu-fontcolor);
}
#rightMenu:hover {
  border: var(--style-border-hover);
  box-shadow: var(--anzhiyu-shadow-theme);
}
#rightMenu .rightMenu-group {
  padding: 0.35rem 0.3rem;
}
#rightMenu .rightMenu-group:not(:nth-last-child(1)) {
  border-bottom: 2px dashed var(--anzhiyu-theme-op);
}
#rightMenu .rightMenu-group.rightMenu-small {
  display: flex;
  justify-content: space-between;
}
#rightMenu .rightMenu-group .rightMenu-item {
  border-radius: 8px;
  transition: 0.1s;
  cursor: pointer;
  padding: 0 6px;
}

#rightMenu .rightMenu-line .rightMenu-item {
  margin: 0.25rem 0;
  padding: 0.25rem 0;
}
#rightMenu .rightMenu-group.rightMenu-line .rightMenu-item {
  display: flex;
  align-items: center;
  padding: 8px;
}

#rightMenu .rightMenu-group .rightMenu-item:hover {
  background-color: var(--anzhiyu-main);
  color: var(--anzhiyu-white);
  box-shadow: var(--anzhiyu-shadow-theme);
}

#rightMenu .rightMenu-group .rightMenu-item i {
  display: inline-block;
  text-align: center;
  line-height: 1.5rem;
  width: 1.5rem;
  padding: 0 0.25rem;
  font-size: 1.1rem;
}
#rightMenu .rightMenu-line .rightMenu-item i {
  margin: 0 0.5rem 0 0.25rem;
}
#rightMenu .rightMenu-group .rightMenu-item span {
  line-height: 1.5rem;
}
#rightmenu-mask {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background: none;
  /* background: var(--anzhiyu-maskbg); */
  top: 0;
  left: 0;
  display: none;
  z-index: 10001;
  margin: 0 !important;
}

@media screen and (max-width: 768px) {
  :root {
    --style-border: 1px solid var(--anzhiyu-card-border);
    --style-border-hover: 1px solid var(--anzhiyu-theme);
  }
}

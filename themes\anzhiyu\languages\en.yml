footer:
  framework: Framework
  theme: Theme

copy:
  success: Copy successfully
  error: Copy error
  noSupport: The browser does not support

page:
  articles: Articles
  tag: Tag
  category: Category
  archives: Archives

card_post_count: comments

sticky: Sticky
no_title: No title

post:
  created: Created
  updated: Updated
  wordcount: Word count
  min2read: Reading time
  min2read_unit: min
  page_pv: Post View
  comments: Comments
  copyright:
    author: Author
    link: Link
    copyright_notice: Copyright Notice
    copyright_content: 'All articles in this blog are licensed under <a href="%s">%s</a> unless stating additionally.'
  recommend: Related Articles
  edit:
    github: Edited on GitHub
    yuque: Edited on Yuque

search:
  title: Search
  load_data: Loading the Database
  algolia_search:
    input_placeholder: Search for Posts
    hits_empty: "We didn't find any results for the search: ${query}."
    hits_stats: "${hits} results found in ${time} ms"

  local_search:
    input_placeholder: Search for Posts
    hits_empty: "We didn't find any results for the search: ${query}"

pagination:
  prev: Previous Post
  next: Next Post

comment: Comment

aside:
  articles: Articles
  tags: Tags
  categories: Categories
  card_announcement: Announcement
  card_categories: Categories
  card_tags: Tags
  card_archives: Archives
  card_recent_post: Recent Post
  card_webinfo:
    headline: Info
    article_name: Article
    runtime:
      name: Run time
      unit: days
    last_push_date:
      name: Last Push
    site_wordcount: Total Count
    site_uv_name: UV
    site_pv_name: PV
  more_button: More
  card_newest_comments:
    headline: Newest Comments
    loading_text: loading...
    error: Unable to get the data, please make sure the settings are correct.
    zero: No Comment
    image: image
    link: link
    code: code
  card_toc: Catalog
  display_mode: Display Mode
  function: Function

date_suffix:
  just: Just
  min: minutes ago
  hour: hours ago
  day: days ago
  month: months ago

donate: Donate
share: Share

rightside:
  readmode_title: Read Mode
  translate_title: Switch Between Traditional Chinese And Simplified Chinese
  night_mode_title: Switch Between Light And Dark Mode
  back_to_top: Back To Top
  toc: Table Of Contents
  scroll_to_comment: Scroll To Comments
  setting: Setting
  aside: Toggle between single-column and double-column
  chat: Chat

copy_copyright:
  author: Author
  link: Link
  source: Source
  info: Copyright is owned by the author. For commercial reprints, please contact the author for authorization. For non-commercial reprints, please indicate the source.
  copySuccess: Copy success, copy and reprint please mark the address of this article

Snackbar:
  chs_to_cht: Traditional Chinese Activated Manually
  cht_to_chs: Simplified Chinese Activated Manually
  day_to_night: Dark Mode Activated Manually
  night_to_day: Light Mode Activated Manually
  copy_equipment_name: Installed device name has been loaded

loading: Loading...

load_more: Load More

error404: Page not found

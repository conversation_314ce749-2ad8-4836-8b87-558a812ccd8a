{"name": "hexo-renderer-ejs", "version": "2.0.0", "description": "EJS renderer plugin for Hexo", "main": "index", "scripts": {"eslint": "eslint .", "test": "mocha test/index.js", "test-cov": "nyc --reporter=lcovonly npm run test"}, "directories": {"lib": "./lib"}, "files": ["index.js", "lib/"], "repository": "hexojs/hexo-renderer-ejs", "keywords": ["hexo", "ejs", "renderer"], "author": "<PERSON> <<EMAIL>> (http://zespia.tw)", "maintainers": ["<PERSON><PERSON> <<EMAIL>> (http://abnerchou.me)"], "license": "MIT", "dependencies": {"ejs": "^3.1.6"}, "devDependencies": {"chai": "^4.3.4", "eslint": "^8.1.0", "eslint-config-hexo": "^4.2.0", "hexo-fs": "^3.1.0", "mocha": "^9.1.0", "nyc": "^15.1.0"}, "engines": {"node": ">=12"}}
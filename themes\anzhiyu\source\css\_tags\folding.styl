trans($time = 0.28s)
  transition: all $time ease
  -moz-transition: all $time ease
  -webkit-transition: all $time ease
  -o-transition: all $time ease
bgcolor($c, $mix = 10)
  return mix($c, #fff, $mix)
details[open]:not(.tk-admin-config-group) {
  background: var(--anzhiyu-card-bg);
}
[data-theme="dark"]
  details.folding-tag
   background: transparent
details.folding-tag
  display: block
  padding: 16px
  margin: 1em 0
  border-radius: 4px
  font-size: var(--global-font-size)
  trans()
  summary
    cursor: pointer
    padding: 16px
    margin: 0 - 16px
    border-radius: 4px
    color: alpha(#444, .7)
    font-size: .875rem!important
    font-weight: bold
    position: relative
    line-height: normal
    >
      p,h1,h2,h3,h4,h5,h6
        display: inline
        border-bottom: none !important
    &:hover
      color: #444
      &:after
        position: absolute
        content: '+'
        text-align: center
        top: 50%
        transform: translateY(-50%)
        right: 16px

  border: 1px solid #f6f6f6
  >summary
    background: var(--anzhiyu-card-bg)
  &[purple]
    border-color: bgcolor(#d017ee)
    >summary
      background: bgcolor(#d017ee)
  &[blue]
    border-color: bgcolor(#2196f3)
    >summary
      background: bgcolor(#2196f3)
  &[cyan]
    border-color: bgcolor(#1BCDFC)
    >summary
      background: bgcolor(#1BCDFC)
  &[green]
    border-color: bgcolor(#3DC550)
    >summary
      background: bgcolor(#3DC550)
  &[yellow]
    border-color: bgcolor(#FFBD2B)
    >summary
      background: bgcolor(#FFBD2B)
  &[orange]
    border-color: bgcolor(#ec7616)
    >summary
      background: bgcolor(#ec7616)
  &[red]
    border-color: bgcolor(#FE5F58)
    >summary
      background: bgcolor(#FE5F58)

details.folding-tag[open]
  border-color: alpha(#444, .2)
  >summary
    border-bottom: 1px solid alpha(#444, .2)
    border-bottom-left-radius: 0
    border-bottom-right-radius: 0
  &[purple]
    border-color: alpha(#d017ee, .3)
    >summary
      border-bottom-color: alpha(#d017ee, .3)
  &[blue]
    border-color: alpha(#2196f3, .3)
    >summary
      border-bottom-color: alpha(#2196f3, .3)
  &[cyan]
    border-color: alpha(#1BCDFC, .3)
    >summary
      border-bottom-color: alpha(#1BCDFC, .3)
  &[green]
    border-color: alpha(#3DC550, .3)
    >summary
      border-bottom-color: alpha(#3DC550, .3)
  &[yellow]
    border-color: alpha(#FFBD2B, .3)
    >summary
      border-bottom-color: alpha(#FFBD2B, .3)
  &[orange]
    border-color: alpha(#ec7616, .3)
    >summary
      border-bottom-color: alpha(#ec7616, .3)
  &[red]
    border-color: alpha(#FE5F58, .3)
    >summary
      border-bottom-color: alpha(#FE5F58, .3)
  >summary
    color: #444
    margin-bottom: 0
    &:hover
      &:after
        content: '-'
  >div.content
    padding: 16px
    margin: 0 - 16px
    margin-top: 0
    p>a:hover
      text-decoration: underline
    >
      p,.tabs,ul,ol,.highlight,.note,details
        &:first-child
          margin-top: 0
        &:last-child
          margin-bottom: 0
[data-theme="dark"]
  details[open]
    & > div
      &.content
        padding 16px
        margin -16px
        margin-top 0
        background: transparent;
        color rgba(255, 255, 255, 0.6)
  details
    & > summary
      filter brightness(1)
      background: var(--anzhiyu-card-bg) !important;

{"author": "planttheidea", "browser": "dist/moize.js", "browserslist": ["defaults", "Explorer >= 9", "Safari >= 6", "Opera >= 15", "iOS >= 8", "Android >= 4"], "bugs": {"url": "https://github.com/planttheidea/moize/issues"}, "dependencies": {"fast-equals": "^3.0.1", "micro-memoize": "^4.1.2"}, "description": "Blazing fast memoization based on all parameters passed", "devDependencies": {"@babel/cli": "^7.21.5", "@babel/core": "^7.21.8", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.21.5", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.5", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-terser": "^0.4.1", "@types/bluebird": "^3.5.38", "@types/eslint": "^8.37.0", "@types/jest": "^29.5.1", "@types/lodash": "^4.14.194", "@types/memoizee": "^0.4.8", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "babel-jest": "^29.5.0", "babel-loader": "^9.1.2", "benchmark": "^2.1.4", "bluebird": "^3.7.2", "cli-table2": "^0.2.0", "core-js": "^3.30.2", "eslint": "^8.40.0", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-react": "^7.32.2", "eslint-webpack-plugin": "^4.0.1", "fast-memoize": "^2.5.2", "html-webpack-plugin": "^5.5.1", "in-publish": "^2.0.1", "ink-docstrap": "^1.3.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jsdoc": "^4.0.2", "jsdoc-babel": "^0.5.0", "lodash": "^4.17.21", "lru-memoize": "^1.1.0", "mem": "^8.1.1", "memoizee": "^0.4.15", "memoizerific": "^1.11.3", "ora": "^5.4.1", "prop-types": "^15.8.1", "q": "^1.5.1", "ramda": "^0.29.0", "react": "^18.2.0", "react-dom": "^18.2.0", "regenerator-runtime": "^0.13.11", "release-it": "^15.10.3", "rimraf": "^5.0.0", "rollup": "^3.21.5", "tslib": "^2.5.0", "typedoc": "^0.24.7", "typescript": "^5.0.4", "underscore": "^1.13.6", "webpack": "^5.82.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "homepage": "https://github.com/planttheidea/moize#readme", "keywords": ["cache", "expire", "lru", "memoize", "memoization", "optimize", "performance", "promise", "ttl"], "license": "MIT", "main": "dist/moize.cjs.js", "module": "dist/moize.esm.js", "name": "moize", "repository": {"type": "git", "url": "git+https://github.com/planttheidea/moize.git"}, "scripts": {"benchmark": "npm run dist && node benchmark/index.js", "benchmark:alternative": "npm run transpile:lib -- --no-comments && BENCHMARK_SUITE=alternative node benchmark/index.js", "benchmark:array": "npm run transpile:lib -- --no-comments && BENCHMARK_SUITE=array node benchmark/index.js", "benchmark:object": "npm run transpile:lib -- --no-comments && BENCHMARK_SUITE=object node benchmark/index.js", "benchmark:primitive": "npm run transpile:lib -- --no-comments && BENCHMARK_SUITE=primitive node benchmark/index.js", "benchmark:react": "npm run transpile:lib -- --no-comments && BENCHMARK_SUITE=react node benchmark/index.js", "build": "NODE_ENV=production rollup -c --bundleConfigAsCjs", "clean:dist": "<PERSON><PERSON><PERSON> dist", "clean:docs": "<PERSON><PERSON><PERSON> docs", "clean:mjs": "<PERSON><PERSON><PERSON> mjs", "copy:mjs": "npm run clean:mjs && node ./es-to-mjs.js", "copy:types": "cp src/types.ts index.d.ts", "dev": "NODE_ENV=development webpack serve --progress --config=webpack/webpack.config.js", "dist": "npm run clean:dist && npm run build", "docs": "npm run clean:docs && typedoc", "lint": "NODE_ENV=test eslint src/*.ts", "lint:fix": "npm run lint -- --fix", "release": "release-it", "release:beta": "release-it --config=.release-it.beta.json", "release:scripts": "npm run lint && npm run typecheck && npm run test:coverage && npm run dist && npm run copy:mjs", "start": "npm run dev", "test": "NODE_ENV=test NODE_PATH=. jest", "test:coverage": "npm test -- --coverage", "test:watch": "npm test -- --watch", "typecheck": "tsc --noEmit"}, "sideEffects": false, "types": "./index.d.ts", "version": "6.1.6"}
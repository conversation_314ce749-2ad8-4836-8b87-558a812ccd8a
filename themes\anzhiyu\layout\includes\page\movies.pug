#movies
  if site.data.movies
    each i in site.data.movies
      .author-content.author-content-item.moviesPage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          .banner-button-group
            a.banner-button(href=i.buttonLink)
              i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
              span.banner-button-text=i.buttonText
      each item in i.good_movies
        .goodmovies-item
          h2.goodmovies-title= item.title
          .goodmovies-item-description= item.description
          .movies-item
            .movies-item-content
              each iten, indey in item.movies_list
                .movies-item-content-item(data-index=indey)
                  .movies-item-content-item-cover
                    a(href=iten.link target="_blank" class="movie-link")
                      img.movies-item-content-item-image(
                        data-lazy-src=url_for(iten.image)
                        onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'`
                        alt=iten.name
                        class="movie-image"
                      )
                  .movies-item-content-item-info
                    .movies-item-content-item-name(onclick=`rm.rightmenuCopyText("${iten.name}");anzhiyu.snackbarShow("${_p('已复制影视名：') + " 【" + iten.name + "】"}");` title=iten.name)= iten.name
                    .movies-item-content-item-specification
                      if iten.year
                        span.year= iten.year + " / "
                      if iten.genre
                        span.genre= iten.genre + " / "
                      if iten.rating
                        span.rating= iten.rating
                      if iten.watch_date
                        span.watch-date= " / " + iten.watch_date
                    .movies-item-content-item-description= iten.description
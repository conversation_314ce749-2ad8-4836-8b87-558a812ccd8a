extends includes/layout.pug

block content
  //- #page(data-type=page.type)
  #page
    if top_img === false && !page.top_single
      h1.page-title= page.title
    case page.type
      when 'tags'
        include includes/page/tags.pug
      when 'link'
        include includes/page/flink.pug
      when 'categories'
        include includes/page/categories.pug
      when 'essay'
        include includes/page/essay.pug
      when 'room'
        include includes/page/room.pug
      when 'about'
        include includes/page/about.pug
      when 'album'
        include includes/page/album.pug
      when 'fcircle'
        include includes/page/fcircle.pug
      when 'album_detail'
        include includes/page/album_detail.pug
      when 'music'
        include includes/page/music.pug
      when 'books'
        include includes/page/books.pug
      when 'games'
        include includes/page/games.pug
      when 'equipment'
        include includes/page/equipment.pug
      when 'movies'
        include includes/page/movies.pug
      when 'musics'
        include includes/page/musics.pug
      when 'update'
        include includes/page/update.pug
      when 'finance'
        include includes/page/finance.pug
      when 'bookmark'
        include includes/page/bookmark.pug
      when 'attendance'
        include includes/page/attendance.pug
      default
        include includes/page/default-page.pug

    if page.comments !== false && theme.comments && theme.comments.use
      - var commentsJsLoad = true
      !=partial('includes/third-party/comments/index', {}, {cache: true})
{"author": "<EMAIL>", "browser": "dist/micro-memoize.js", "browserslist": ["defaults", "Explorer >= 9", "Safari >= 6", "Opera >= 15", "iOS >= 8", "Android >= 4"], "bugs": {"url": "https://github.com/planttheidea/micro-memoize/issues"}, "description": "A tiny, crazy fast memoization library for the 95% use-case", "devDependencies": {"@rollup/plugin-terser": "^0.4.1", "@rollup/plugin-typescript": "^11.1.0", "@types/bluebird": "^3.5.38", "@types/jest": "^29.5.1", "@types/react": "^18.2.6", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "benchee": "^1.1.0", "benchmark": "^2.1.4", "bluebird": "^3.7.2", "cli-table2": "^0.2.0", "eslint": "^8.40.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-react": "^7.32.2", "eslint-webpack-plugin": "^4.0.1", "fast-equals": "^5.0.1", "fast-memoize": "^2.5.2", "html-webpack-plugin": "^5.5.1", "in-publish": "^2.0.1", "jest": "^29.5.0", "lodash": "^4.17.21", "lru-memoize": "^1.1.0", "mem": "^8.1.1", "memoizee": "^0.4.15", "memoizerific": "^1.11.3", "mini-bench": "^1.0.0", "ora": "^6.3.0", "performance-now": "^2.1.0", "ramda": "^0.29.0", "react": "^18.2.0", "release-it": "^15.10.3", "rollup": "^3.21.5", "rsvp": "^4.8.5", "simple-statistics": "^7.8.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.2", "tslib": "^2.5.0", "typescript": "^5.0.4", "underscore": "^1.13.6", "webpack": "^5.82.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "homepage": "https://github.com/planttheidea/micro-memoize#readme", "keywords": ["memoize", "memoized", "memoizer", "memoization", "memo", "cache", "cached", "storage", "memory", "optimize", "performance", "lru", "expire"], "license": "MIT", "main": "dist/micro-memoize.cjs.js", "module": "dist/micro-memoize.esm.js", "name": "micro-memoize", "repository": {"type": "git", "url": "git+https://github.com/planttheidea/micro-memoize.git"}, "scripts": {"benchmark": "npm run dist && NODE_ENV=production node ./benchmarks/index.cjs", "build": "NODE_ENV=production rollup -c --bundleConfigAsCjs", "build:mjs": "node ./es-to-mjs.js", "clean": "rimraf dist && rimraf mjs", "dev": "NODE_ENV=development webpack-dev-server --config=webpack/webpack.config.js", "dist": "npm run clean && npm run build && npm run build:mjs", "lint": "NODE_ENV=test eslint src/*.ts", "lint:fix": "npm run lint -- --fix", "prepublish": "if in-publish; then npm run prepublish:compile; fi", "prepublish:compile": "npm run typecheck && npm run lint && npm run test:coverage && npm run dist", "release": "release-it", "release:beta": "release-it --config=.release-it.beta.json", "start": "npm run dev", "test": "NODE_PATH=. jest", "test:coverage": "npm run test -- --coverage", "test:watch": "npm run test -- --watch", "typecheck": "tsc --noEmit"}, "sideEffects": false, "types": "./index.d.ts", "version": "4.1.2"}
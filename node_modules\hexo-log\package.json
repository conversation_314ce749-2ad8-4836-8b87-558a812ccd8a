{"name": "hexo-log", "version": "4.1.0", "description": "Logger for Hexo", "main": "dist/log.js", "scripts": {"prepublish ": "npm run clean && npm run build", "build": "tsc -b", "clean": "tsc -b --clean", "eslint": "eslint .", "pretest": "npm run clean && npm run build", "test": "mocha test/index.js --require ts-node/register", "test-cov": "nyc --reporter=lcovonly npm test"}, "files": ["dist/**"], "types": "./dist/log.d.ts", "repository": "hexojs/hexo-log", "homepage": "https://hexo.io/", "keywords": ["website", "blog", "cms", "framework", "hexo"], "author": "<PERSON> <<EMAIL>> (https://zespia.tw)", "maintainers": ["<PERSON><PERSON> <<EMAIL>> (https://abnerchou.me)"], "license": "MIT", "devDependencies": {"@types/node": "^18.7.18", "@types/rewire": "^2.5.28", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "chai": "^4.1.2", "eslint": "^8.23.1", "eslint-config-hexo": "^5.0.0", "mocha": "^10.0.0", "nyc": "^15.0.0", "rewire": "^6.0.0", "sinon": "^15.0.2", "ts-node": "^10.9.1", "typescript": "^5.0.3"}, "engines": {"node": ">=14"}, "dependencies": {"picocolors": "^1.0.0"}}
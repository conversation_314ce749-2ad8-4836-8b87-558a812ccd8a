<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"><title>2024年10月10日-流量卡选购指南 | 我的博客</title><meta name="keywords" content="日常,指南"><meta name="author" content="c2hapmll"><meta name="copyright" content="c2hapmll"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f7f9fe"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="2024年10月10日-流量卡选购指南"><meta name="application-name" content="2024年10月10日-流量卡选购指南"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f7f9fe"><meta property="og:type" content="article"><meta property="og:title" content="2024年10月10日-流量卡选购指南"><meta property="og:url" content="https://shijie.icu/post/20241010144421.html"><meta property="og:site_name" content="我的博客"><meta property="og:description" content="注意，2025年2月中旬起，好像各大运营商又出比较高质量的流量卡套餐了，各位可以去瞅瞅。   本文仅作为科普文章，本人不参与其中任何交易环节，如果有任何问题都可以通过社交软件联系我，我非常乐意提供力所能及的帮助。   非常遗憾的是，截止到本文的写作开始，最近一次的三网竞合已经结束，现在已经选购不到"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://shijie.icu/images/posts/2024101001/top.jpg"><meta property="article:author" content="c2hapmll"><meta property="article:tag"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://shijie.icu/images/posts/2024101001/top.jpg"><meta name="description" content="注意，2025年2月中旬起，好像各大运营商又出比较高质量的流量卡套餐了，各位可以去瞅瞅。   本文仅作为科普文章，本人不参与其中任何交易环节，如果有任何问题都可以通过社交软件联系我，我非常乐意提供力所能及的帮助。   非常遗憾的是，截止到本文的写作开始，最近一次的三网竞合已经结束，现在已经选购不到"><link rel="shortcut icon" href="/images/themes/32.png"><link rel="canonical" href="https://shijie.icu/post/20241010144421.html"><link rel="preconnect" href="//cdn.cbd.int"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><meta name="google-site-verification" content="xxx"/><meta name="baidu-site-verification" content="code-xxx"/><meta name="msvalidate.01" content="xxx"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.cbd.int/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media='all'"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media='all'"><script>const GLOBAL_CONFIG = {
  linkPageTop: undefined,
  peoplecanvas: undefined,
  postHeadAiDescription: undefined,
  diytitle: undefined,
  LA51: undefined,
  greetingBox: undefined,
  twikooEnvId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
  commentBarrageConfig:undefined,
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: false,
  mainTone: undefined,
  authorStatus: {"skills":["🤖️ 数码科技爱好者","🔍 分享与热心帮助","🏠 智能家居小能手"]},
  algolia: undefined,
  localSearch: {"path":"/search.xml","preload":true,"languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: undefined,
  highlight: {"plugin":"prismjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    simplehomepage: true,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: c2hapmll","link":"链接: ","source":"来源: 我的博客","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: false,
  shortcutKey: undefined,
  autoDarkmode: true
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  configTitle: '我的博客',
  title: '2024年10月10日-流量卡选购指南',
  postAI: '',
  pageFillDescription: '当前情况, 流量卡, 认识流量卡, 历史, 现状, 选购对比, 例子1：移动沧蕾卡, 例子2：电信沧欢卡, 例子3：联通长期卡, 购买方式, 注意事项注意年月中旬起好像各大运营商又出比较高质量的流量卡套餐了各位可以去瞅瞅本文仅作为科普文章本人不参与其中任何交易环节如果有任何问题都可以通过社交软件联系我我非常乐意提供力所能及的帮助非常遗憾的是截止到本文的写作开始最近一次的三网竞合已经结束现在已经选购不到我副卡的这种套餐了现在大都是元流量的套餐优惠力度已然大减当前情况我总共拥有张卡每张卡都有不同的用途并且每张卡都是正规运营商的卡且和身份信息绑定不存在违规违法的情况主卡联通卡月租元早先申请的联通大王卡后来加了一张流量卡就把这张主卡的套餐更换为了联通元保号套餐是联系我的最终方式其中绑定了许多网站银行基本无法舍弃副卡电信流量卡月租元是前几年申请的湖北流量卡主套餐是元星卡但是叠加了的全国流量和的定向流量且可以参与充反每月反的活动活动返完可继续参与所以综合月租是元是我没有的情况下主要的上网方式实际上每个月根本用不了这么多宽带卡移动卡月租元月租元元千兆提速是我来到苏州时办理的宽带卡家里必备的宽带这个不可省略本来是想看看主卡和副卡能不能办理跨省宽带的但是不太划算英国卡无月租每半年需要变动一次余额才能保号主要用来注册国外的网站和服务例如等等有些国家和地区是把的号码排除在外的国内的手机不能注册和使用总共的月租是元感觉确实有点高了准备过段时间把宽带的资费降一降流量卡首先明确一件事物联卡有时也被叫做流量卡但是物联卡和流量卡确实不是同一种你在网购平台搜索流量卡可能会遇到一些标为流量卡但是实际是物联卡的情况要仔细辨别认识流量卡历史早先在三网竞合还未开始之前移动联通电信为了抢占用户推出了许多月租极低流量极多的套餐搭载了这些性价比极高套餐的卡被叫做流量卡前几年有些无限流量卡就是这么出来的这些流量卡一般是用到就降速到但是随着时代发展运营商发现这些套餐非常不划算于是三家收紧策略统一不再推出无限流量卡套餐直到无限流量套餐逐渐消失的无影无踪你现在去贴吧还能看到有人高价收购带有这些无限套餐的流量卡不记得多少年前三网各个省份的运营商还有着自己独立的后来也逐渐整合到一个就是我们现在用的中国移动中国电信中国联通但还是能在应用内选择地区运营商你地区选苏州就是国内服务地区运营商的活动现状同样现在各省份地区运营商为了新用户会推出一些优惠套餐一般是星卡主套餐元叠加包叠加包内容一般是赠送例如我的副卡流量卡是被地区运营商叫做阳光卡套餐我在上面也有做介绍这种流量卡是正规运营商的卡在对应的运营商中能够查询套餐信息任意平台都能够充值话费根本的说就是一张普普通通的卡选购对比现在的流量卡又分为三种一种是短期卡一种是长期卡最后一种是短期可续长期但是续约定义比较模糊的这里的短期和长期不是指的使用期限而是指套餐的优惠长短有些套餐是长期优惠优惠时间可达年年年有些套餐是短期优惠优惠时间短则个月长则年下面我会用几个例子做对比例子移动沧蕾卡月租元首月免月租原套餐通用流量定向流量优惠详情任意渠道首充元可享受优惠活动叠加每月通用流量优惠期个月到期无异议可续禁发北京云南西藏黑龙江例子电信沧欢卡月租元原套餐通用流量定向流量激活赠送元体验金体验金不可结转首月免月租优惠详情快递处或指定链接首充元可享受优惠活动充送即刻到账分月返还每月反元每月赠送通用流量叠加包可参加两次店铺叠加优惠参加首充活动后个月月底反元第月再次参加送后第月反月反元综上所述首月免月租月元每月月元每月月元每月月后元每月禁发北京云南西藏黑龙江例子联通长期卡月租元原套餐通用流量定向流量优惠详情任意渠道首充元可享受优惠活动叠加每月通用流量有效期至年月日到期自动续约禁发北京云南西藏黑龙江我们逐个分析首先要确认优惠时长例子明显算是短转长这个到期无异议可续是运营商的补充条款所以含有此种类似描述的也有概率不可续约例子明显是两年短期套餐这种套餐一般都是优惠比较大但是两年后就必须注销更换比较麻烦否则优惠结束非常不划算例子是最容易懂的长期套餐了这种标识套餐有效期到年年年的一般都是长期套餐这种就无需担忧运营商续约的情况了这三种套餐我最不推荐例子的短期套餐到优惠结束还要注销费时费力但这个就见仁见智各自选择了购买方式由前文我们可以知道流量卡一般是地区运营商为了拉新推出的优惠套餐这种拉新一般都存在代理架构我们在淘宝拼多多各大短视频长视频网站上能够搜索到店铺的一般都是区域运营商下的代理商代理商也有不同的级别划分各家代理销售的流量卡都有返利不同级别的代理返利不同例如你在这个代理商这里下单了例子的长期卡这个卡你拿到后激活并首充这个代理如果是黑钻级别返利就是元现金而你在各个平台上下单有时还要收你一笔钱这是不是很不可思议代理能够两头赚就是源于信息差一级代理商一般是以公司为代表的平台机构例如号卡平台仅做科普不含任何链接推广不含任何利益相关请注意辨别官方平台真假例子的长期卡就是我在里面摘抄的资料但是该平台不接受个人的一级代理我在一个好友那里拿到了他的下级黑钻代理可以直接看到例子的长期卡的返利是元激活现结综上所述如果你真的很想要一张流量卡作为副卡那么完全可以自己做代理然后自己办卡拿自己的返利左右的返利相当于个月的月租了并且如果有首充赠的话相当于白嫖运营商一年还多了如果你真的很懒不想这么麻烦可以按照选购对比中的方式找到你想要的套餐直接在代理商下单即可一般是免费的也不排除有黑心商家想两头赚再收你一笔注意事项流量卡套餐优先选择限制较小的使用期限长的广电的流量卡谨慎选择优先选择移动联通电信有些卡有注销限制尽量选择能够在线注销的尽管都可以停机注销下单尽量选择粉丝多的博主或者销量高的店铺少一点套路多一点真诚激活时要严格按照当时选购时的首充要求否则可能没有优惠',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2025-02-17 15:03:54',
  postMainColor: '',
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#18171d')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f7f9fe')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/costom_widget.css"><link rel="stylesheet" href="/css/badge.css"><meta name="generator" content="Hexo 7.3.0"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="/images/themes/32.png"/><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"/><script async="async" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><a id="site-name" href="/" accesskey="h"><div class="title">我的博客</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"/><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole();"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" alt="微信" src="/images/themes/reward/wechat.jpg"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="/images/themes/reward/alipay.jpg"/></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title"> 最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 1.05rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 1.05rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 1.05rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 1.05rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 1.05rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 1.05rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 1.05rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 1.05rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 1.05rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/02/"><span class="card-archive-list-date">二月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/12/"><span class="card-archive-list-date">十二月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/11/"><span class="card-archive-list-date">十一月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/10/"><span class="card-archive-list-date">十月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2024/09/"><span class="card-archive-list-date">九月 2024</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">1</span><span>篇</span></div></a></li></ul></div><hr/></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%97%A5%E5%B8%B8/" itemprop="url">日常</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E6%97%A5%E5%B8%B8/" tabindex="-1" itemprop="url"> <span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>日常</span></a><a class="article-meta__tags" href="/tags/%E6%8C%87%E5%8D%97/" tabindex="-1" itemprop="url"> <span> <i class="anzhiyufont anzhiyu-icon-hashtag"></i>指南</span></a></span></div></div><h1 class="post-title" itemprop="name headline">2024年10月10日-流量卡选购指南</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2024-10-10T06:44:21.000Z" title="发表于 2024-10-10 14:44:21">2024-10-10</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-02-17T07:03:54.000Z" title="更新于 2025-02-17 15:03:54">2025-02-17</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">2.4k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>7分钟</span></span><span class="post-meta-separator"></span><span class="post-meta-pv-cv" id="" data-flag-title="2024年10月10日-流量卡选购指南"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="busuanzi_value_page_pv"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator">       </span><span class="post-meta-position" title="作者IP属地为苏州"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>苏州</span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="/images/posts/2024101001/top.jpg"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container" itemscope itemtype="https://shijie.icu/post/20241010144421.html"><header><a class="post-meta-categories" href="/categories/%E6%97%A5%E5%B8%B8/" itemprop="url">日常</a><a href="/tags/%E6%97%A5%E5%B8%B8/" tabindex="-1" itemprop="url">日常</a><a href="/tags/%E6%8C%87%E5%8D%97/" tabindex="-1" itemprop="url">指南</a><h1 id="CrawlerTitle" itemprop="name headline">2024年10月10日-流量卡选购指南</h1><span itemprop="author" itemscope itemtype="http://schema.org/Person">c2hapmll</span><time itemprop="dateCreated datePublished" datetime="2024-10-10T06:44:21.000Z" title="发表于 2024-10-10 14:44:21">2024-10-10</time><time itemprop="dateCreated datePublished" datetime="2025-02-17T07:03:54.000Z" title="更新于 2025-02-17 15:03:54">2025-02-17</time></header><blockquote>
<p>注意，2025年2月中旬起，好像各大运营商又出比较高质量的流量卡套餐了，各位可以去瞅瞅。</p>
</blockquote>
<blockquote>
<p>本文仅作为科普文章，本人不参与其中任何交易环节，如果有任何问题都可以通过社交软件联系我，我非常乐意提供力所能及的帮助。</p>
</blockquote>
<blockquote>
<p>非常遗憾的是，截止到本文的写作开始，最近一次的三网竞合已经结束，现在已经选购不到我副卡2的这种套餐了，现在大都是29元80G流量的套餐，优惠力度已然大减。</p>
</blockquote>
<h2 id="当前情况"><a href="#当前情况" class="headerlink" title="当前情况"></a>当前情况</h2><p>我总共拥有4张SIM卡，每张卡都有不同的用途，并且每张卡都是正规运营商的卡，且和身份信息绑定，不存在违规违法的情况。</p>
<ol>
<li><p>主卡联通卡1：月租8元，早先申请的联通大王卡，后来加了一张流量卡就把这张主卡的套餐更换为了联通8元保号套餐。是联系我的最终方式，其中绑定了许多网站、银行，基本无法舍弃。</p>
</li>
<li><p>副卡电信流量卡2：月租19元，是前几年申请的湖北流量卡，主套餐是29元星卡，但是叠加了125G的全国流量和30G的定向流量，且可以参与充100反220（每月反10）的活动，活动返完可继续参与，所以综合月租是19元。是我没有WIFI的情况下主要的上网方式，实际上每个月根本用不了这么多。</p>
</li>
<li><p>宽带卡移动卡3：月租33元，8月租+15元500M+10元千兆提速，是我来到苏州时办理的宽带卡，家里必备的宽带，这个不可省略，本来是想看看主卡1和副卡2能不能办理跨省宽带的，但是不太划算。</p>
</li>
<li><p>英国Giffgaff卡4：无月租，每半年需要变动一次余额才能保号，主要用来注册国外的网站和服务，例如chatgpt、github等等，有些国家和地区是把+86的号码排除在外的，国内的手机不能注册和使用。</p>
</li>
</ol>
<p>总共的月租是8+19+33&#x3D;60元，感觉确实有点高了，准备过段时间把宽带的资费降一降。</p>
<h2 id="流量卡"><a href="#流量卡" class="headerlink" title="流量卡"></a>流量卡</h2><p>首先明确一件事，物联卡有时也被叫做流量卡，但是物联卡和流量卡确实不是同一种，你在网购平台搜索流量卡可能会遇到一些标为流量卡但是实际是物联卡的情况，要仔细辨别。</p>
<h3 id="认识流量卡"><a href="#认识流量卡" class="headerlink" title="认识流量卡"></a>认识流量卡</h3><h4 id="历史"><a href="#历史" class="headerlink" title="历史"></a>历史</h4><p>早先在三网竞合还未开始之前，移动、联通、电信为了抢占用户，推出了许多月租极低、流量极多的套餐，搭载了这些性价比极高套餐的SIM卡被叫做流量卡。</p>
<p>前几年有些无限流量卡就是这么出来的，这些流量卡一般是用到40G就降速到128k，但是随着时代发展运营商发现这些套餐非常不划算，于是三家收紧策略，统一不再推出无限流量卡套餐，直到无限流量套餐逐渐消失的无影无踪，你现在去贴吧还能看到有人高价收购带有这些无限套餐的流量卡。</p>
<p>不记得多少年前，三网各个省份的运营商还有着自己独立的App，后来也逐渐整合到一个App，就是我们现在用的中国移动App、中国电信App、中国联通App，但还是能在应用内选择地区运营商，你地区选苏州就是国内服务+地区运营商的活动。</p>
<h4 id="现状"><a href="#现状" class="headerlink" title="现状"></a>现状</h4><p>同样，现在各省份地区运营商为了新用户会推出一些优惠套餐，一般是星卡主套餐29元 + 叠加包（叠加包内容一般是赠送）。例如我的副卡流量卡2，是被地区运营商叫做阳光卡，套餐我在上面也有做介绍，这种流量卡是正规运营商的SIM卡，在对应的运营商App中能够查询套餐信息，任意平台都能够充值话费，根本的说，就是一张普普通通的卡。</p>
<h4 id="选购对比"><a href="#选购对比" class="headerlink" title="选购对比"></a>选购对比</h4><p>现在的流量卡又分为三种，一种是短期卡、一种是长期卡、最后一种是短期可续长期但是续约定义比较模糊的，这里的短期和长期不是指SIM的使用期限，而是指套餐的优惠长短。有些套餐是长期优惠，优惠时间可达5年、20年、40年，有些套餐是短期优惠，优惠时间短则6个月，长则2年。下面我会用几个例子做对比。</p>
<h5 id="例子1：移动沧蕾卡"><a href="#例子1：移动沧蕾卡" class="headerlink" title="例子1：移动沧蕾卡"></a>例子1：移动沧蕾卡</h5><ol>
<li><p>月租29元、首月免月租</p>
</li>
<li><p>原套餐：5G通用流量+30G定向流量</p>
</li>
<li><p>优惠详情：任意渠道首充100元可享受优惠活动，叠加每月45G通用流量，优惠期12个月，到期无异议可续。</p>
</li>
<li><p>禁发北京、云南、西藏、黑龙江</p>
</li>
</ol>
<h5 id="例子2：电信沧欢卡"><a href="#例子2：电信沧欢卡" class="headerlink" title="例子2：电信沧欢卡"></a>例子2：电信沧欢卡</h5><ol>
<li><p>月租39元</p>
</li>
<li><p>原套餐：5G通用流量+30G定向流量</p>
</li>
<li><p>激活赠送40元体验金，体验金不可结转（首月免月租）</p>
</li>
<li><p>优惠详情：快递处或指定链接首充50元可享受优惠活动，充50送120，50即刻到账，120分12月返还，每月反10元，每月赠送45G通用流量叠加包，可参加两次。</p>
</li>
<li><p>店铺叠加优惠：参加首充活动后2-13个月月底反10元，第14月再次参加50送120后，第14月反20，15-24月反10元</p>
</li>
<li><p>综上所述，首月免月租，2-13月19元每月，14月19元每月，15-24月19元每月，24月后29元每月。</p>
</li>
<li><p>禁发北京、云南、西藏、黑龙江</p>
</li>
</ol>
<h5 id="例子3：联通长期卡"><a href="#例子3：联通长期卡" class="headerlink" title="例子3：联通长期卡"></a>例子3：联通长期卡</h5><ol>
<li><p>月租39元</p>
</li>
<li><p>原套餐：15G通用流量+30G定向流量</p>
</li>
<li><p>优惠详情：任意渠道首充100元可享受优惠活动，叠加每月200G通用流量，有效期至2029年12月31日，到期自动续约。</p>
</li>
<li><p>禁发北京、云南、西藏、黑龙江</p>
</li>
</ol>
<p>我们逐个分析，首先要确认优惠时长，例子1明显算是短转长，这个“到期无异议可续”是运营商的补充条款，所以含有此种类似描述的，也有概率不可续约。例子2明显是两年短期套餐，这种套餐一般都是优惠比较大，但是两年后就必须注销更换，比较麻烦，否则优惠结束非常不划算。例子3是最容易懂的长期套餐了，这种标识套餐有效期到2029年、2049年、2099年的一般都是长期套餐，这种就无需担忧运营商续约的情况了。这三种套餐我最不推荐例子2的短期套餐，到优惠结束还要注销费时费力。但这个就见仁见智，各自选择了。</p>
<h4 id="购买方式"><a href="#购买方式" class="headerlink" title="购买方式"></a>购买方式</h4><p>由前文我们可以知道，流量卡一般是地区运营商为了拉新推出的优惠套餐，这种拉新一般都存在代理架构，我们在淘宝、拼多多、各大短视频、长视频网站上能够搜索到店铺的一般都是区域运营商下的代理商，代理商也有不同的级别划分，各家代理销售的流量卡都有返利，不同级别的代理返利不同。例如你在这个代理商这里下单了例子3的长期卡，这个卡你拿到后激活并首充，这个代理如果是黑钻V1级别，返利就是120元现金，而你在各个平台上下单有时还要收你一笔钱，这是不是很不可思议，代理能够两头赚就是源于信息差。</p>
<p>一级代理商一般是以公司为代表的平台机构，例如172号卡平台（仅做科普，不含任何链接、推广，不含任何利益相关，请注意辨别官方平台真假），例子3的长期卡就是我在里面摘抄的资料。但是该平台不接受个人的一级代理，我在一个好友那里拿到了他的下级黑钻V1代理，可以直接看到例子3的长期卡的返利是120元，激活现结。</p>
<p>综上所述，如果你真的很想要一张流量卡作为副卡，那么完全可以自己做代理，然后自己办卡，拿自己的返利，100左右的返利相当于3-4个月的月租了，并且如果有首充100赠220的话，相当于白嫖运营商一年还多了。如果你真的很懒，不想这么麻烦，可以按照 选购对比 中的方式找到你想要的套餐，直接在代理商下单即可，一般是免费的，也不排除有黑心商家想两头赚，再收你一笔。</p>
<h2 id="注意事项"><a href="#注意事项" class="headerlink" title="注意事项"></a>注意事项</h2><ol>
<li><p>流量卡套餐优先选择限制较小的，使用期限长的。</p>
</li>
<li><p>广电的流量卡谨慎选择，优先选择移动、联通、电信。</p>
</li>
<li><p>有些卡有注销限制，尽量选择能够在线注销的，尽管都可以停机注销。</p>
</li>
<li><p>下单尽量选择粉丝多的博主或者销量高的店铺，少一点套路，多一点真诚。</p>
</li>
<li><p>激活时要严格按照当时选购时的首充要求，否则可能没有优惠。</p>
</li>
</ol>
</article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" title="头像" alt="头像"></a><div class="post-copyright__author_name">c2hapmll</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://shijie.icu/post/20241010144421.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl('https://shijie.icu/post/20241010144421.html')">2024年10月10日-流量卡选购指南</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="/images/themes/reward/wechat.jpg" target="_blank"><img class="post-qr-code-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/wechat.jpg" alt="微信"/></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="/images/themes/reward/alipay.jpg" target="_blank"><img class="post-qr-code-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/reward/alipay.jpg" alt="支付宝"/></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display: none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://shijie.icu/post/20241010144421.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=2024年10月10日-流量卡选购指南&amp;url=https://shijie.icu/post/20241010144421.html&amp;pic=/images/posts/2024101001/top.jpg" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl() {
  var currentPageUrl = window.location.href;
  var input = document.createElement("input");
  input.setAttribute("value", currentPageUrl);
  document.body.appendChild(input);
  input.select();
  input.setSelectionRange(0, 99999);
  document.execCommand("copy");
  document.body.removeChild(input);
}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://shijie.icu" target="_blank">我的博客</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E6%97%A5%E5%B8%B8/"><span class="tags-punctuation"> <i class="anzhiyufont anzhiyu-icon-tag"></i></span>日常<span class="tagsPageCount">3</span></a><a class="post-meta__box__tags" href="/tags/%E6%8C%87%E5%8D%97/"><span class="tags-punctuation"> <i class="anzhiyufont anzhiyu-icon-tag"></i></span>指南<span class="tagsPageCount">3</span></a></div></div></div><div class="post_share"><div class="social-share" data-image="/images/posts/2025/042201/202504220200_top.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media='all'"/><script src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/js/social-share.min.js" defer="defer"></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/post/20240902145411.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024090201/top.jpg" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">2024年09月02日-大抵只有我被困在那段时光里了</div></div></a></div><div class="next-post pull-right"><a href="/post/20241120092654.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024112001/top.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">2024年11月20日-RetroArch全能游戏模拟器</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size: 1.5rem; margin-right: 4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器"><img class="cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024112001/top.png" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2024-11-20</div><div class="title">2024年11月20日-RetroArch全能游戏模拟器</div></div></a></div><div><a href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇"><img class="cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/042201/202504220200_top.png" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-22</div><div class="title">2025年04月22日 - RetroArch模拟器 - 联机篇</div></div></a></div></div></div><hr/><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)">匿名评论</a><a href="/privacy" style="margin-left: 4px">隐私政策</a></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div></div></div><div class="comment-barrage"></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="card-content"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div><div class="author-info-avatar"><img class="avatar-img" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/144.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/><div class="author-status"><img class="g-status" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="https://bu.dusays.com/2021/03/03/c50ad1cd452e8.png" alt="status"/></div></div><div class="author-info__description"><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">欢迎访问我的博客，这里是我分享<b style="color:#fff">生活</b>的地方，另外还有一些技能和经验的<b style="color:#fff">记录</b>。</div><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">希望可以帮助到你。</div></div><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">c2hapmll</h1><div class="author-info__desc"></div></a><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://steamcommunity.com/profiles/76561199172600200/" target="_blank" title="Steam"><svg class="icon faa-tada" aria-hidden="true"><use xlink:href="#icon-steam"></use></svg></a></div></div></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bullhorn anzhiyu-shake"></i><span>公告</span></div><div class="announcement_content">刚从Halo迁移至Hexo，还在调整中，欢迎访问~ <br/> 本站开放时间为：<b>8:30-23:00</b>，可能会提前关站哦~ <br/> 历史更新请点击<a href="https://shijie.icu/update/"><b>博客更新日志</b></a> <br/></div></div><div class="card-widget card-countdown"><div class="item-headline"><i></i><span></span></div><div class="item-content"><div class="cd-count-left">
  <span class="cd-text">距离</span>
  <span class="cd-name" id="eventName"></span>
  <span class="cd-time" id="daysUntil"></span>
  <span class="cd-date" id="eventDate"></span>
</div>
<div id="countRight" class="cd-count-right"></div>
</div></div><div class="card-widget music-widget"><div class="item-headline"><i></i><span></span></div><div class="item-content"><div class="music-widget">
  <div id="musicStatus" class="music-status"></div>
  <img id="musicCover" class="music-cover" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="" alt="专辑封面">
</div>
<div class="music-info">
  <div id="musicTitle" class="music-title"></div>
  <div id="musicArtist" class="music-artist"></div>
  <div id="musicAlbum" class="music-album"></div>
</div>
</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%BD%93%E5%89%8D%E6%83%85%E5%86%B5"><span class="toc-number">1.</span> <span class="toc-text">当前情况</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%B5%81%E9%87%8F%E5%8D%A1"><span class="toc-number">2.</span> <span class="toc-text">流量卡</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%AE%A4%E8%AF%86%E6%B5%81%E9%87%8F%E5%8D%A1"><span class="toc-number">2.1.</span> <span class="toc-text">认识流量卡</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%8E%86%E5%8F%B2"><span class="toc-number">2.1.1.</span> <span class="toc-text">历史</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%8E%B0%E7%8A%B6"><span class="toc-number">2.1.2.</span> <span class="toc-text">现状</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%80%89%E8%B4%AD%E5%AF%B9%E6%AF%94"><span class="toc-number">2.1.3.</span> <span class="toc-text">选购对比</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BE%8B%E5%AD%901%EF%BC%9A%E7%A7%BB%E5%8A%A8%E6%B2%A7%E8%95%BE%E5%8D%A1"><span class="toc-number">2.1.3.1.</span> <span class="toc-text">例子1：移动沧蕾卡</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BE%8B%E5%AD%902%EF%BC%9A%E7%94%B5%E4%BF%A1%E6%B2%A7%E6%AC%A2%E5%8D%A1"><span class="toc-number">2.1.3.2.</span> <span class="toc-text">例子2：电信沧欢卡</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BE%8B%E5%AD%903%EF%BC%9A%E8%81%94%E9%80%9A%E9%95%BF%E6%9C%9F%E5%8D%A1"><span class="toc-number">2.1.3.3.</span> <span class="toc-text">例子3：联通长期卡</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%B4%AD%E4%B9%B0%E6%96%B9%E5%BC%8F"><span class="toc-number">2.1.4.</span> <span class="toc-text">购买方式</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-number">3.</span> <span class="toc-text">注意事项</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/042201/202504220200_top.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2025年04月22日 - RetroArch模拟器 - 联机篇"/></a><div class="content"><a class="title" href="/post/20250422110000.html" title="2025年04月22日 - RetroArch模拟器 - 联机篇">2025年04月22日 - RetroArch模拟器 - 联机篇</a><time datetime="2025-04-22T03:00:00.000Z" title="发表于 2025-04-22 11:00:00">2025-04-22</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20250215190000.html" title="2025年02月15日-记一次去医院看牙的经历"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2025/021501/top.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2025年02月15日-记一次去医院看牙的经历"/></a><div class="content"><a class="title" href="/post/20250215190000.html" title="2025年02月15日-记一次去医院看牙的经历">2025年02月15日-记一次去医院看牙的经历</a><time datetime="2025-02-15T11:00:00.000Z" title="发表于 2025-02-15 19:00:00">2025-02-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20241203090000.html" title="2024年12月03日-Hexo安知鱼主题魔改记录"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024120301/top.webp" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年12月03日-Hexo安知鱼主题魔改记录"/></a><div class="content"><a class="title" href="/post/20241203090000.html" title="2024年12月03日-Hexo安知鱼主题魔改记录">2024年12月03日-Hexo安知鱼主题魔改记录</a><time datetime="2024-12-03T01:00:00.000Z" title="发表于 2024-12-03 09:00:00">2024-12-03</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024112001/top.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年11月20日-RetroArch全能游戏模拟器"/></a><div class="content"><a class="title" href="/post/20241120092654.html" title="2024年11月20日-RetroArch全能游戏模拟器">2024年11月20日-RetroArch全能游戏模拟器</a><time datetime="2024-11-20T01:26:54.000Z" title="发表于 2024-11-20 09:26:54">2024-11-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南"><img src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/posts/2024101001/top.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="2024年10月10日-流量卡选购指南"/></a><div class="content"><a class="title" href="/post/20241010144421.html" title="2024年10月10日-流量卡选购指南">2024年10月10日-流量卡选购指南</a><time datetime="2024-10-10T06:44:21.000Z" title="发表于 2024-10-10 14:44:21">2024-10-10</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">&copy;2024 - 2025 By <a class="footer-bar-link" href="/" title="c2hapmll" target="_blank">c2hapmll</a></div></div><div id="footer-type-tips"></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index" title="皖ICP备20000482号-2">皖ICP备20000482号-2</a><a class="footer-bar-link" target="_blank" rel="noopener" href="https://beian.mps.gov.cn/#/query/webSearch" title="皖公网安备 34120402000423号">皖公网安备 34120402000423号</a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">6</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">9</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">3</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" target="_blank" rel="noopener" href="https://home.shijie.icu/" title="我的主页"><img class="back-menu-item-icon" src= "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" onerror="this.onerror=null,this.src=&quot;/img/404.jpg&quot;" data-lazy-src="/images/themes/32.png" alt="我的主页"/><span class="back-menu-item-text">我的主页</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size: 0.9em;"></i><span> 隧道</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size: 0.9em;"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size: 0.9em;"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 生活</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size: 0.9em;"></i><span> 即刻</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size: 0.9em;"></i><span> 相册</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/equipment/"><i class="anzhiyufont anzhiyu-icon-keyboard faa-tada" style="font-size: 0.9em;"></i><span> 装备</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/finance/"><i class="anzhiyufont anzhiyu-icon-chart-line faa-tada" style="font-size: 0.9em;"></i><span> 财务</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);"><span> 记录</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/books/"><i class="anzhiyufont anzhiyu-icon-book faa-tada" style="font-size: 0.9em;"></i><span> 阅读</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/musics/"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size: 0.9em;"></i><span> 音乐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/movies/"><i class="anzhiyufont anzhiyu-icon-bilibili faa-tada" style="font-size: 0.9em;"></i><span> 影视</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/games/"><i class="anzhiyufont anzhiyu-icon-dice faa-tada" style="font-size: 0.9em;"></i><span> 游戏</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/attendance/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size: 0.9em;"></i><span> 打卡</span></a></li></ul></div><div class="menus_item"><a class="site-page faa-parent animated-hover" href="/about/"><span> 关于我</span></a></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/%E4%B8%BB%E9%A2%98/" style="font-size: 0.88rem;">主题<sup>1</sup></a><a href="/tags/%E5%8C%BB%E9%99%A2/" style="font-size: 0.88rem;">医院<sup>1</sup></a><a href="/tags/%E6%83%85%E7%BB%AA/" style="font-size: 0.88rem;">情绪<sup>1</sup></a><a href="/tags/%E6%8C%87%E5%8D%97/" style="font-size: 0.88rem;">指南<sup>3</sup></a><a href="/tags/%E6%97%A5%E5%B8%B8/" style="font-size: 0.88rem;">日常<sup>3</sup></a><a href="/tags/%E6%A8%A1%E6%8B%9F%E5%99%A8/" style="font-size: 0.88rem;">模拟器<sup>2</sup></a><a href="/tags/%E6%B8%B8%E6%88%8F/" style="font-size: 0.88rem;">游戏<sup>2</sup></a><a href="/tags/%E7%89%99%E7%A7%91/" style="font-size: 0.88rem;">牙科<sup>1</sup></a><a href="/tags/%E9%AD%94%E6%94%B9/" style="font-size: 0.88rem;">魔改<sup>1</sup></a></div></div><hr/></div></div><div id="rightside"><div id="rightside-config-hide"><button id="readmode" type="button" title="阅读模式"><i class="anzhiyufont anzhiyu-icon-book-open"></i></button><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><a id="to_comment" href="#post-comment" title="直达评论"><i class="anzhiyufont anzhiyu-icon-comments"></i></a><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><span id="loading-status"></span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="is-center" id="loading-database"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-pulse-icon"></i><span>  数据库加载中</span></div><div class="search-wrap"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div><hr/><div id="local-search-results"></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size: 1rem;"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://y.qq.com/n/ryqq/playlist/8802438608&quot;, &quot;_blank&quot;);" style="display: none;"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://cdn.cbd.int/instant.page@5.2.0/instantpage.js" type="module"></script><script src="https://cdn.cbd.int/vanilla-lazyload@17.8.5/dist/lazyload.iife.min.js"></script><script src="https://cdn.cbd.int/node-snackbar@0.1.16/dist/snackbar.min.js"></script><canvas id="universe"></canvas><script async src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script async src="/anzhiyu/random.js"></script><script src="/js/search/local-search.js"></script><div class="js-pjax"><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
      region: '',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://fastidious-malabi-20e512.netlify.app/.netlify/functions/twikoo',
        region: '',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script>var visitorMail = "";
</script><script async data-pjax src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script async src="https://at.alicdn.com/t/c/font_4729035_3m5int5al8c.js"></script><script src="/js/countdown.js"></script><script src="/js/getnowplaying.js"></script><script src="/js/badge.js"></script><script src="/js/update.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/aplayer/APlayer.min.css" media="print" onload="this.media='all'"><script src="https://cdn.cbd.int/anzhiyu-blog-static@1.0.1/js/APlayer.min.js"></script><script src="https://cdn.cbd.int/hexo-anzhiyu-music@1.0.1/assets/js/Meting2.min.js"></script><script src="https://cdn.cbd.int/pjax@0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: false,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script><script charset="UTF-8" src="https://cdn.cbd.int/anzhiyu-theme-static@1.1.5/accesskey/accesskey.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div></body></html>
const NowPlayingMusic = (() => {
    const config = {
        apiKey: '0c3c7200137807bda2042d589a05863d', // 替换为您的 Last.fm API 密钥
        username: 'c2hapmll' // 替换为您的 Last.fm 用户名
    };

    function fetchNowPlaying() {
        fetch(`https://ws.audioscrobbler.com/2.0/?method=user.getrecenttracks&user=${config.username}&api_key=${config.apiKey}&format=json`)
            .then(response => response.json())
            .then(data => {
                const track = data.recenttracks.track[0];
                if (track['@attr'] && track['@attr'].nowplaying) {
                    const songName = track.name;
                    updateSidebar(`🎸正在听 ~ ${songName}`);
                } else {
                    updateSidebar('当前不在听歌');
                }
            })
            .catch(error => {
                console.error('获取当前播放音乐失败：', error);
                updateSidebar('获取失败');
            });
    }

    function updateSidebar(status) {
        const elements = ['musicStatus']
            .map(id => document.getElementById(id));

        if (elements.some(el => !el)) return;

        const [musicStatus] = elements;

        musicStatus.textContent = status;
    }

    function injectStyles() {
        const styles = `
            .music-widget {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 40px;
            }
            #musicStatus {
                font-size: 16px;
                font-weight: bold;
                text-align: center;  /* 确保文本居中 */
                color: #000; /* 文字颜色 */
            }
        `;

        const styleSheet = document.createElement("style");
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    let timer;
    const start = () => {
        injectStyles();
        fetchNowPlaying();
        timer = setInterval(fetchNowPlaying, 180000); // 每 3 分钟更新一次
    };

    ['pjax:complete', 'DOMContentLoaded'].forEach(event => document.addEventListener(event, start));
    document.addEventListener('pjax:send', () => timer && clearInterval(timer));

    return { start, stop: () => timer && clearInterval(timer) };
})();

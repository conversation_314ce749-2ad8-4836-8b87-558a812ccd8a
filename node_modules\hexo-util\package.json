{"name": "hexo-util", "version": "3.3.0", "description": "Utilities for Hexo.", "main": "dist/index", "types": "./dist/index.d.ts", "scripts": {"prepublishOnly": "npm install && npm run clean && npm run build", "build": "tsc -b", "clean": "tsc -b --clean", "preeslint": "npm run clean && npm run build", "eslint": "eslint lib test", "pretest": "npm run clean && npm run build", "test": "mocha --require ts-node/register", "test-cov": "c8 --reporter=lcovonly npm run test", "build:highlight": "node scripts/build_highlight_alias.js", "postinstall": "npm run build:highlight"}, "files": ["dist/**", "scripts/", "highlight_alias.json"], "repository": {"type": "git", "url": "git+https://github.com/hexojs/hexo-util.git"}, "homepage": "https://hexo.io/", "keywords": ["hexo", "util", "utilities"], "author": "<PERSON> <<EMAIL>> (https://zespia.tw)", "maintainers": ["<PERSON><PERSON> <<EMAIL>> (https://abnerchou.me)"], "license": "MIT", "devDependencies": {"@types/chai": "^4.3.11", "@types/cross-spawn": "^6.0.2", "@types/mocha": "^10.0.6", "@types/node": "^18.11.8", "@types/prismjs": "^1.26.0", "@types/rewire": "^2.5.30", "c8": "^9.1.0", "chai": "^4.3.6", "domhandler": "^5.0.3", "eslint": "^8.23.0", "eslint-config-hexo": "^5.0.0", "html-entities": "^2.3.3", "html-tag-validator": "^1.6.0", "mocha": "^10.0.0", "rewire": "^6.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.3"}, "dependencies": {"camel-case": "^4.1.2", "cross-spawn": "^7.0.3", "deepmerge": "^4.2.2", "highlight.js": "^11.6.0", "htmlparser2": "^9.0.0", "prismjs": "^1.29.0", "strip-indent": "^3.0.0"}, "engines": {"node": ">=14"}}
function initFooterAnimal() {
    const footerBar = document.querySelector('#footer-bar');
    if (!footerBar) return;

    // 防止重复插入
    if (document.getElementById('footer-animal')) return;

    const footerAnimal = document.createElement('div');
    footerAnimal.id = 'footer-animal';
    footerAnimal.innerHTML = `
        <img class="animal entered loaded"
            src="https://i1.wp.com/ruom.wuaze.com/i/2024/10/19/473503.webp"
            alt="动物" />
    `;
    footerBar.insertAdjacentElement('beforebegin', footerAnimal);
}

document.addEventListener('DOMContentLoaded', initFooterAnimal);
document.addEventListener('pjax:success', initFooterAnimal);
#bookmark
  if site.data.bookmark
    each i in site.data.bookmark
      .author-content.author-content-item.bookmarkPage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          //- .banner-button-group
          //-   a.banner-button(href=i.buttonLink)
          //-     i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
          //-     span.banner-button-text=i.buttonText
      each item in i.good_bookmark
        .goodbookmark-item
          h2.goodbookmark-title= item.title
          .goodbookmark-item-description= item.description
          .bookmark-item
            .bookmark-item-content
              each iten, indey in item.bookmark_list
                .bookmark-item-content-item(data-index=indey)
                  .bookmark-item-content-item-cover
                    a(href=iten.link target="_blank" class="bookmark-link")
                      img.bookmark-item-content-item-image(
                        data-lazy-src=url_for(iten.image)
                        onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'`
                        alt=iten.name
                        class="bookmark-image"
                      )
                  .bookmark-item-content-item-info
                    .bookmark-item-content-item-name(onclick=`rm.rightmenuCopyText("${iten.name}");anzhiyu.snackbarShow("${_p('已复制书签名：') + " 【" + iten.name + "】"}");` title=iten.name)= iten.name
                    .bookmark-item-content-item-specification= iten.specification
                    .bookmark-item-content-item-description= iten.description
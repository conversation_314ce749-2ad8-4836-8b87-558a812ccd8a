{"name": "hexo-site", "version": "0.0.0", "private": true, "scripts": {"build": "hexo generate", "clean": "hexo clean", "deploy": "hexo deploy", "server": "hexo server"}, "hexo": {"version": "7.3.0"}, "dependencies": {"axios": "^1.9.0", "cheerio": "^1.0.0", "hexo": "^7.3.0", "hexo-blog-encrypt": "^3.1.9", "hexo-douban": "^2.3.6", "hexo-generator-archive": "^2.0.0", "hexo-generator-category": "^2.0.0", "hexo-generator-index": "^4.0.0", "hexo-generator-search": "^2.4.3", "hexo-generator-tag": "^2.0.0", "hexo-renderer-ejs": "^2.0.0", "hexo-renderer-marked": "^6.3.0", "hexo-renderer-pug": "^3.0.0", "hexo-renderer-stylus": "^3.0.1", "hexo-server": "^3.0.0", "hexo-theme-landscape": "^1.0.0", "hexo-wordcount": "^6.0.1", "js-yaml": "^4.1.0"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/cheerio": "^0.22.35"}}
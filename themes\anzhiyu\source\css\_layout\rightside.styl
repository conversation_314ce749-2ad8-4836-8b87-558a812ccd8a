#rightside
  position: fixed
  right: -48px
  bottom: $rightside-bottom
  z-index: 100
  opacity: 0
  transition: all .5s
  +maxWidth768()
    #switch-commentBarrage
      display: none

  i
    font-size: 1rem

  #rightside-config-hide
    height: 0
    opacity: 0
    transition: transform .4s
    transform: translate(45px, 0)

    &.show
      height: auto
      opacity: 1
      transform: translate(0, 0)

    &.status
      height: auto
      opacity: 1

  & > div
    & > button,
    & > a
      display: block
      margin-bottom: 5px
      width: w = 35px
      height: w
      border-radius: 5px
      background-color: var(--btn-bg)
      color: var(--btn-color)
      text-align: center
      font-size: 16px
      line-height: w

      &:hover
        background-color: var(--btn-hover-color)

  #mobile-toc-button
    display: none

    +maxWidth900()
      display: block

  +maxWidth900()
    #hide-aside-btn
      display: none

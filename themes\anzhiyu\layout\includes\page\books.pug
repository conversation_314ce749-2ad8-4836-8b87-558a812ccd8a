#books
  if site.data.books
    each i in site.data.books
      .author-content.author-content-item.booksPage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          .banner-button-group
            a.banner-button(href=i.buttonLink)
              i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
              span.banner-button-text=i.buttonText
      each item in i.good_books
        .goodbooks-item
          h2.goodbooks-title= item.title
          .goodbooks-item-description= item.description
          .books-item
            .books-item-content
              each iten, indey in item.books_list
                .books-item-content-item(data-index=indey)
                  .books-item-content-item-cover
                      a(href=iten.link target="_blank" class="book-link")
                        img.books-item-content-item-image(
                          data-lazy-src=url_for(iten.image)
                          onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'`
                          alt=iten.name
                          class="book-image"
                        )
                  .books-item-content-item-info
                    .books-item-content-item-name(onclick=`rm.rightmenuCopyText("${iten.name}");anzhiyu.snackbarShow("${_p('已复制书名：') + " 【" + iten.name + "】"}");` title=iten.name)= iten.name
                    .books-item-content-item-specification
                      if iten.year
                        span.year= iten.year + " / "
                      if iten.genre
                        span.genre= iten.genre + " / "
                      if iten.rating
                        span.rating= iten.rating
                      if iten.read_date
                        span.read-date= " / " + iten.read_date
                    .books-item-content-item-description= iten.description